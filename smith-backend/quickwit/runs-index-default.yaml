# THIS FILE HAS BEEN GENERATED - DO NOT EDIT MANUALLY
---
version: 0.8
index_id: runs
doc_mapping:
  # Using lenient mode to prevent ingesting unmapped fields - https://quickwit.io/docs/configuration/index-config#mode
  mode: lenient
  store_source: false
  # Follows JSON definition for run in https://docs.smith.langchain.com/reference/data_formats/run_data_format
  # Deprecated fields are omitted
  field_mappings:
    - name: id
      type: text
      tokenizer: raw
      fast: true
    - name: id_sort
      type: u64
      fast: true
    - name: tenant_id
      type: text
      tokenizer: raw
    - name: session_id
      type: text
      tokenizer: raw
    - name: is_root
      type: bool
    - name: name
      type: text
      tokenizer: raw
    - name: start_time
      type: datetime
      fast: true
      input_formats: ["rfc3339", "unix_timestamp", "%Y-%m-%d %H:%M:%S.%f"]
    - name: end_time
      type: datetime
      fast: true
      input_formats: ["rfc3339", "unix_timestamp", "%Y-%m-%d %H:%M:%S.%f"]
    # Quickwit’s default behavior only supports second-level precision for direct equality matches on datetime fields.
    # Internally, a datetime field’s value is stored with nanosecond resolution in the document store and in
    # fast fields, but the inverted index (term dictionary) stores timestamps truncated to seconds.
    # https://quickwit.io/docs/configuration/index-config#:~:text=Internally%20,in%20the%20term%20dictionary
    - name: start_time_micros
      type: u64
      fast: true
    - name: extra
      type: json
      tokenizer: default
      stored: false
      record: basic
    - name: error
      type: text
      tokenizer: default
      record: position
    - name: run_type
      type: text
      tokenizer: raw
    - name: inputs
      type: json
      tokenizer: default
      stored: false
      record: position
    - name: inputs_flat
      type: concatenate
      tokenizer: default
      concatenate_fields: [inputs]
      record: position
    # inputs_preview is stored but not indexed as we don't search on it
    - name: inputs_preview
      type: text
      indexed: false
    - name: outputs
      type: json
      tokenizer: default
      record: position
      stored: false
    - name: outputs_flat
      type: concatenate
      tokenizer: default
      concatenate_fields: [outputs]
      record: position
    # outputs_preview is stored but not indexed as we don't search on it
    - name: outputs_preview
      type: text
      indexed: false
    - name: parent_run_id
      type: text
      tokenizer: raw
    - name: tags
      type: array<text>
      tokenizer: default
    - name: status
      type: text
      tokenizer: raw
    - name: trace_id
      type: text
      tokenizer: raw
    - name: dotted_order
      type: text
      indexed: false
    - name: thread_id
      type: text
      tokenizer: raw
    - name: reference_example_id
      type: text
      indexed: false
  timestamp_field: start_time
  partition_key: hash_mod(tenant_id, 10),hash_mod(session_id, 10)
  tag_fields: ["tenant_id", "session_id"]
  index_field_presence: true
indexing_settings:
  commit_timeout_secs: 30
retention:
  period: 14 days
  schedule: daily

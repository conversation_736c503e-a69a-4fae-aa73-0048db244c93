import argparse
import json
import pprint
import sys

import requests


def delete_by_query(
    index_id, query, tenant_id=None, session_id=None, endpoint="http://localhost:7280/"
):
    """
    Create a delete task in Quickwit to remove documents matching a query.

    Args:
        index_id (str): The ID of the index to delete from
        query (str): The query to match documents for deletion
        tenant_id (str, optional): The tenant ID for multi-tenancy filtering
        session_id (str, optional): The session ID for filtering
        endpoint (str, optional): The Quickwit API endpoint

    Returns:
        dict: The API response as a dictionary
    """
    # Ensure the endpoint has a trailing slash
    if not endpoint.endswith("/"):
        endpoint += "/"

    # Build the API URL
    api_url = f"{endpoint}api/v1/{index_id}/delete-tasks"

    parts = []
    if query:
        parts.append(query)
    # Add tenant_id or session_id if provided
    if tenant_id:
        parts.append(f"tenant_id:{tenant_id}")

    if session_id:
        parts.append(f"session_id:{session_id}")

    # Build the request payload
    payload = {"query": " AND ".join(parts)}

    # Make the API request
    try:
        response = requests.post(api_url, json=payload)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error making request to Quickwit API: {e}", file=sys.stderr)
        if hasattr(e, "response") and e.response is not None:
            try:
                error_details = e.response.json()
                print(
                    f"API error details: {json.dumps(error_details, indent=2)}",
                    file=sys.stderr,
                )
            except ValueError:
                print(f"API error response: {e.response.text}", file=sys.stderr)
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="Delete Quickwit documents by query")
    parser.add_argument("index_id", help="The ID of the index to delete from")
    parser.add_argument(
        "--query", help="The query to match documents for deletion", required=False
    )

    # Optional arguments
    parser.add_argument(
        "--tenant-id", help="The tenant ID for filtering", required=False
    )
    parser.add_argument(
        "--session-id", help="The session ID for filtering", required=False
    )
    parser.add_argument(
        "--endpoint",
        default="http://localhost:7280/",
        help="The Quickwit API endpoint (default: http://localhost:7280/)",
    )

    args = parser.parse_args()

    # Call the API
    result = delete_by_query(
        args.index_id,
        args.query,
        tenant_id=args.tenant_id,
        session_id=args.session_id,
        endpoint=args.endpoint,
    )

    # Print the result in a formatted way
    print("Delete task created successfully!")
    pprint.pprint(result)


if __name__ == "__main__":
    main()

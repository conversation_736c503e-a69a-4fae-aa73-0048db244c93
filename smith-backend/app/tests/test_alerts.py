"""Test correct behavior of the alerts metrics ingestion."""

import asyncio
import datetime
import json
import logging
from collections import Counter
from typing import Any, Awaitable, Callable
from uuid import uuid4

import asyncpg
import pytest
from aiochclient import ChClient
from lc_database import redis

from app import crud, schemas
from app.models.alerts.match import create_run_count_metric, create_run_latency_metric
from app.models.alerts.models import AlertEntity, AlertRule
from app.models.query_lang.parse import parse_as_filter_directive
from app.models.runs.rules_apply import cron_schedule_apply_rules
from app.tests.utils import (
    fresh_tenant_client,
    post_runs,
    random_lower_string,
)

logger = logging.getLogger(__name__)

PARAM_INGEST_ENDPOINT = pytest.mark.parametrize(
    "ingest_endpoint",
    [
        "/runs",
        "/runs/batch",
        "/runs/multipart",
        "/runs/multipart|s3",
    ],
)


@PARAM_INGEST_ENDPOINT
async def test_error_count_metrics_ingestion(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that a run can be created."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        session = await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
        )
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session.id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "error_count",
            "sum",
            15,
            "gte",
            5,
            None,
            None,
            None,
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session.id
        run_id = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)
        serialized = {"name": "AgentExecutor"}
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=1)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_data = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }

        if ingest_endpoint.startswith("/runs/multipart"):
            await post_runs(
                ingest_endpoint,
                http_tenant_one,
                post=[run_data],
            )
        else:
            response = await http_tenant_one.post("/runs", json=run_data)
            assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()

        # check if the alert metric was added to redis with retry logic
        minute_key = end_time.strftime("%Y-%m-%d-%H-%M")
        alert_metrics = None

        # Retry logic for flaky test - check Redis multiple times
        for attempt in range(5):  # Try up to 5 times
            async with redis.aredis_routed_pool(
                str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
            ) as aredis:
                alert_metrics = await aredis.lrange(
                    f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
                )

            if alert_metrics and len(alert_metrics) >= 1:
                break

            if attempt < 4:  # Don't sleep on the last attempt
                await asyncio.sleep(0.5)  # Wait 500ms before retrying

        assert alert_metrics is not None, (
            f"Alert metrics not found after 5 attempts for key smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}"
        )
        assert len(alert_metrics) == 1, (
            f"Expected 1 alert metric, got {len(alert_metrics)}: {alert_metrics}"
        )
        assert alert_metrics[0].decode("utf-8") == "1.0"


@PARAM_INGEST_ENDPOINT
async def test_error_count_pct_metrics_ingestion(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that percentage-based error count metrics are correctly ingested."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        session = await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
        )
        alert_rule_id = uuid4()
        # create alert rule with PCT aggregation
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule_pct",
            "This is a test alert rule for percentage-based metrics",
            str(session.id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "error_count",
            "pct",  # Changed to PCT aggregation
            15,
            "gte",
            50,  # 50% threshold
            None,
            None,
            None,
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session.id
        run_id1 = uuid4()
        run_id2 = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)
        serialized = {"name": "AgentExecutor"}
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=1)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_data1 = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",  # This run has an error
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id1),
            "dotted_order": f"20230505T051324571809Z{run_id1}",
            "trace_id": str(run_id1),
        }

        run_data2 = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": None,  # This run has no error
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id2),
            "dotted_order": f"20230505T051324571809Z{run_id2}",
            "trace_id": str(run_id2),
        }

        if ingest_endpoint.startswith("/runs/multipart"):
            await post_runs(
                ingest_endpoint,
                http_tenant_one,
                post=[run_data1, run_data2],
            )
        else:
            response = await http_tenant_one.post("/runs", json=run_data1)
            assert response.status_code == 202, response.text
            response = await http_tenant_one.post("/runs", json=run_data2)
            assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()

        # check if both numerator and denominator metrics were added to redis
        minute_key = end_time.strftime("%Y-%m-%d-%H-%M")
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            # Check numerator (error count)
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )
            # Check denominator (total count)
            denominator_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}:denominator",
                0,
                -1,
            )

        # Verify numerator (error count)
        assert alert_metrics is not None
        assert len(alert_metrics) == 2
        tup = (
            alert_metrics[0].decode("utf-8"),
            alert_metrics[1].decode("utf-8"),
        )
        assert tup == ("1.0", "0.0") or tup == ("0.0", "1.0")  # One error

        # Verify denominator (total count)
        assert denominator_metrics is not None
        assert len(denominator_metrics) == 2
        assert denominator_metrics[0].decode("utf-8") == "1.0"  # One total run
        assert denominator_metrics[1].decode("utf-8") == "1.0"  # One total run


@PARAM_INGEST_ENDPOINT
async def test_latency_metrics_ingestion(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    """Test that a run can be created."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        session = await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
        )
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session.id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "latency",
            "avg",
            15,
            "gte",
            5,
            None,
            None,
            None,
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session.id
        run_id = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)
        serialized = {"name": "AgentExecutor"}
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=2)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_data = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }

        if ingest_endpoint.startswith("/runs/multipart"):
            await post_runs(
                ingest_endpoint,
                http_tenant_one,
                post=[run_data],
            )
        else:
            response = await http_tenant_one.post("/runs", json=run_data)
            assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()

        # check if the alert metric was added to redis
        minute_key = end_time.strftime("%Y-%m-%d-%H-%M")
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )

        assert alert_metrics is not None
        assert len(alert_metrics) == 1
        assert alert_metrics[0].decode("utf-8") == "2.0"


async def test_feedback_score_metrics_ingestion(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a run can be created."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        ingest_endpoint = "/runs/multipart"
        session = await crud.create_tracer_session(
            auth_tenant_one,
            schemas.TracerSessionCreate(trace_tier=schemas.TraceTier.longlived),
        )
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session.id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "feedback_score",
            "avg",
            15,
            "lte",
            5,
            None,
            None,
            "yolo",
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session.id
        run_id = uuid4()
        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)
        serialized = {"name": "AgentExecutor"}
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=2)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_data = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id),
            "dotted_order": f"20230505T051324571809Z{run_id}",
            "trace_id": str(run_id),
        }

        feedback = schemas.FeedbackCreateSchemaInternal(
            id=uuid4(),
            trace_id=str(run_id),
            run_id=run_id,
            score=0.5,
            key="yolo",
        )

        await post_runs(
            ingest_endpoint,
            http_tenant_one,
            post=[run_data],
            feedback=[feedback.model_dump()],
        )

        await wait_until_task_queue_empty()

        # check if the alert metric was added to redis
        minute_key = feedback.modified_at.strftime("%Y-%m-%d-%H-%M")
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )

        assert alert_metrics is not None
        assert len(alert_metrics) >= 1
        assert alert_metrics[0].decode("utf-8") == "0.5"


async def test_online_llm_evaluator_with_alerts(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        # Setup initial rule with a broken evaluator
        response = await http_tenant_one.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # set up alert rule
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session_id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "feedback_score",
            "avg",
            15,
            "lte",
            5,
            None,
            None,
            "eq(feedback_key, 'toxic')",
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        evaluator = {
            "structured": {
                "prompt": [
                    [
                        "system",
                        "You are an evaluator.",
                    ],
                    [
                        "user",
                        "Evaluate the following:\n[User Query]: {input}\n[Response]: {output}",
                    ],
                ],
                "schema": {
                    "type": "object",
                    "name": "eval",
                    "description": "",
                },
                "model": {
                    "lc": 1,
                    "type": "constructor",
                    "id": [
                        "langchain",
                        "chat_models",
                        "fake",
                        "FakeMessagesListChatModel",
                    ],
                    "kwargs": {
                        "a_great_secret": {
                            "lc": 1,
                            "type": "secret",
                            "id": ["A_GREAT_SECRET"],
                        },
                        "responses": [
                            {
                                "lc": 1,
                                "type": "constructor",
                                "id": ["langchain", "schema", "messages", "AIMessage"],
                                "kwargs": {
                                    "content": "",
                                    "additional_kwargs": {
                                        "tool_calls": [
                                            {
                                                "function": {
                                                    "name": "eval",
                                                    "arguments": json.dumps(
                                                        {
                                                            "toxic": True,
                                                        }
                                                    ),
                                                }
                                            }
                                        ]
                                    },
                                },
                            }
                        ],
                    },
                },
            }
        }

        response = await http_tenant_one.post(
            "/workspaces/current/secrets",
            json=[{"key": "A_GREAT_SECRET", "value": "a great great secret"}],
        )

        assert response.status_code == 200, response.text

        response = await http_tenant_one.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "evaluators": [evaluator],
                "filter": 'eq(run_type, "tool")',
                "display_name": "not_broken_evaluator_rule",
                "sampling_rate": 1,
            },
        )
        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        # Send runs that match the rule
        run_one = uuid4()
        run_two = uuid4()
        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "Search",
                        "start_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "end_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "How many people live in Canada?"},
                        "outputs": {"output": "38 million"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_one),
                        "trace_id": str(run_one),
                        "dotted_order": f"20230505T051324571809Z{run_one}",
                    },
                    {
                        "name": "Search",
                        "start_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "end_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "What's the capital of France?"},
                        "outputs": {"output": "Paris"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_two),
                        "trace_id": str(run_two),
                        "dotted_order": f"20230505T051324571809Z{run_two}",
                    },
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        # Apply the rule
        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Confirm the rule ran by checking the run logs
        response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
        assert response.status_code == 200, response.text
        rule_logs = response.json()
        assert len(rule_logs) > 0
        assert rule_logs[0]["evaluators"]["outcome"] == "success"

        # Confirm that feedback was added to the runs with the proper error form
        for run_id in [run_one, run_two]:
            feedbacks = await ch_client.fetch(
                "SELECT * FROM feedbacks WHERE run_id = {run}",
                params={"run": run_id},
            )
            assert len(feedbacks) == 1, (
                f"Expected 1 feedback for run {run_id}, got {len(feedbacks)}"
            )
            feedback = feedbacks[0]
            assert feedback["feedback_source"] is not None
            source = json.loads(feedback["feedback_source"])
            assert source["type"] == "auto_eval"
            assert source["metadata"]["rule_id"] == rule_id
            assert feedback["key"] == "toxic"
            assert feedback["score"] == 1

        # check alert metrics
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            minute_key = datetime.datetime.now(datetime.timezone.utc).strftime(
                "%Y-%m-%d-%H-%M"
            )
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )
            # also check the previous minute in case of off by one error
            previous_minute_key = (
                datetime.datetime.now(datetime.timezone.utc)
                - datetime.timedelta(minutes=1)
            ).strftime("%Y-%m-%d-%H-%M")
            alert_metrics_prev = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{previous_minute_key}",
                0,
                -1,
            )
            alert_metrics = alert_metrics_prev + alert_metrics

        assert alert_metrics is not None
        assert len(alert_metrics) == 2
        assert alert_metrics[0].decode("utf-8") == "1.0"
        assert alert_metrics[1].decode("utf-8") == "1.0"


async def test_online_code_evaluator_with_alerts(
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    db_asyncpg: asyncpg.Connection,
    ch_client: ChClient,
) -> None:
    """Test that a broken online evaluator is handled correctly."""
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        # Setup initial rule with a broken evaluator
        response = await http_tenant_one.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # set up alert rule
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session_id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "feedback_score",
            "avg",
            15,
            "lte",
            5,
            None,
            None,
            "eq(feedback_key, 'code_evaluator_rule')",
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        evaluator = {
            "code": "Import supported libraries, e.g.:\n# import json\n\ndef perform_eval(run):\n  # run is a Run object\n\n  # Example usages:\n  # equals_str = run['outputs']['my_key'] == 'expected_value'\n\n  # contains_str = 'expected_to_contain' in run['outputs']['my_key']\n\n  # decoded_json = json.loads(run['outputs']['stringified_json_key'])\n  \n  score = 1\n  return { \"feedback_key\": score }"
        }

        response = await http_tenant_one.post(
            "/runs/rules",
            json={
                "session_id": session_id,
                "code_evaluators": [evaluator],
                "filter": 'eq(run_type, "tool")',
                "display_name": "code_evaluator_rule",
                "sampling_rate": 1,
            },
        )

        assert response.status_code == 200, response.text
        rule_id = response.json()["id"]

        # Send runs that match the rule
        run_one = uuid4()
        response = await http_tenant_one.post(
            "/runs/batch",
            json={
                "post": [
                    {
                        "name": "Search",
                        "start_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "end_time": datetime.datetime.now(
                            datetime.timezone.utc
                        ).isoformat(),
                        "extra": {"foo": "bar"},
                        "error": None,
                        "execution_order": 1,
                        "serialized": {"name": "Search"},
                        "inputs": {"input": "How many people live in Canada?"},
                        "outputs": {"output": "38 million"},
                        "session_id": session_id,
                        "parent_run_id": None,
                        "run_type": "tool",
                        "id": str(run_one),
                        "trace_id": str(run_one),
                        "dotted_order": f"20230505T051324571809Z{run_one}",
                    }
                ]
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        # Apply the rule
        await cron_schedule_apply_rules(rule_ids=[rule_id])
        await wait_until_task_queue_empty()

        # Confirm the rule ran by checking the run logs
        response = await http_tenant_one.get(f"/runs/rules/{rule_id}/logs")
        assert response.status_code == 200, response.text
        rule_logs = response.json()
        assert len(rule_logs) > 0
        assert rule_logs[0]["evaluators"]["outcome"] == "success"

        # check alert metrics
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            minute_key = datetime.datetime.now(datetime.timezone.utc).strftime(
                "%Y-%m-%d-%H-%M"
            )
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )
            # also check the previous minute in case of off by one error
            previous_minute_key = (
                datetime.datetime.now(datetime.timezone.utc)
                - datetime.timedelta(minutes=1)
            ).strftime("%Y-%m-%d-%H-%M")
            alert_metrics_prev = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{previous_minute_key}",
                0,
                -1,
            )
            alert_metrics = alert_metrics_prev + alert_metrics

        assert alert_metrics is not None
        assert len(alert_metrics) == 1
        assert alert_metrics[0].decode("utf-8") == "1.0"


@pytest.mark.parametrize(
    "test_case",
    [
        # Basic error status test cases
        {
            "filter": 'and(eq(is_root, true), eq(status, "error"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "status": "error",
                "error": "test error",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(is_root, true), eq(status, "error"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "status": "success",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(is_root, true), eq(status, "error"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": False,
                "status": "error",
                "error": "test error",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Run latency test cases
        {
            "filter": "eq(is_root, true)",
            "expected_metric": 10.0,
            "metric": "run_latency",
            "entity": {
                "is_root": True,
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(seconds=10),
            },
        },
        {
            "filter": "eq(is_root, true)",
            "expected_metric": None,
            "metric": "run_latency",
            "entity": {
                "is_root": False,
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(seconds=10),
            },
        },
        # Name filter test cases
        {
            "filter": 'and(eq(is_root, true), eq(name, "test"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "name": "test",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(is_root, true), eq(name, "test"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "name": "test2",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(is_root, true), eq(name, "test"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": False,
                "name": "test",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Run type test cases
        {
            "filter": 'and(eq(name, "test"), eq(run_type, "tool"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "name": "test",
                "run_type": "tool",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(name, "test"), eq(run_type, "tool"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": False,
                "name": "test",
                "run_type": "llm",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Input/Output/Error test cases
        {
            "filter": 'and(like(error, "%foo%"), like(inputs, "%bar%"), like(outputs, "%baz%"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "error": "this is a foo error",
                "inputs": "this is a bar input",
                "outputs": "this is a baz output",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(like(error, "%foo%"), like(inputs, "%bar%"), like(outputs, "%baz%"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "error": "this is a foo error",
                "inputs": "this is a bing input",
                "outputs": "this is a baz output",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Case insensitive error test
        {
            "filter": 'and(like(error, "%FOO%"), eq(run_type, "llm"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "error": "this is a foo error",
                "run_type": "llm",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(like(error, "%FOO%"), eq(run_type, "llm"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "error": "this is a foo error",
                "run_type": "tool",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Latency filter test cases
        {
            "filter": 'and(gte(latency, 5.0), eq(run_type, "llm"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "latency": 5.0,
                "run_type": "llm",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(seconds=5.0),
            },
        },
        {
            "filter": 'and(gte(latency, 5.0), eq(run_type, "llm"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "latency": 4.0,
                "run_type": "llm",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc)
                + datetime.timedelta(seconds=4.0),
            },
        },
        # Input key test cases
        {
            "filter": 'and(eq(input_key, "test"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "name": "bar",
                "input_key": {"test": "foo"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(input_key, "test"))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "name": "bar",
                "input_key": {"not_test": "bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Tags test cases
        {
            "filter": 'and(like(tag, "test"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "tag": ["abcdtestabcd"],
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(notlike(tag, "test"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "tag": ["hello", "world"],
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Output key test cases
        {
            "filter": 'and(eq(output_key, "test"), eq(is_root, true))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "output_key": {"test": "foo"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(output_key, "test"), eq(is_root, true))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "output_key": {"not_test": "bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Metadata key test cases
        {
            "filter": 'and(neq(metadata_key, "test"), eq(is_root, true))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "metadata_key": {"test": "foo"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(neq(metadata_key, "test"), eq(is_root, true))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "metadata_key": {"not_test": "bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Additional key-value attribute test cases
        {
            "filter": 'and(eq(input_key, "foo"), eq(input_value, "bar"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"foo": "bar", "not_foo": "not_bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(input_key, "foo"), neq(input_value, "bar"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"foo": "not_bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(input_key, "foo"), like(input_value, "bar"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"foo": "abcbarabc"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(input_key, "foo"), notlike(input_value, "bar"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"foo": "baz", "bing": "bong"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        # Complex filter test cases
        {
            "filter": 'and(and(neq(input_key, "foo"), eq(input_value, "bar")), eq(run_type, "tool"), and(eq(metadata_key, "model"), neq(metadata_value, "openai")))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"foo": "bar", "not_foo": "not_bar"},
                "metadata_key": {"model": "anthropic"},
                "run_type": "tool",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(and(neq(input_key, "foo"), eq(input_value, "bar")), eq(run_type, "tool"), and(eq(metadata_key, "model"), neq(metadata_value, "openai")))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"not_foo": "bar"},
                "metadata_key": {"model": "openai"},
                "run_type": "tool",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(and(neq(input_key, "foo"), eq(input_value, "bar")), eq(run_type, "tool"), and(eq(metadata_key, "model"), neq(metadata_value, "openai")))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"not_foo": "bar"},
                "metadata_key": {"model": "anthropic"},
                "run_type": "tool",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(and(neq(input_key, "foo"), eq(input_value, "bar")), eq(run_type, "tool"), and(eq(metadata_key, "model"), neq(metadata_value, "openai")))',
            "expected_metric": None,
            "metric": "run_count",
            "entity": {
                "is_root": True,
                "input_key": {"not_foo": "bar"},
                "metadata_key": {"model": "anthropic"},
                "run_type": "llm",
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(name, "foo"), eq(input_value, "bar"))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "name": "foo",
                "input_key": {"baz": "bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
        {
            "filter": 'and(eq(name, "foo"), and(eq(input_value, "bar"), eq(input_key, "baz")))',
            "expected_metric": 1,
            "metric": "run_count",
            "entity": {
                "name": "foo",
                "input_key": {"baz": "bar"},
                "start_time": datetime.datetime.now(datetime.timezone.utc),
                "end_time": datetime.datetime.now(datetime.timezone.utc),
            },
        },
    ],
)
async def test_filters(test_case: dict[str, Any]):
    """Test alert rule filters with various configurations."""
    alert_rule_id = uuid4()
    alert_entity_id = uuid4()
    session_id = uuid4()

    alert_rule = AlertRule(
        id=alert_rule_id,
        session_id=session_id,
        attribute=test_case["metric"],
        aggregation="sum",
        window_minutes=15,
        filter=test_case["filter"],
        filter_directive=parse_as_filter_directive(test_case["filter"]),
    )

    alert_entity = AlertEntity(
        id=alert_entity_id,
        session_id=session_id,
        **test_case["entity"],
    )

    if test_case["metric"] == "run_count":
        metric = create_run_count_metric(alert_entity, alert_rule)
    elif test_case["metric"] == "run_latency":
        metric = create_run_latency_metric(alert_entity, alert_rule)
    else:
        raise ValueError(f"Invalid metric: {test_case['metric']}")

    if test_case["expected_metric"] is None:
        assert metric is None
    else:
        assert metric is not None
        if test_case["metric"] == "run_latency":
            assert test_case["expected_metric"] == pytest.approx(metric.metric_value)
        else:
            assert metric.metric_value == test_case["expected_metric"]


@PARAM_INGEST_ENDPOINT
async def test_pct_complex_filter_runs(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    # Setup initial rule with a broken evaluator
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        response = await http_tenant_one.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # set up alert rule
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session_id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "run_count",
            "pct",
            15,
            "lte",
            5,
            None,
            None,
            "and(eq(run_type, 'chain'), eq(input_key, 'input'), like(inputs, '%canada%'))",
            None,
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session_id

        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)
        serialized = {"name": "AgentExecutor"}
        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=1)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_id_one = uuid4()
        run_data_one = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_one),
            "dotted_order": f"20230505T051324571809Z{run_id_one}",
            "trace_id": str(run_id_one),
        }

        run_id_two = uuid4()
        run_data_two = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_two),
            "dotted_order": f"20230505T051324571809Z{run_id_two}",
            "trace_id": str(run_id_two),
        }

        run_id_three = uuid4()
        run_data_three = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "tool",
            "id": str(run_id_three),
            "dotted_order": f"20230505T051324571809Z{run_id_three}",
            "trace_id": str(run_id_three),
        }

        if ingest_endpoint.startswith("/runs/multipart"):
            await post_runs(
                ingest_endpoint,
                http_tenant_one,
                post=[run_data_one, run_data_two, run_data_three],
            )
        else:
            response = await http_tenant_one.post("/runs", json=run_data_one)
            assert response.status_code == 202, response.text
            response = await http_tenant_one.post("/runs", json=run_data_two)
            assert response.status_code == 202, response.text
            response = await http_tenant_one.post("/runs", json=run_data_three)
            assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()

        # check if the alert metric was added to redis
        minute_key = end_time.strftime("%Y-%m-%d-%H-%M")
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )
            alert_denominator_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}:denominator",
                0,
                -1,
            )

        assert alert_metrics is not None
        # since only two of the runs match the filter, only two metrics should be added
        assert len(alert_metrics) == 3
        assert Counter(
            [
                alert_metrics[0].decode("utf-8"),
                alert_metrics[1].decode("utf-8"),
                alert_metrics[2].decode("utf-8"),
            ]
        ) == Counter(["1.0", "1.0", "0.0"])

        assert alert_denominator_metrics is not None
        assert len(alert_denominator_metrics) == 3
        assert alert_denominator_metrics[0].decode("utf-8") == "1.0"
        assert alert_denominator_metrics[1].decode("utf-8") == "1.0"
        assert alert_denominator_metrics[2].decode("utf-8") == "1.0"


@PARAM_INGEST_ENDPOINT
async def test_pct_complex_filter_runs_with_denominator_filter(
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    ingest_endpoint: str,
) -> None:
    # Setup initial rule with a broken evaluator
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client
        auth_tenant_one = authed_client.auth

        response = await http_tenant_one.post(
            "/sessions",
            json={"name": random_lower_string(), "trace_tier": "shortlived"},
        )
        assert response.status_code == 200, response.text
        session_id = response.json()["id"]

        # set up alert rule
        alert_rule_id = uuid4()
        # create alert rule
        await db_asyncpg.execute(
            """
            INSERT INTO alert_rules (
                id,
                name,
                description,
                session_id,
                tenant_id,
                type,
                attribute,
                aggregation,
                window_minutes,
                operator,
                threshold,
                threshold_window_minutes,
                threshold_multiplier,
                filter,
                denominator_filter,
                created_at,
                updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17)
            """,
            alert_rule_id,
            "test_alert_rule",
            "This is a test alert rule",
            str(session_id),
            str(auth_tenant_one.tenant_id),
            "threshold",
            "run_count",
            "pct",
            15,
            "lte",
            5,
            None,
            None,
            "and(eq(run_type, 'chain'), eq(input_key, 'input'), like(inputs, '%canada%'))",
            "and(eq(run_type, 'tool'))",
            datetime.datetime.now(),
            datetime.datetime.now(),
        )

        tenant_one_tracer_session_id = session_id

        inputs = {"input": "How many people live in canada as of 2023?"}
        outputs = {"output": "39,566,248 people"}
        events = [
            {"name": "start", "time": "2024-09-17T18:17:17.361668+00:00"},
            {"name": "end", "time": "2024-09-17T18:17:17.361909+00:00"},
        ] * (1000 if "s3" in ingest_endpoint else 1)

        extra = {"foo": "bar", "metadata": {"conversation_id": "112233"}}
        serialized = {"name": "AgentExecutor"}

        start_time = datetime.datetime.now().replace(second=0, microsecond=0)
        end_time = start_time + datetime.timedelta(seconds=1)

        start_time_str = start_time.isoformat(timespec="microseconds")
        end_time_str = end_time.isoformat(timespec="microseconds")

        run_id_one = uuid4()
        run_data_one = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_one),
            "dotted_order": f"20230505T051324571809Z{run_id_one}",
            "trace_id": str(run_id_one),
        }

        run_id_two = uuid4()
        run_data_two = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "error": "an error",
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "chain",
            "id": str(run_id_two),
            "dotted_order": f"20230505T051324571809Z{run_id_two}",
            "trace_id": str(run_id_two),
        }

        run_id_three = uuid4()
        run_data_three = {
            "name": "LLM",
            "start_time": start_time_str,
            "end_time": end_time_str,
            "extra": extra,
            "execution_order": 1,
            "serialized": serialized,
            "inputs": inputs,
            "outputs": outputs,
            "events": events,
            "session_id": str(tenant_one_tracer_session_id),
            "parent_run_id": None,
            "run_type": "tool",
            "id": str(run_id_three),
            "dotted_order": f"20230505T051324571809Z{run_id_three}",
            "trace_id": str(run_id_three),
        }

        if ingest_endpoint.startswith("/runs/multipart"):
            await post_runs(
                ingest_endpoint,
                http_tenant_one,
                post=[run_data_one, run_data_two, run_data_three],
            )
        else:
            response = await http_tenant_one.post("/runs", json=run_data_one)
            assert response.status_code == 202, response.text
            response = await http_tenant_one.post("/runs", json=run_data_two)
            assert response.status_code == 202, response.text
            response = await http_tenant_one.post("/runs", json=run_data_three)
            assert response.status_code == 202, response.text

        await wait_until_task_queue_empty()

        # check if the alert metric was added to redis
        minute_key = end_time.strftime("%Y-%m-%d-%H-%M")
        async with redis.aredis_routed_pool(
            str(auth_tenant_one.tenant_id), redis.RedisOperation.ALERTS
        ) as aredis:
            alert_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}", 0, -1
            )
            alert_denominator_metrics = await aredis.lrange(
                f"smith:alerts:metrics:{str(alert_rule_id)}:{minute_key}:denominator",
                0,
                -1,
            )

        assert alert_metrics is not None
        assert len(alert_metrics) == 3
        assert Counter(
            [
                alert_metrics[0].decode("utf-8"),
                alert_metrics[1].decode("utf-8"),
                alert_metrics[2].decode("utf-8"),
            ]
        ) == Counter(["1.0", "1.0", "0.0"])

        assert alert_denominator_metrics is not None
        assert len(alert_denominator_metrics) == 3
        assert Counter(
            [
                alert_denominator_metrics[0].decode("utf-8"),
                alert_denominator_metrics[1].decode("utf-8"),
                alert_denominator_metrics[2].decode("utf-8"),
            ]
        ) == Counter(["0.0", "0.0", "1.0"])


@pytest.mark.parametrize(
    "filter",
    [
        "",
        "invalid",
        "and(invalid)",
        'and(invalid, invalid)and(eq(invalid, "world"))',
        'and(eq(invalid, "world"), eq(name, "hello"))',
    ],
)
async def test_invalid_filters_create_alert_rule(
    db_asyncpg: asyncpg.Connection,
    filter: str,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client

        validate_filter_response = await http_tenant_one.post(
            "/internal/alert-filters/validate",
            json={"filter": filter},
        )
        assert validate_filter_response.status_code == 400


@pytest.mark.parametrize(
    "filter",
    [
        'and(eq(name, "hello"))',
        'and(eq(name, "hello"), eq(name, "world"))',
        'and(eq(input_key, "bar"), eq(input_value, "baz"))',
        'and(eq(metadata_key, "foo"), and(eq(input_key, "bar"), eq(input_value, "baz")))',
    ],
)
async def test_valid_filters_create_alert_rule(
    db_asyncpg: asyncpg.Connection,
    filter: str,
) -> None:
    async with fresh_tenant_client(db_asyncpg, use_api_key=True) as authed_client:
        http_tenant_one = authed_client.client

        validate_filter_response = await http_tenant_one.post(
            "/internal/alert-filters/validate",
            json={"filter": filter},
        )
        assert validate_filter_response.status_code == 200

from datetime import datetime
from unittest.mock import AsyncMock, Mock, patch

import pytest
from saq import Job

from app.workers.jobs.clickhouse_autoscaler import (
    autoscale_clickhouse_cluster,
    autoscale_clickhouse_nodes,
)


@pytest.fixture
def mock_job_context():
    """Mock job context with required job object."""
    mock_job = Mock(spec=Job)
    mock_job.id = "test-job-id"
    mock_job.function = "autoscale_clickhouse_cluster"
    return {"job": mock_job}


@pytest.fixture
def mock_cluster_config():
    """Mock cluster configuration."""
    return {
        "test_cluster": {
            "enabled": True,
            "client": "INGESTION",
            "org_id": "test-org-id",
            "service_id": "test-service-id",
            "replica_limits": {"min": 1, "max": 5},
            "scaling_config": {
                "cpu_up": 70,
                "mem_up": 80,
                "cpu_down": 20,
                "mem_down": 30,
                "dry_run": False,
                "force": False,
                "stabilization_minutes": 5,
            },
        }
    }


@pytest.fixture
def mock_service_response():
    """Mock service API response."""
    return {
        "result": {
            "id": "test-service-id",
            "name": "test-service",
            "numReplicas": 2,
            "maxReplicaMemoryGb": 8,
        }
    }


@pytest.fixture
def mock_high_utilization_metrics():
    """Mock ClickHouse metrics with high utilization."""
    return [
        {"metric": "lag60_cpu_utilization_pct", "value": 75.0},
        {"metric": "lag60_memory_utilization_pct", "value": 85.0},
        {"metric": "lag60_total_allocated_memory", "value": 16.0},
    ]


@pytest.fixture
def mock_low_utilization_metrics():
    """Mock ClickHouse metrics with low utilization."""
    return [
        {"metric": "lag60_cpu_utilization_pct", "value": 15.0},
        {"metric": "lag60_memory_utilization_pct", "value": 25.0},
        {"metric": "lag60_total_allocated_memory", "value": 16.0},
    ]


@patch("app.workers.jobs.clickhouse_autoscaler.settings.CLICKHOUSE_CLUSTERS")
@patch("app.workers.jobs.clickhouse_autoscaler.autoscale_clickhouse_cluster")
async def test_autoscale_clickhouse_nodes_enabled_clusters(
    mock_autoscale_clickhouse_cluster,
    mock_clickhouse_clusters,
    mock_cluster_config,
    mock_job_context,
):
    """Test that autoscale_clickhouse_nodes processes enabled clusters."""
    mock_clickhouse_clusters.__getitem__.side_effect = mock_cluster_config.__getitem__
    mock_clickhouse_clusters.items.return_value = mock_cluster_config.items()
    mock_autoscale_clickhouse_cluster.return_value = None

    await autoscale_clickhouse_nodes(mock_job_context)

    mock_autoscale_clickhouse_cluster.assert_called_once_with(
        mock_job_context, "test_cluster", mock_cluster_config["test_cluster"]
    )


@patch("app.workers.jobs.clickhouse_autoscaler.settings.CLICKHOUSE_CLUSTERS")
@patch("app.workers.jobs.clickhouse_autoscaler.autoscale_clickhouse_cluster")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_nodes_disabled_clusters(
    mock_logger,
    mock_autoscale_clickhouse_cluster,
    mock_clickhouse_clusters,
    mock_job_context,
):
    """Test that autoscale_clickhouse_nodes skips disabled clusters."""
    disabled_config = {
        "disabled_cluster": {
            "enabled": False,
            "client": "INGESTION",
            "org_id": "test-org-id",
            "service_id": "test-service-id",
        }
    }
    mock_clickhouse_clusters.__getitem__.side_effect = disabled_config.__getitem__
    mock_clickhouse_clusters.items.return_value = disabled_config.items()

    await autoscale_clickhouse_nodes(mock_job_context)

    mock_autoscale_clickhouse_cluster.assert_not_called()
    mock_logger.ainfo.assert_called_with(
        "Skipping disabled cluster", cluster="disabled_cluster"
    )


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_scale_up(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_cluster_config,
    mock_service_response,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test scaling up when CPU/memory utilization is high."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock HTTP client responses
    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = mock_service_response

    mock_patch_response = Mock()
    mock_patch_response.is_success = True
    mock_patch_response.raise_for_status.return_value = None

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_client.patch.return_value = mock_patch_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.set = AsyncMock(return_value=None)

    cluster_name = "test_cluster"
    config = mock_cluster_config["test_cluster"]

    await autoscale_clickhouse_cluster(mock_job_context, cluster_name, config)

    # Verify service API was called
    expected_service_url = "https://api.clickhouse.cloud/v1/organizations/test-org-id/services/test-service-id"
    mock_client.get.assert_called_once_with(expected_service_url)

    # Verify scaling API was called
    expected_scaling_url = f"{expected_service_url}/scaling"
    mock_client.patch.assert_called_once_with(
        expected_scaling_url, json={"numReplicas": 3}
    )

    # Verify log was called with scale-up decision
    scale_up_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_successful_decision"
        ),
        None,
    )
    assert scale_up_log_call is not None

    # Test all the monitoring metadata
    assert scale_up_log_call.kwargs["decision"] == "scale-up"
    assert scale_up_log_call.kwargs["new_replicas"] == 3
    assert scale_up_log_call.kwargs["prev_replicas"] == 2
    assert scale_up_log_call.kwargs["service_id"] == "test-service-id"
    assert scale_up_log_call.kwargs["service_name"] == "test-service"
    assert scale_up_log_call.kwargs["current_replicas"] == 2
    assert scale_up_log_call.kwargs["max_replicas"] == 5
    assert scale_up_log_call.kwargs["min_replicas"] == 1
    assert scale_up_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert scale_up_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert scale_up_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert scale_up_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert scale_up_log_call.kwargs["cpu_util_pct"] == 75.0
    assert scale_up_log_call.kwargs["mem_util_pct"] == 85.0
    assert scale_up_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert scale_up_log_call.kwargs["memory_limit_gb"] == 16.0  # 2 replicas * 8GB
    assert scale_up_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0  # 16/16 * 100
    assert "high CPU" in scale_up_log_call.kwargs["reason"]
    assert "memory" in scale_up_log_call.kwargs["reason"]


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_scale_down(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_cluster_config,
    mock_service_response,
    mock_low_utilization_metrics,
    mock_job_context,
):
    """Test scaling down when CPU/memory utilization is low."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock HTTP client responses
    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = mock_service_response

    mock_patch_response = Mock()
    mock_patch_response.is_success = True
    mock_patch_response.raise_for_status.return_value = None

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_client.patch.return_value = mock_patch_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_low_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.set = AsyncMock(return_value=None)

    cluster_name = "test_cluster"
    config = mock_cluster_config["test_cluster"]

    await autoscale_clickhouse_cluster(mock_job_context, cluster_name, config)

    # Verify scaling API was called to scale down
    expected_service_url = "https://api.clickhouse.cloud/v1/organizations/test-org-id/services/test-service-id"
    expected_scaling_url = f"{expected_service_url}/scaling"
    mock_client.patch.assert_called_once_with(
        expected_scaling_url, json={"numReplicas": 1}
    )

    # Verify log was called with scale-down decision
    scale_down_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_successful_decision"
        ),
        None,
    )
    assert scale_down_log_call is not None

    # Test all the monitoring metadata for scale-down
    assert scale_down_log_call.kwargs["decision"] == "scale-down"
    assert scale_down_log_call.kwargs["new_replicas"] == 1
    assert scale_down_log_call.kwargs["prev_replicas"] == 2
    assert scale_down_log_call.kwargs["service_id"] == "test-service-id"
    assert scale_down_log_call.kwargs["max_replicas"] == 5
    assert scale_down_log_call.kwargs["min_replicas"] == 1
    assert scale_down_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert scale_down_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert scale_down_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert scale_down_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert scale_down_log_call.kwargs["cpu_util_pct"] == 15.0
    assert scale_down_log_call.kwargs["mem_util_pct"] == 25.0
    assert scale_down_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert scale_down_log_call.kwargs["memory_limit_gb"] == 16.0
    assert scale_down_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0
    assert "low CPU" in scale_down_log_call.kwargs["reason"]
    assert "memory" in scale_down_log_call.kwargs["reason"]


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_skip_at_max_replicas(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test that scaling up is skipped when already at max replicas."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock service response with max replicas already
    service_at_max = {
        "result": {
            "id": "test-service-id",
            "name": "test-service",
            "numReplicas": 5,  # Already at max
            "maxReplicaMemoryGb": 8,
        }
    }

    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = service_at_max

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client with high utilization
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)

    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {"min": 1, "max": 5},
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": False,
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    await autoscale_clickhouse_cluster(mock_job_context, "test_cluster", config)

    # Verify scaling API was NOT called
    mock_client.patch.assert_not_called()

    # Verify log was called with skip decision
    skip_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_successful_decision"
        ),
        None,
    )
    assert skip_log_call is not None

    # Test skip at max replicas monitoring metadata
    assert skip_log_call.kwargs["decision"] == "skip"
    assert skip_log_call.kwargs["new_replicas"] == 5  # same as current since at max
    assert skip_log_call.kwargs["prev_replicas"] == 5
    assert skip_log_call.kwargs["service_id"] == "test-service-id"
    assert skip_log_call.kwargs["max_replicas"] == 5
    assert skip_log_call.kwargs["min_replicas"] == 1
    assert skip_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert skip_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert skip_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert skip_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert skip_log_call.kwargs["cpu_util_pct"] == 75.0
    assert skip_log_call.kwargs["mem_util_pct"] == 85.0
    assert skip_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert skip_log_call.kwargs["memory_limit_gb"] == 40.0  # 5 replicas * 8GB
    assert skip_log_call.kwargs["memory_util_vs_limit_pct"] == 40.0  # 16/40 * 100
    assert "already at max replicas" in skip_log_call.kwargs["reason"]


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_skip_at_min_replicas(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_low_utilization_metrics,
    mock_job_context,
):
    """Test that scaling down is skipped when already at min replicas."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock service response with min replicas already
    service_at_min = {
        "result": {
            "id": "test-service-id",
            "name": "test-service",
            "numReplicas": 1,  # Already at min
            "maxReplicaMemoryGb": 8,
        }
    }

    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = service_at_min

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client with low utilization
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_low_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)

    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {"min": 1, "max": 5},
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": False,
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    await autoscale_clickhouse_cluster(mock_job_context, "test_cluster", config)

    # Verify scaling API was NOT called
    mock_client.patch.assert_not_called()

    # Verify log was called with skip decision
    skip_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_successful_decision"
        ),
        None,
    )
    assert skip_log_call is not None

    # Test skip at min replicas monitoring metadata
    assert skip_log_call.kwargs["decision"] == "skip"
    assert skip_log_call.kwargs["new_replicas"] == 1  # same as current since at min
    assert skip_log_call.kwargs["prev_replicas"] == 1
    assert skip_log_call.kwargs["service_id"] == "test-service-id"
    assert skip_log_call.kwargs["max_replicas"] == 5
    assert skip_log_call.kwargs["min_replicas"] == 1
    assert skip_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert skip_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert skip_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert skip_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert skip_log_call.kwargs["cpu_util_pct"] == 15.0
    assert skip_log_call.kwargs["mem_util_pct"] == 25.0
    assert skip_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert skip_log_call.kwargs["memory_limit_gb"] == 8.0  # 1 replica * 8GB
    assert skip_log_call.kwargs["memory_util_vs_limit_pct"] == 200.0  # 16/8 * 100
    assert "already at min replicas" in skip_log_call.kwargs["reason"]


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_skip_stabilization_window(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_cluster_config,
    mock_service_response,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test that scaling is skipped when within stabilization window."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock HTTP client responses
    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = mock_service_response

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis with recent timestamp (within stabilization window)
    recent_timestamp = datetime.now().isoformat()
    mock_redis.get = AsyncMock(return_value=recent_timestamp.encode())

    cluster_name = "test_cluster"
    config = mock_cluster_config["test_cluster"]

    await autoscale_clickhouse_cluster(mock_job_context, cluster_name, config)

    # Verify scaling API was NOT called
    mock_client.patch.assert_not_called()

    # Verify log was called with skip decision
    skip_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_successful_decision"
        ),
        None,
    )
    assert skip_log_call is not None

    # Test skip stabilization window monitoring metadata
    assert skip_log_call.kwargs["decision"] == "skip"
    assert skip_log_call.kwargs["new_replicas"] == 2  # would be 3 but skipped
    assert skip_log_call.kwargs["prev_replicas"] == 2
    assert skip_log_call.kwargs["service_id"] == "test-service-id"
    assert skip_log_call.kwargs["max_replicas"] == 5
    assert skip_log_call.kwargs["min_replicas"] == 1
    assert skip_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert skip_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert skip_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert skip_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert skip_log_call.kwargs["cpu_util_pct"] == 75.0
    assert skip_log_call.kwargs["mem_util_pct"] == 85.0
    assert skip_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert skip_log_call.kwargs["memory_limit_gb"] == 16.0  # 2 replicas * 8GB
    assert skip_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0  # 16/16 * 100
    assert skip_log_call.kwargs["reason"] == "within stabilization window"


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_api_error(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_cluster_config,
    mock_service_response,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test handling of API errors during scaling."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock HTTP client responses - service call succeeds
    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = mock_service_response

    # Mock HTTP client responses - scaling call fails
    mock_patch_response = Mock()
    mock_patch_response.is_success = False
    mock_patch_response.text = "API Error"
    mock_patch_response.raise_for_status.side_effect = Exception("HTTP 500")

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_client.patch.return_value = mock_patch_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)

    cluster_name = "test_cluster"
    config = mock_cluster_config["test_cluster"]

    # Expect the function to raise an exception due to API error
    with pytest.raises(Exception, match="HTTP 500"):
        await autoscale_clickhouse_cluster(mock_job_context, cluster_name, config)

    # Verify log was called with failed decision
    failed_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if call.args[0] == "autoscale_clickhouse_cluster_failed_decision"
        ),
        None,
    )
    assert failed_log_call is not None

    # Test failed decision monitoring metadata
    assert failed_log_call.kwargs["decision"] == "scale-up"
    assert failed_log_call.kwargs["new_replicas"] == 3
    assert failed_log_call.kwargs["prev_replicas"] == 2
    assert failed_log_call.kwargs["service_id"] == "test-service-id"
    assert failed_log_call.kwargs["max_replicas"] == 5
    assert failed_log_call.kwargs["min_replicas"] == 1
    assert failed_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert failed_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert failed_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert failed_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert failed_log_call.kwargs["cpu_util_pct"] == 75.0
    assert failed_log_call.kwargs["mem_util_pct"] == 85.0
    assert failed_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert failed_log_call.kwargs["memory_limit_gb"] == 16.0
    assert failed_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0
    assert "clickhouse_response_error: API Error" in failed_log_call.kwargs["reason"]


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_dry_run(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_service_response,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test dry run mode - no actual scaling calls made."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock HTTP client responses
    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = mock_service_response

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)
    mock_redis.set = AsyncMock(return_value=None)

    # Create config with dry_run enabled
    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {"min": 1, "max": 5},
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": True,  # Enable dry run
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    await autoscale_clickhouse_cluster(mock_job_context, "test_cluster", config)

    # Verify scaling API was NOT called in dry run mode
    mock_client.patch.assert_not_called()

    # Verify log contains dry run prefix
    dry_run_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if "[DRY-RUN]" in call.args[0]
        ),
        None,
    )
    assert dry_run_log_call is not None

    # Test dry run monitoring metadata
    assert (
        "[DRY-RUN] autoscale_clickhouse_cluster_successful_decision"
        == dry_run_log_call.args[0]
    )
    assert dry_run_log_call.kwargs["decision"] == "scale-up"
    assert dry_run_log_call.kwargs["new_replicas"] == 3
    assert dry_run_log_call.kwargs["prev_replicas"] == 2
    assert dry_run_log_call.kwargs["service_id"] == "test-service-id"
    assert dry_run_log_call.kwargs["max_replicas"] == 5
    assert dry_run_log_call.kwargs["min_replicas"] == 1
    assert dry_run_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert dry_run_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert dry_run_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert dry_run_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert dry_run_log_call.kwargs["cpu_util_pct"] == 75.0
    assert dry_run_log_call.kwargs["mem_util_pct"] == 85.0
    assert dry_run_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert dry_run_log_call.kwargs["memory_limit_gb"] == 16.0
    assert dry_run_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_min_replicas_higher_than_current_replicas(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_low_utilization_metrics,
    mock_job_context,
):
    """If we change min_replicas to a value lower than current_replicas."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock service response with 2 current replicas
    service_response = {
        "result": {
            "id": "test-service-id",
            "name": "test-service",
            "numReplicas": 2,
            "maxReplicaMemoryGb": 8,
        }
    }

    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = service_response

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client with low utilization (should trigger scale-down logic)
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_low_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)

    # Config with min_replicas = 3, current replicas = 2 (valid production scenario)
    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {
            "min": 3,
            "max": 5,
        },  # min_replicas > current_replicas (valid scenario)
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": True,  # Dry run to avoid actual scaling
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    await autoscale_clickhouse_cluster(mock_job_context, "test_cluster", config)

    # Find the decision log call
    decision_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if "autoscale_clickhouse_cluster_successful_decision" in call.args[0]
        ),
        None,
    )
    assert decision_log_call is not None

    assert decision_log_call.kwargs["decision"] == "scale-up"
    assert decision_log_call.kwargs["new_replicas"] == 3
    assert decision_log_call.kwargs["prev_replicas"] == 2
    assert decision_log_call.kwargs["service_id"] == "test-service-id"
    assert decision_log_call.kwargs["max_replicas"] == 5
    assert decision_log_call.kwargs["min_replicas"] == 3
    assert decision_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert decision_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert decision_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert decision_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert decision_log_call.kwargs["cpu_util_pct"] == 15.0
    assert decision_log_call.kwargs["mem_util_pct"] == 25.0
    assert decision_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert decision_log_call.kwargs["memory_limit_gb"] == 16.0
    assert decision_log_call.kwargs["memory_util_vs_limit_pct"] == 100.0
    assert (
        decision_log_call.kwargs["reason"]
        == "low CPU (15.00%) and memory (25.00%) utilization"
    )


@patch("app.workers.jobs.clickhouse_autoscaler.settings")
@patch("app.workers.jobs.clickhouse_autoscaler.redis.aredis_pool")
@patch("app.workers.jobs.clickhouse_autoscaler.AsyncClient")
@patch("app.workers.jobs.clickhouse_autoscaler.clickhouse_client")
@patch("app.workers.jobs.clickhouse_autoscaler.logger", new_callable=AsyncMock)
async def test_autoscale_clickhouse_cluster_max_replicas_lower_than_current_replicas(
    mock_logger,
    mock_clickhouse_client,
    mock_async_client,
    mock_aredis_pool,
    mock_settings,
    mock_high_utilization_metrics,
    mock_job_context,
):
    """Test scenario where max_replicas < current_replicas should scale down despite high utilization."""
    mock_settings.CLICKHOUSE_API_KEY_ID = "test-key-id"
    mock_settings.CLICKHOUSE_API_KEY_SECRET = "test-key-secret"
    mock_settings.CLICKHOUSE_CLOUD_API_BASE_URL = "https://api.clickhouse.cloud"
    mock_settings.CH_UPGRADE_MAX_EXECUTION_TIME = 300

    # Mock logger
    mock_logger.ainfo = AsyncMock()
    mock_logger.awarning = AsyncMock()

    # Mock Redis lock
    mock_redis = AsyncMock()
    mock_lock = AsyncMock()
    mock_lock.acquire = AsyncMock(return_value=True)
    mock_lock.release = AsyncMock(return_value=None)
    mock_redis.lock = Mock(return_value=mock_lock)
    mock_aredis_pool.return_value.__aenter__.return_value = mock_redis

    # Mock service response with 3 current replicas
    service_response = {
        "result": {
            "id": "test-service-id",
            "name": "test-service",
            "numReplicas": 3,
            "maxReplicaMemoryGb": 8,
        }
    }

    mock_get_response = Mock()
    mock_get_response.raise_for_status.return_value = None
    mock_get_response.json.return_value = service_response

    mock_client = AsyncMock()
    mock_client.get.return_value = mock_get_response
    mock_async_client.return_value.__aenter__.return_value = mock_client

    # Mock ClickHouse client with high utilization (would normally trigger scale-up)
    mock_ch = AsyncMock()
    mock_ch.fetch.return_value = mock_high_utilization_metrics
    mock_clickhouse_client.return_value.__aenter__.return_value = mock_ch

    # Mock Redis for timestamp storage
    mock_redis.get = AsyncMock(return_value=None)

    # Config with max_replicas = 2, current replicas = 3 (max < current)
    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {"min": 1, "max": 2},  # max_replicas < current_replicas
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": True,  # Dry run to avoid actual scaling
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    await autoscale_clickhouse_cluster(mock_job_context, "test_cluster", config)

    # Find the decision log call
    decision_log_call = next(
        (
            call
            for call in mock_logger.ainfo.call_args_list
            if "autoscale_clickhouse_cluster_successful_decision" in call.args[0]
        ),
        None,
    )
    assert decision_log_call is not None

    # Should be a scale-down decision to enforce max replicas limit
    assert decision_log_call.kwargs["decision"] == "scale-down"
    assert decision_log_call.kwargs["new_replicas"] == 2
    assert decision_log_call.kwargs["prev_replicas"] == 3
    assert decision_log_call.kwargs["service_id"] == "test-service-id"
    assert decision_log_call.kwargs["cpu_up_threshold_pct"] == 70
    assert decision_log_call.kwargs["mem_up_threshold_pct"] == 80
    assert decision_log_call.kwargs["cpu_down_threshold_pct"] == 20
    assert decision_log_call.kwargs["mem_down_threshold_pct"] == 30
    assert decision_log_call.kwargs["cpu_util_pct"] == 75.0
    assert decision_log_call.kwargs["mem_util_pct"] == 85.0
    assert decision_log_call.kwargs["allocated_memory_gb"] == 16.0
    assert decision_log_call.kwargs["memory_limit_gb"] == 24.0
    assert decision_log_call.kwargs["memory_util_vs_limit_pct"] == 66.67
    assert (
        decision_log_call.kwargs["reason"]
        == "high CPU (75.00%) or memory (85.00%) utilization"
    )


async def test_missing_replica_limits_raises_error():
    """Test that missing replica limits raises ValueError."""
    config = {
        "client": "INGESTION",
        "org_id": "test-org-id",
        "service_id": "test-service-id",
        "replica_limits": {},  # Missing min/max
        "scaling_config": {
            "cpu_up": 70,
            "mem_up": 80,
            "cpu_down": 20,
            "mem_down": 30,
            "dry_run": False,
            "force": False,
            "stabilization_minutes": 5,
        },
    }

    with pytest.raises(ValueError, match="Missing replica_limits"):
        # This validation happens within autoscale_clickhouse_cluster
        replica_limits = config.get("replica_limits", {})
        if "min" not in replica_limits or "max" not in replica_limits:
            raise ValueError(
                "Missing replica_limits['min'] or ['max'] for cluster test_cluster"
            )

import uuid
from functools import wraps
from typing import Any, Awaitable, Callable, Dict, List, Tuple, cast
from unittest.mock import AsyncMock, Mock, patch
from uuid import UUID, uuid4

import asyncpg
import pytest
from httpx import ASGITransport, AsyncClient

from app import config, schemas
from app.api.auth import AuthInfo, TenantlessAuthInfo
from app.api.auth.schemas import (
    OrganizationPermissions,
    OrganizationRoles,
    Permissions,
)
from app.config import settings
from app.crud import create_tenant
from app.main import app
from app.models.api_keys.crud import create_api_key, list_api_keys
from app.models.identities import seat_txn
from app.models.identities.crud import (
    get_org_members,
    get_tenant_members,
)
from app.models.identities.invites import SSOEmailVerificationPayload
from app.models.identities.seat_txn import SeatChangeOperation, get_seat_count_for_org
from app.models.identities.users import get_provider_users_slim_in_txn, get_user
from app.models.organizations.shared import get_org_config
from app.models.organizations.sso import LOGIN_SLUG_LENGTH
from app.models.tenants.list import list_tenants_for_identity_in_org
from app.schemas import Identity, Role
from app.tests.conftest import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
)
from app.tests.ensure import DecodedUserInfo, ensure_user
from app.tests.models.billing.test_transactions import (
    _list_seat_events_for_org,
)
from app.tests.models.identities.test_crud import (
    _NUM_SEAT_TYPES,
    _assert_reporting_status,
    _assert_valid_transaction_chain_for_org,
)
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    create_test_run,
    fresh_tenant_client,
    jwt_for_user,
    random_lower_string,
)

# org auth is only enabled for supabase for now
_org_auth_enabled = config.settings.AUTH_TYPE in ["supabase"]


async def _create_workspaces(
    client: AsyncClient,
    organization_id: UUID | None,
    num_workspaces: int,
) -> List[schemas.Tenant]:
    """Create multiple workspaces for an organization."""
    workspaces = []
    for i in range(num_workspaces):
        response = await client.post(
            "/workspaces" if organization_id else "/tenants",
            json={
                "display_name": random_lower_string(),
                **(
                    {"organization_id": str(organization_id)} if organization_id else {}
                ),
            },
        )
        assert response.status_code == 200, (
            f"Failed to create workspace for {i}: {response.text}"
        )
        workspaces.append(schemas.Tenant(**response.json()))
    return workspaces


def _setup_supabase_client_mock(
    mock_supabase_context_mgr: Mock, num_providers: int
) -> Tuple[AsyncMock, List[Dict]]:
    mock_supabase_client = AsyncMock()
    mock_supabase_context_mgr.return_value.__aenter__.return_value = (
        mock_supabase_client
    )
    responses = [{"id": uuid.uuid4()} for _ in range(num_providers)]
    mock_supabase_client.create_sso_provider.side_effect = responses
    mock_supabase_client.delete_sso_provider.side_effect = responses
    mock_supabase_client.update_sso_provider.side_effect = responses
    return mock_supabase_client, responses


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_list_orgs_create(
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can create an organization by listing orgs."""
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    user_id = uuid4()

    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    response = await http_no_auth.get("/orgs", headers=auth_headers)
    assert response.status_code == 200
    orgs = response.json()
    assert len(orgs) == 1
    org_id = orgs[0]["id"]
    org_headers = {**auth_headers, "X-Organization-Id": str(org_id)}

    response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
    assert response.status_code == 200
    org_info = schemas.OrganizationInfo(**response.json())
    assert org_info.is_personal is True
    assert org_info.config == config.settings.PERSONAL_ORG_DEFAULT_CONFIG

    response = await http_no_auth.get("/tenants", headers=auth_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 1
    workspace_id = workspaces[0]["id"]

    headers = {
        **org_headers,
        "X-Tenant-Id": str(workspace_id),
    }

    response = await http_no_auth.get("/orgs/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/workspaces/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_list_orgs_skip_create(
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can skip org and workspace creation when listing."""
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    user_id = uuid4()

    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    response = await http_no_auth.get("/orgs?skip_create=true", headers=auth_headers)
    assert response.status_code == 200
    orgs = response.json()
    assert len(orgs) == 0

    response = await http_no_auth.get("/tenants?skip_create=true", headers=auth_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 0


class MockMetronomeClient:
    def __init__(self):
        self.post = AsyncMock()


class MockStripeCustomers:
    def __init__(self):
        self.update_async = AsyncMock()


class MockStripeClient:
    def __init__(self):
        self.customers = MockStripeCustomers()


def mock_billing_clients(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        with (
            patch(
                "app.models.organizations.payment.metronome.metronome_client"
            ) as metronome_client_ctx_mgr_mock_payment,
            patch(
                "app.models.organizations.shared.metronome_client"
            ) as metronome_client_ctx_mgr_mock_shared,
            patch(
                "app.models.organizations.metronome_cache.metronome.metronome_client"
            ) as metronome_client_ctx_mgr_mock_metronome_cache,
            patch(
                "app.models.organizations.payment.stripe.client",
                new_callable=MockStripeClient,
            ),
        ):
            mock_metronome_client = MockMetronomeClient()
            metronome_client_ctx_mgr_mock_payment.return_value.__aenter__.return_value = mock_metronome_client
            metronome_client_ctx_mgr_mock_shared.return_value.__aenter__.return_value = mock_metronome_client
            metronome_client_ctx_mgr_mock_metronome_cache.return_value.__aenter__.return_value = mock_metronome_client
            return await func(*args, **kwargs)

    return wrapper


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
@mock_billing_clients
async def test_update_org_info(
    http_no_auth: AsyncClient,
) -> None:
    """Test that a user can update org info."""

    user_id = uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    # cannot update personal org
    response = await http_no_auth.post(
        "/orgs",
        json={"display_name": "Personal", "is_personal": True},
        headers=auth_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.display_name == "Personal"

    personal_org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

    response = await http_no_auth.patch(
        "/orgs/current/info",
        json={"display_name": "test org 2"},
        headers=personal_org_headers,
    )
    assert response.status_code == 400

    # can update non-personal org
    response = await http_no_auth.post(
        "/orgs",
        json={"display_name": "test org", "is_personal": False},
        headers=auth_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.display_name == "test org"

    org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

    response = await http_no_auth.patch(
        "/orgs/current/info", json={"display_name": "test org 2"}, headers=org_headers
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.display_name == "test org 2"

    response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
    assert response.status_code == 200
    org_info = schemas.OrganizationInfo(**response.json())
    assert org_info.display_name == "test org 2"

    response = await http_no_auth.patch(
        "/orgs/current/info",
        json={"jit_provisioning_enabled": False},
        headers=org_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.jit_provisioning_enabled is False


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_disable_public_sharing(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        client = authed_client.client
        await db_asyncpg.execute(
            "update organizations set config = config || '{\"can_disable_public_sharing\": true}' where id = $1",
            auth.organization_id,
        )

        # Disable then re-enable with no shared resources
        response = await client.patch(
            "/orgs/current/info",
            json={"public_sharing_disabled": True, "unshare_all": True},
        )
        assert response.status_code == 200, response.text
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.public_sharing_disabled is True
        response = await client.patch(
            "/orgs/current/info", json={"public_sharing_disabled": False}
        )
        assert response.status_code == 200, response.text
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.public_sharing_disabled is False

        # Create resources and share
        response = await client.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]
        response = await client.put(f"/datasets/{dataset_id}/share")
        assert response.status_code == 200
        ds_share_token = response.json()["share_token"]

        run_id = uuid4()
        session_name = random_lower_string()
        response = await client.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_name": session_name,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()
        response = await client.put(
            f"/runs/{str(run_id)}/share",
        )
        assert response.status_code == 200
        run_share_token = response.json()["share_token"]

        response = await client.post(
            "/repos/",
            json={
                "repo_handle": random_lower_string(),
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        repo = body["repo"]

        # Disabling should unshare all shared resources
        response = await client.patch(
            "/orgs/current/info",
            json={"public_sharing_disabled": True, "unshare_all": True},
        )
        assert response.status_code == 200, response.text
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.public_sharing_disabled is True
        response = await client.get("/workspaces/current/shared")
        assert response.status_code == 200, response.text
        entities = response.json()["entities"]
        assert len(entities) == 0, "Shared datasets and runs should be unshared"
        response = await client.get(
            f"/repos/{repo['full_name']}",
        )
        assert response.status_code == 200
        assert response.json()["repo"]["is_public"] is False

        # Share tokens should not work
        response = await client.get(f"/public/{ds_share_token}/datasets")
        assert response.status_code == 404, response.text
        response = await client.post(f"/public/{run_share_token}/runs/query", json={})
        assert response.status_code == 404, response.text

        # Once disabled, cannot share resources
        response = await client.post(
            "/repos/",
            json={
                "repo_handle": random_lower_string(),
                "is_public": True,
            },
        )
        assert response.status_code == 403, "Should not be able to create public prompt"
        response = await client.put(f"/datasets/{dataset_id}/share")
        assert response.status_code == 403, "Should not be able to share dataset"
        response = await client.put(
            f"/runs/{str(run_id)}/share",
        )
        assert response.status_code == 403, "Should not be able to share run"
        response = await client.patch(
            f"/repos/{repo['full_name']}",
            json={"is_public": True},
        )
        assert response.status_code == 403, "Should not be able to set prompt to public"

        # Normal private resources should still work
        response = await client.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200

        run_id = uuid4()
        session_name = random_lower_string()
        response = await client.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_name": session_name,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()

        response = await client.post(
            "/repos/",
            json={
                "repo_handle": random_lower_string(),
                "is_public": False,
            },
        )
        assert response.status_code == 200

        # Disabling again should succeed even though no-op
        response = await client.patch(
            "/orgs/current/info",
            json={"public_sharing_disabled": True, "unshare_all": True},
        )
        assert response.status_code == 200, response.text


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_unshare_all(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        # Unsharing all succeeds with no shared resources
        response = await client.patch("/orgs/current/info", json={"unshare_all": True})
        assert response.status_code == 200, response.text
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.public_sharing_disabled is False

        # Create resources and share
        response = await client.post(
            "/datasets",
            json={
                "name": random_lower_string(),
                "description": "test",
            },
        )
        assert response.status_code == 200
        dataset_id = response.json()["id"]
        response = await client.put(f"/datasets/{dataset_id}/share")
        assert response.status_code == 200
        ds_share_token = response.json()["share_token"]

        run_id = uuid4()
        session_name = random_lower_string()
        response = await client.post(
            "/runs",
            json={
                "name": "AgentExecutor",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_name": session_name,
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 202, response.text
        await wait_until_task_queue_empty()
        response = await client.put(
            f"/runs/{str(run_id)}/share",
        )
        assert response.status_code == 200
        run_share_token = response.json()["share_token"]

        response = await client.post(
            "/repos/",
            json={
                "repo_handle": random_lower_string(),
                "is_public": True,
            },
        )
        assert response.status_code == 200
        body = response.json()
        repo = body["repo"]

        # Unshare all without disabling future sharing
        response = await client.patch("/orgs/current/info", json={"unshare_all": True})
        assert response.status_code == 200, response.text
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.public_sharing_disabled is False
        response = await client.get("/workspaces/current/shared")
        assert response.status_code == 200, response.text
        entities = response.json()["entities"]
        assert len(entities) == 0, "Shared datasets and runs should be unshared"
        response = await client.get(
            f"/repos/{repo['full_name']}",
        )
        assert response.status_code == 200
        assert response.json()["repo"]["is_public"] is False

        # Share tokens should not work
        response = await client.get(f"/public/{ds_share_token}/datasets")
        assert response.status_code == 404, response.text
        response = await client.post(f"/public/{run_share_token}/runs/query", json={})
        assert response.status_code == 404, response.text


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_create_personal_org_no_existing(
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can create a personal org without any existing orgs."""
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    user_id = uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    response = await http_no_auth.post(
        "/orgs",
        json={"display_name": "test org", "is_personal": True},
        headers=auth_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.created_by_user_id == user_id
    assert org.is_personal is True
    assert org.disabled is False
    org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

    response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
    assert response.status_code == 200
    org_info = schemas.OrganizationInfo(**response.json())
    assert org_info.is_personal is True
    assert org_info.config == config.settings.PERSONAL_ORG_DEFAULT_CONFIG

    response = await http_no_auth.get("/tenants", headers=auth_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 1
    workspace = schemas.TenantForUser(**workspaces[0])
    assert workspace.organization_id == org.id
    assert workspace.display_name == settings.DEFAULT_WORKSPACE_NAME
    assert workspace.is_personal is True

    headers = {
        **auth_headers,
        "X-Organization-Id": str(org.id),
        "X-Tenant-Id": str(workspace.id),
    }

    response = await http_no_auth.get("/orgs/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/workspaces/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_create_personal_org_with_existing(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can create a personal org with an existing non-personal org."""
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth = authed_client.auth
        client = authed_client.client
        auth_headers = {"Authorization": client.headers["Authorization"]}

        response = await http_no_auth.post(
            "/orgs",
            json={"display_name": "test org", "is_personal": True},
            headers=auth_headers,
        )
        assert response.status_code == 200

        org = schemas.OrganizationPGSchemaSlim(**response.json())
        assert org.created_by_user_id == auth.user_id
        assert org.is_personal
        assert not org.disabled
        org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

        response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
        assert response.status_code == 200
        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.is_personal is True
        assert org_info.config == config.settings.PERSONAL_ORG_DEFAULT_CONFIG

        response = await http_no_auth.get("/workspaces", headers=org_headers)
        assert response.status_code == 200
        workspaces = response.json()
        assert len(workspaces) == 1
        workspace = schemas.TenantForUser(**workspaces[0])
        assert workspace.organization_id == org.id
        assert workspace.display_name == settings.DEFAULT_WORKSPACE_NAME
        assert workspace.is_personal is True

    headers = {
        **org_headers,
        "X-Tenant-Id": str(workspace.id),
    }

    response = await http_no_auth.get("/orgs/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/workspaces/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_create_shared_org(
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can create a shared org with and without existing orgs."""
    if use_api_key:
        pytest.skip("Api keys are not supported for creating an organization")

    user_id = uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"test+{user_id}@langchain.dev",
        user_full_name="",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}

    response = await http_no_auth.post(
        "/orgs",
        json={"display_name": "test org", "is_personal": False},
        headers=auth_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.created_by_user_id == user_id
    assert org.is_personal is False
    assert org.disabled is False
    assert org.jit_provisioning_enabled is True
    org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

    response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
    assert response.status_code == 200
    org_info = schemas.OrganizationInfo(**response.json())
    assert org_info.is_personal is False
    assert org_info.config == config.settings.SHARED_ORG_DEFAULT_CONFIG

    response = await http_no_auth.get("/workspaces", headers=org_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 1
    workspace = schemas.TenantForUser(**workspaces[0])
    assert workspace.organization_id == org.id
    assert workspace.display_name == settings.DEFAULT_WORKSPACE_NAME
    assert workspace.is_personal is False

    headers = {
        **auth_headers,
        "X-Organization-Id": str(org.id),
        "X-Tenant-Id": str(workspace.id),
    }

    response = await http_no_auth.get("/orgs/current/members", headers=org_headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/workspaces/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    # Create another shared org
    response = await http_no_auth.post(
        "/orgs",
        json={"display_name": "test org 2", "is_personal": False},
        headers=auth_headers,
    )
    assert response.status_code == 200
    org = schemas.OrganizationPGSchemaSlim(**response.json())
    assert org.created_by_user_id == user_id
    assert org.is_personal is False
    assert org.disabled is False
    org_headers = {**auth_headers, "X-Organization-Id": str(org.id)}

    response = await http_no_auth.get("/orgs/current/info", headers=org_headers)
    assert response.status_code == 200
    org_info = schemas.OrganizationInfo(**response.json())
    assert org_info.is_personal is False
    assert org_info.config == config.settings.SHARED_ORG_DEFAULT_CONFIG

    response = await http_no_auth.get("/workspaces", headers=org_headers)
    assert response.status_code == 200
    workspaces = response.json()
    assert len(workspaces) == 1
    workspace = schemas.TenantForUser(**workspaces[0])
    assert workspace.organization_id == org.id
    assert workspace.display_name == settings.DEFAULT_WORKSPACE_NAME
    assert workspace.is_personal is False

    headers = {
        **auth_headers,
        "X-Organization-Id": str(org.id),
        "X-Tenant-Id": str(workspace.id),
    }

    response = await http_no_auth.get("/orgs/current/members", headers=org_headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/workspaces/current/members", headers=headers)
    assert response.status_code == 200
    assert len(response.json()["members"]) == 1

    response = await http_no_auth.get("/orgs", headers=auth_headers)
    assert response.status_code == 200
    assert len(response.json()) == 2


@pytest.mark.skipif(
    settings.BASIC_AUTH_ENABLED or settings.AUTH_TYPE == "none",
    reason="cannot create additional orgs in these modes",
)
async def test_create_org_failure_modes(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    if use_api_key:
        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            client = authed_client.client

            response = await client.post(
                "/orgs",
                json={"display_name": "test org", "is_personal": False},
            )
            assert response.status_code == 403, response.text
    else:
        user_id = uuid4()
        jwt = jwt_for_user(
            user_id=user_id,
            user_email=f"test+{user_id}@langchain.dev",
            user_full_name="",
        )
        auth_headers = {"Authorization": f"Bearer {jwt}"}

        # Cannot create a personal org if they are disabled
        prev_value = config.settings.FF_PERSONAL_ORGS_DISABLED
        config.settings.FF_PERSONAL_ORGS_DISABLED = True
        response = await http_no_auth.post(
            "/orgs",
            json={"display_name": "test org", "is_personal": True},
            headers=auth_headers,
        )
        assert response.status_code == 400, response.text
        assert "Personal organizations are disabled" in response.text
        config.settings.FF_PERSONAL_ORGS_DISABLED = prev_value

        # Cannot create another personal org
        async with fresh_tenant_client(
            db_asyncpg, use_api_key, organization_is_personal=True
        ) as authed_client:
            client = authed_client.client
            response = await client.post(
                "/orgs",
                json={"display_name": "test org", "is_personal": True},
            )
            assert response.status_code == 400, response.text
            assert "multiple personal organizations" in response.text


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_invite_admin(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an org-level admin can be invited and has corresponding pending org-level and workspace identities."""
    if use_api_key:
        pytest.skip("Api keys cannot be used for invites")

    new_user_email = "<EMAIL>"

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        auth_other_org = authed_client.auth

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # update max workspaces to 4
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 4}' WHERE id = $1",
            auth.organization_id,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 2)

        org_admin_role_id = await db_asyncpg.fetchval(
            f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
        )

        # create an invite for this user in this org as an org admin
        response = await client.post(
            "/orgs/current/members",
            json={"email": new_user_email, "role_id": str(org_admin_role_id)},
        )
        assert response.status_code == 200
        pending_org_identity = schemas.PendingIdentity(**response.json())

        org_members = await get_org_members(auth)
        assert pending_org_identity.email in [
            member.email for member in org_members.pending
        ], "User should have a pending identity in the organization"

        assert pending_org_identity.access_scope == schemas.AccessScope.organization
        assert pending_org_identity.email == new_user_email
        user = await get_user(new_user_email)
        assert user is None, "User should not exist in users table for other auth types"
        assert pending_org_identity.organization_id == auth.organization_id
        assert pending_org_identity.tenant_id is None

        # response from get_org_members has the role_name, while the response from the invite does not
        pending_identity_full = next(
            m for m in org_members.pending if m.id == pending_org_identity.id
        )
        assert pending_identity_full.role_name == "Organization Admin"

        assert pending_org_identity.password is None, (
            "Response should not have a password"
        )

        # check that the pending Admin identity was created in both workspaces
        for workspace in workspaces:
            tenant_members = await get_tenant_members(
                AuthInfo(
                    tenant_id=workspace.id,
                    organization_id=auth.organization_id,
                    tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
                )
            )
            assert new_user_email in [m.email for m in tenant_members.pending]
            pending_workspace_identity = next(
                m for m in tenant_members.pending if m.email == new_user_email
            )
            assert pending_workspace_identity.role_name == "Admin"
            assert (
                pending_workspace_identity.access_scope == schemas.AccessScope.workspace
            )
            assert pending_workspace_identity.organization_id == auth.organization_id

    # confirm that the other org does not have any identities for the new user
    org_members = await get_org_members(auth_other_org)
    assert new_user_email not in [m.email for m in org_members.pending]
    tenant_members = await get_tenant_members(auth_other_org)
    assert new_user_email not in [m.email for m in tenant_members.pending]


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_invite_user(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that an Org User can be invited"""
    if use_api_key:
        pytest.skip("Api keys cannot be used for invites")

    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.post(
            "/orgs/current/members",
            json={
                "email": "<EMAIL>",
                "role_id": str(org_user_role_id),
            },
        )

        assert response.status_code == 200, response.text
        pending_identity = schemas.PendingIdentity(**response.json())
        org_members = await get_org_members(auth)
        assert pending_identity.email in [
            member.email for member in org_members.pending
        ], "User should have an identity in the organization"

        assert pending_identity.access_scope == schemas.AccessScope.organization

        # response from get_org_members has the role_name, while the response from the invite does not
        pending_identity_full = next(
            m for m in org_members.pending if m.id == pending_identity.id
        )
        assert pending_identity_full.role_name == "Organization User"


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_invite_user_batch(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that users can be invited to a workspace and organization by a Workspace Admin if enabled"""
    if use_api_key:
        pytest.skip("Api keys cannot be used for invites")

    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )
    old_flag = settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED
    config.settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED = True

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        admin_email = "<EMAIL>"
        viewer_email = "<EMAIL>"

        response = await client.post(
            "/workspaces/current/members/batch",
            json=[
                {
                    "email": admin_email,
                    "workspace_role_id": str(workspace_admin_role_id),
                },
                {
                    "email": viewer_email,
                    "workspace_role_id": str(workspace_viewer_role_id),
                },
            ],
        )

        assert response.status_code == 200, response.text
        org_members = await get_org_members(auth)
        assert set([admin_email, viewer_email]) == set(
            [p.email for p in org_members.pending]
        ), "Users should have a pending identity in the organization"

        # response from get_org_members has the role_name, while the response from the invite does not
        assert all(m.role_name == "Organization User" for m in org_members.pending), (
            "All pending users should be org users"
        )

        ws_members = await get_tenant_members(auth)
        assert set([admin_email, viewer_email]) == set(
            [p.email for p in ws_members.pending]
        ), "Users should have a pending identity in the workspace"
        pending_ws_admin_identity = next(
            m for m in ws_members.pending if m.email == admin_email
        )
        assert pending_ws_admin_identity.role_name == "Admin"

        pending_ws_viewer_identity = next(
            m for m in ws_members.pending if m.email == viewer_email
        )
        assert pending_ws_viewer_identity.role_name == "Viewer"

    config.settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED = old_flag


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_list_org_members(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("Api keys are not supported for org auth endpoints")

    # Test without x-organization-id header
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=False
    ) as authed_client:
        client = authed_client.client

        response = await client.get("/orgs/current/members")
        assert response.status_code == 401

    # Test with correct headers
    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/orgs/current/members")
        assert response.status_code == 200
        org_members = schemas.OrganizationMembers(**response.json())
        assert len(org_members.members) == 1  # owner
        assert len(org_members.pending) == 0
        assert org_members.organization_id == auth.organization_id
        member = org_members.members[0]
        assert member.role_name == "Organization Admin"
        assert member.access_scope == schemas.AccessScope.organization
        assert member.organization_id == auth.organization_id
        assert member.tenant_id is None
        assert member.read_only is False
        assert member.user_id == auth.user_id
        assert member.email == auth.user_email
        assert set(member.tenant_ids) == {auth.tenant_id}
        assert set([p.provider_user_id for p in member.linked_login_methods]) == {
            auth.user_id,
        }
        expected_login_methods = await get_provider_users_slim_in_txn(
            db_asyncpg, cast(uuid.UUID, auth.ls_user_id)
        )
        assert len(expected_login_methods) == 1
        expected_login_method = expected_login_methods[0]
        login_method = next(
            p for p in member.linked_login_methods if p.provider_user_id == auth.user_id
        )
        assert expected_login_method.model_dump() == login_method.model_dump()
        assert "hashed_password" not in login_method.model_dump()


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_list_org_members_paginated(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Invite users
        num_invites = 10
        num_to_claim = 5
        invite_payload = [
            {
                "email": f"testlistorgpg{i}@langchain.dev",
            }
            for i in range(num_invites)
        ]
        response = await client.post(
            "/orgs/current/members/batch",
            json=invite_payload,
        )

        response = await client.get("/orgs/current/members")
        assert response.status_code == 200
        org_members = schemas.OrganizationMembers(**response.json())
        assert len(org_members.members) == 1  # owner
        assert len(org_members.pending) == num_invites

        # Check paginated endpoints
        response = await client.get("/orgs/current/members/active")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        active_members = [schemas.OrgMemberIdentity(**i) for i in response.json()]
        assert org_members.members == active_members

        response = await client.get(
            "/orgs/current/members/pending", params={"limit": num_invites}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members = [schemas.OrgPendingIdentity(**i) for i in response.json()]
        assert len(pending_members) == num_invites, "Should return exactly the limit"

        response = await client.get(
            "/orgs/current/members/pending", params={"limit": num_invites // 2}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members1 = [schemas.OrgPendingIdentity(**i) for i in response.json()]
        response = await client.get(
            "/orgs/current/members/pending",
            params={"limit": num_invites // 2, "offset": num_invites // 2},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        pending_members2 = [schemas.OrgPendingIdentity(**i) for i in response.json()]
        assert [p.email for p in pending_members] == [
            p.email for p in pending_members1 + pending_members2
        ]

        response = await client.get(
            "/orgs/current/members/pending", params={"limit": 2}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites)
        assert len(response.json()) == 2, "Should return exactly the limit"

        # Claim half of the invites
        for i in range(num_to_claim):
            payload = invite_payload[i]
            email = payload["email"]
            token = jwt_for_user(user_id=uuid4(), user_email=email, user_full_name="")
            response = await client.post(
                f"/orgs/pending/{auth.organization_id}/claim",
                headers={"Authorization": f"Bearer {token}"},
            )
            assert response.status_code == 200

        # Check endpoints again
        response = await client.get("/orgs/current/members")
        assert response.status_code == 200
        org_members = schemas.OrganizationMembers(**response.json())
        assert len(org_members.members) == num_to_claim + 1
        assert len(org_members.pending) == num_invites - num_to_claim

        # Default pagination params should get all users
        response = await client.get("/orgs/current/members/active")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_to_claim + 1)
        active_members = [schemas.OrgMemberIdentity(**i) for i in response.json()]
        assert org_members.members == active_members

        response = await client.get("/orgs/current/members/pending")
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites - num_to_claim)
        pending_members = [schemas.OrgPendingIdentity(**i) for i in response.json()]
        assert len(pending_members) == num_invites - num_to_claim
        assert pending_members == org_members.pending

        # Get fewer, confirm stable ordering
        limit = 2
        response = await client.get(
            "/orgs/current/members/active", params={"limit": limit}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_to_claim + 1)
        active_members_few = [schemas.OrgMemberIdentity(**i) for i in response.json()]
        assert len(active_members_few) == limit
        assert active_members_few == org_members.members[: len(active_members_few)]

        response = await client.get(
            "/orgs/current/members/pending", params={"limit": limit}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == str(num_invites - num_to_claim)
        pending_members_few = [schemas.OrgPendingIdentity(**i) for i in response.json()]
        assert len(pending_members_few) == limit
        assert pending_members_few == org_members.pending[: len(pending_members_few)]

        # Filter active by email, user_id, and ls_user_id
        response = await client.get(
            "/orgs/current/members/active",
            params={"ls_user_ids": active_members[0].ls_user_id},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.OrgMemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member
        response = await client.get(
            "/orgs/current/members/active", params={"emails": active_members[0].email}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.OrgMemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member
        response = await client.get(
            "/orgs/current/members/active",
            params={"user_ids": active_members[0].user_id},
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        active_member = [schemas.OrgMemberIdentity(**i) for i in response.json()][0]
        assert active_members[0] == active_member

        # Filter pending by email
        response = await client.get(
            "/orgs/current/members/pending", params={"emails": pending_members[0].email}
        )
        assert response.status_code == 200
        assert response.headers["X-Pagination-Total"] == "1"
        assert len(response.json()) == 1
        pending_member = [schemas.OrgPendingIdentity(**i) for i in response.json()][0]
        assert pending_members[0] == pending_member


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_default_org_workspace(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("Api keys are not supported for org auth endpoints")

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/workspaces")
        assert response.status_code == 200
        workspaces = response.json()
        assert len(workspaces) == 1
        workspace = schemas.TenantForUser(**workspaces[0])
        assert workspace.organization_id == auth.organization_id
        assert workspace.display_name == settings.DEFAULT_WORKSPACE_NAME


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_org_user_invite_disallowed(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Test than role cannot be specified when inviting at the org level when max_workspaces = 1"""
    if use_api_key:
        pytest.skip("Api keys are not supported for org auth endpoints")

    new_user_email = "<EMAIL>"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, organization_is_personal=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # max workspaces is 1 by default for personal, so this should fail
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 400

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # update max workspaces to 2
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 2}' WHERE id = $1",
            auth.organization_id,
        )

        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # update max workspaces to -1 (unlimited)
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": -1}' WHERE id = $1",
            auth.organization_id,
        )

        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_user_invite_flow(
    db_asyncpg: asyncpg.Connection,
    auth_tenant_one: AuthInfo,
    auth_tenant_two: AuthInfo,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test organization member invite flows."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    # test that an unauthenticated user cannot see pending invites
    response = await http_no_auth.get(
        "/orgs/pending",
    )
    assert response.status_code == 403

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # test new user only has access to their own personal tenant
        response = await client.get("/tenants")
        assert response.status_code == 200
        assert len(response.json()) == 1

        # test new user has no pending invites (for now)
        response = await client.get("/orgs/pending")
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can't claim a pending invite that doesn't exist
        response = await client.post(
            f"/orgs/pending/{uuid4()}/claim",
        )
        assert response.status_code == 404

        # create a pending invite for someone else
        other_new_user_email = "<EMAIL>"
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": other_new_user_email,
            },
        )
        assert response.status_code == 200
        other_new_user_pending_identity_id = response.json()["id"]

        # test new user can't see a pending invite that isn't theirs
        response = await client.get("/orgs/pending")
        assert response.status_code == 200
        assert len(response.json()) == 0

        # test new user can't claim a pending invite that isn't theirs
        response = await client.post(
            f"/orgs/pending/{auth.organization_id}/claim",
        )
        assert response.status_code == 404

        # test new user can't delete a pending invite that isn't theirs
        response = await client.delete(
            f"/orgs/current/{other_new_user_pending_identity_id}/pending",
        )
        assert response.status_code == 404

        # create a pending invite for this user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
            },
        )
        assert response.status_code == 200

        # try to invite again, should fail
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
            },
        )
        assert response.status_code == 409

        # the user should have a single pending org identity
        org_members = await get_org_members(auth)
        assert (
            len([m.email for m in org_members.pending if m.email == new_user_email])
            == 1
        )

        # test new user can see a pending invite that is theirs
        response = await client.get(
            "/orgs/pending",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()) == 1
        assert response.json()[0]["id"] == str(auth.organization_id)

        # test new user cannot use a tenant until they claim the invite
        response = await client.get(
            "/datasets",
            headers={
                **new_user_headers,
                "X-Tenant-Id": str(auth_tenant_one.tenant_id),
            },
        )
        assert response.status_code == 403

        # test new user can delete a pending invite that is theirs
        response = await client.delete(
            f"/orgs/pending/{auth.organization_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # test that the invite is gone from the org members list
        response = await client.get("/orgs/current/members")
        assert response.status_code == 200
        assert (
            len([p for p in response.json()["pending"] if p["email"] == new_user_email])
            == 0
        )

        # test that the invite is gone from the pending list for the user
        response = await client.get(
            "/orgs/pending",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()) == 0

        # recreate the pending invite for this user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
            },
        )
        assert response.status_code == 200

        # test new user can claim a pending invite that is theirs
        response = await client.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        claimed_identity = Identity(**response.json())
        assert claimed_identity.access_scope == schemas.AccessScope.organization
        assert claimed_identity.organization_id == auth.organization_id
        assert claimed_identity.tenant_id is None
        org_members = await get_org_members(auth)
        assert claimed_identity.id in [m.id for m in org_members.members]
        identity_full = next(
            m for m in org_members.members if m.id == claimed_identity.id
        )
        assert identity_full.role_name == "Organization User"

        # test new user cannot use a tenant after they claim the invite
        response = await client.get(
            "/datasets",
            headers={
                "X-Tenant-Id": str(auth_tenant_two.tenant_id),
            },
        )
        assert response.status_code == 403

        # test new user cannot create a dataset (has write permissions)
        response = await client.post(
            "/datasets",
            headers={
                "X-Tenant-Id": str(auth_tenant_two.tenant_id),
            },
            json={
                "name": random_lower_string(),
            },
        )
        assert response.status_code == 403

        # test deleting the user
        response = await client.delete(
            f"/orgs/current/members/{claimed_identity.id}",
        )
        assert response.status_code == 200
        response = await client.get(
            "/orgs/current/members",
        )
        members = response.json()["members"]
        assert len([m for m in members if m["id"] == claimed_identity.id]) == 0


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_user_invite_flow_case_insensitive(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test user invite flow works case-insensitively."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email_lower = "<EMAIL>"
    new_user_email_upper = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email_lower))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email_lower,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # create a pending invite for this user with uppercase
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email_upper,
            },
        )
        assert response.status_code == 200

        # try to invite again with either casing, should fail
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email_lower,
            },
        )
        assert response.status_code == 409
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email_upper,
            },
        )
        assert response.status_code == 409

        # the user should have a single pending org identity
        org_members = await get_org_members(auth)
        assert (
            len(
                [
                    m.email
                    for m in org_members.pending
                    if m.email == new_user_email_lower
                ]
            )
            == 1
        )

        # test new user can see a pending invite that is theirs
        response = await client.get(
            "/orgs/pending",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        assert len(response.json()) == 1
        assert response.json()[0]["id"] == str(auth.organization_id)

        # test new user can claim a pending invite that is theirs
        response = await client.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        claimed_identity = Identity(**response.json())
        assert claimed_identity.access_scope == schemas.AccessScope.organization
        assert claimed_identity.organization_id == auth.organization_id
        assert claimed_identity.tenant_id is None
        org_members = await get_org_members(auth)
        assert claimed_identity.id in [m.id for m in org_members.members]
        identity_full = next(
            m for m in org_members.members if m.id == claimed_identity.id
        )
        assert identity_full.role_name == "Organization User"
        assert identity_full.email == new_user_email_lower


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_invite_user_to_org_and_tenants(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a user can be invited to an org and then multiple tenants."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"

        # increase max workspaces
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 5}' WHERE id = $1",
            org_id,
        )

        workspaces = await _create_workspaces(client, org_id, 2)

        # set up auths for each workspace
        auths: list[AuthInfo] = []
        for workspace in workspaces:
            authinfo = AuthInfo(
                user_id=auth.user_id,
                user_email=auth.user_email,
                tenant_id=workspace.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
            )
            auths.append(authinfo)
        auth1 = auths[0]
        auth2 = auths[1]

        # invite user to org as an org user & claim invite
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            f"/orgs/pending/{org_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text
        claimed_org_identity = schemas.Identity(**response.json())

        # check org members
        org_members = await get_org_members(auth1)
        assert len(org_members.members) == 2  # owner + user
        assert len(org_members.pending) == 0

        # invite user to both workspaces (as readonly for 2)
        # this should add user as an identity directly, no pending step / need to claim
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(stored_user_id),
            },
            headers={
                "X-Tenant-Id": str(auth1.tenant_id),
            },
        )
        assert response.status_code == 200, response.text
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(stored_user_id),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={
                "X-Tenant-Id": str(auth2.tenant_id),
            },
        )
        assert response.status_code == 200, response.text

        # check same org & org identity
        org_members = await get_org_members(auth1)
        assert org_members == await get_org_members(auth2)
        assert len(org_members.members) == 2  # owner + user
        assert len(org_members.pending) == 0
        assert claimed_org_identity.id in [m.id for m in org_members.members]
        org_identity_full = next(
            m for m in org_members.members if m.id == claimed_org_identity.id
        )
        assert org_identity_full.role_name == "Organization User", (
            f"User should be an org user but was {org_identity_full.role_name}"
        )
        assert org_identity_full.role_id == await db_asyncpg.fetchval(
            "SELECT id from roles WHERE name = 'ORGANIZATION_USER'"
        )
        assert org_identity_full.access_scope == schemas.AccessScope.organization
        assert org_identity_full.read_only is True
        assert org_identity_full.tenant_id is None
        assert org_identity_full.organization_id == org_id
        assert set(
            [p.provider_user_id for p in org_identity_full.linked_login_methods]
        ) == {
            org_identity_full.user_id,
        }
        # check that org member lists both tenants
        assert set(org_identity_full.tenant_ids) == {
            auth1.tenant_id,
            auth2.tenant_id,
        }

        user_ids = []
        for authinfo in auths:
            # check that user is claimed in workspaces
            tenant_members = await get_tenant_members(authinfo)
            assert new_user_email not in [
                member.email for member in tenant_members.pending
            ]
            assert new_user_email in [member.email for member in tenant_members.members]
            member = next(
                m for m in tenant_members.members if m.email == new_user_email
            )
            assert member.org_role_id == org_user_role_id
            assert member.org_role_name == "Organization User"
            user_ids.append(member.user_id)

        claimed_tenant_one_identity_id = await db_asyncpg.fetchval(
            f"SELECT id from identities WHERE user_id = '{user_ids[0]}' AND tenant_id = '{str(workspaces[0].id)}'"
        )
        assert claimed_tenant_one_identity_id is not None
        claimed_tenant_two_identity_id = await db_asyncpg.fetchval(
            f"SELECT id from identities WHERE user_id = '{user_ids[1]}' AND tenant_id = '{str(workspaces[1].id)}'"
        )
        assert claimed_tenant_two_identity_id is not None

        # check that user has claimed identities in both tenants
        tenant1_members = await get_tenant_members(auths[0])
        tenant2_members = await get_tenant_members(auths[1])
        assert len(tenant1_members.members) == 2  # owner + new user
        assert len(tenant2_members.members) == 2
        assert len(tenant1_members.pending) == 0
        assert len(tenant2_members.pending) == 0
        assert claimed_tenant_one_identity_id in [m.id for m in tenant1_members.members]
        assert claimed_tenant_two_identity_id in [m.id for m in tenant2_members.members]
        tenant1_identity_full = next(
            m for m in tenant1_members.members if m.id == claimed_tenant_one_identity_id
        )
        assert tenant1_identity_full.access_scope == schemas.AccessScope.workspace
        assert tenant1_identity_full.organization_id == org_id
        assert tenant1_identity_full.tenant_id == auth1.tenant_id
        assert tenant1_identity_full.read_only is False
        tenant2_identity_full = next(
            m for m in tenant2_members.members if m.id == claimed_tenant_two_identity_id
        )
        assert tenant2_identity_full.access_scope == schemas.AccessScope.workspace
        assert tenant2_identity_full.organization_id == org_id
        assert tenant2_identity_full.tenant_id == auth2.tenant_id
        assert tenant2_identity_full.read_only is True


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
@pytest.mark.parametrize("readonly_workspace_role", [True, False])
async def test_invite_user_multiple_workspaces(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    readonly_workspace_role: bool,
    use_api_key: bool,
) -> None:
    """Test that a user can be invited to an org and multiple tenants on the same invite, and claim all via org invite."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_id = auth.organization_id
        assert org_id is not None, "Organization should exist"

        # increase max workspaces
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 3}' WHERE id = $1",
            org_id,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 2)

        # set up auths for each workspace
        auths: list[AuthInfo] = []
        for workspace in workspaces:
            authinfo = AuthInfo(
                tenant_id=workspace.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
            )
            auths.append(authinfo)

        # invite user to org and also tenants/workspaces
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(workspaces[0].id), str(workspaces[1].id)],
                "workspace_role_id": str(workspace_viewer_role_id)
                if readonly_workspace_role
                else None,
            },
        )
        assert response.status_code == 200, response.text

        # verify seat change events
        seat_events = await _list_seat_events_for_org(org_id)
        assert len(seat_events) == 2

        # 0 is ADMIN seat_type, 1 is USER. Always add User for new members if
        # RBAC is enabled and there are multiple workspaces
        assert seat_events[0].seat_type == OrganizationRoles.ADMIN.value
        assert seat_events[0].seats_before == 1
        assert seat_events[0].seats_after == 1
        assert seat_events[0].pending_seats_before == 0
        assert seat_events[0].pending_seats_after == 0

        org_user_seat_event = seat_events[1]
        assert org_user_seat_event.organization_id == org_id
        assert org_user_seat_event.operation == SeatChangeOperation.INVITE
        assert (
            org_user_seat_event.seat_type == OrganizationRoles.USER.value
            if readonly_workspace_role
            else OrganizationRoles.ADMIN.value
        )
        assert org_user_seat_event.seats_after == org_user_seat_event.seats_before
        assert (
            org_user_seat_event.pending_seats_after
            == org_user_seat_event.pending_seats_before + 1
        )

        for authinfo in auths:
            # check that user is pending on workspaces
            tenant_members = await get_tenant_members(authinfo)
            assert new_user_email in [member.email for member in tenant_members.pending]

        # claim invites via org invite
        response = await http_no_auth.post(
            f"/orgs/pending/{org_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200, response.text
        claimed_org_identity = schemas.Identity(**response.json())

        # verify seat change events
        seat_events = await _list_seat_events_for_org(org_id)
        assert len(seat_events) == 4
        assert seat_events[2].seat_type == OrganizationRoles.ADMIN.value
        assert seat_events[2].seats_before == 1
        assert seat_events[2].seats_after == 1
        assert seat_events[2].pending_seats_before == 0
        assert seat_events[2].pending_seats_after == 0

        org_user_seat_event = seat_events[3]
        assert org_user_seat_event.organization_id == org_id
        assert (
            org_user_seat_event.operation == SeatChangeOperation.CLAIM_INTO_ORGANIZATION
        )
        assert (
            org_user_seat_event.seat_type == OrganizationRoles.USER.value
            if readonly_workspace_role
            else OrganizationRoles.ADMIN.value
        )

        assert org_user_seat_event.seats_after == org_user_seat_event.seats_before + 1
        assert (
            org_user_seat_event.pending_seats_after
            == org_user_seat_event.pending_seats_before - 1
        )

        # check same org & org identity
        org_members = await get_org_members(auths[0])
        assert org_members == await get_org_members(auths[1])
        assert len(org_members.members) == 2  # org owner + 1 user
        assert len(org_members.pending) == 0
        assert claimed_org_identity.id in [m.id for m in org_members.members]
        org_identity_full = next(
            m for m in org_members.members if m.id == claimed_org_identity.id
        )
        assert org_identity_full.role_name == "Organization User", (
            f"User should be an Org User but was {org_identity_full.role_name}"
        )
        assert org_identity_full.role_id == await db_asyncpg.fetchval(
            "SELECT id from roles WHERE name = 'ORGANIZATION_USER'"
        )
        assert org_identity_full.access_scope == schemas.AccessScope.organization
        assert org_identity_full.read_only is True
        assert org_identity_full.tenant_id is None
        assert org_identity_full.organization_id == org_id

        user_ids = []
        for authinfo in auths:
            # check that user is claimed in workspaces
            tenant_members = await get_tenant_members(authinfo)
            assert new_user_email not in [
                member.email for member in tenant_members.pending
            ]
            assert new_user_email in [member.email for member in tenant_members.members]
            member = next(
                m for m in tenant_members.members if m.email == new_user_email
            )
            user_ids.append(member.user_id)

        claimed_tenant_one_identity_id = await db_asyncpg.fetchval(
            f"SELECT id from identities WHERE user_id = '{user_ids[0]}' AND tenant_id = '{str(workspaces[0].id)}'"
        )
        assert claimed_tenant_one_identity_id is not None
        claimed_tenant_two_identity_id = await db_asyncpg.fetchval(
            f"SELECT id from identities WHERE user_id = '{user_ids[1]}' AND tenant_id = '{str(workspaces[1].id)}'"
        )
        assert claimed_tenant_two_identity_id is not None

        # check that user has claimed identities in both tenants
        tenant1_members = await get_tenant_members(auths[0])
        tenant2_members = await get_tenant_members(auths[1])
        assert len(tenant1_members.members) == 2  # owner + new user
        assert len(tenant2_members.members) == 2
        assert len(tenant1_members.pending) == 0
        assert len(tenant2_members.pending) == 0
        assert claimed_tenant_one_identity_id in [m.id for m in tenant1_members.members]
        assert claimed_tenant_two_identity_id in [m.id for m in tenant2_members.members]
        tenant1_identity_full = next(
            m for m in tenant1_members.members if m.id == claimed_tenant_one_identity_id
        )
        assert tenant1_identity_full.access_scope == schemas.AccessScope.workspace
        assert tenant1_identity_full.organization_id == org_id
        assert tenant1_identity_full.tenant_id == auths[0].tenant_id
        assert tenant1_identity_full.read_only == readonly_workspace_role
        tenant2_identity_full = next(
            m for m in tenant2_members.members if m.id == claimed_tenant_two_identity_id
        )
        assert tenant2_identity_full.access_scope == schemas.AccessScope.workspace
        assert tenant2_identity_full.organization_id == org_id
        assert tenant2_identity_full.tenant_id == auths[1].tenant_id
        assert tenant2_identity_full.read_only == readonly_workspace_role


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_invite_user_dissalow_cross_org_tenant(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that a user cannot be invited to workspaces in different orgs."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        aclient1 = authed_client.client
        auth1 = authed_client.auth
        org_id = auth1.organization_id
        assert org_id is not None, "Organization should exist"

        # create separate tenant in a different org
        async with fresh_tenant_client(
            db_asyncpg, use_api_key, set_org_id=True
        ) as authed_client2:
            auth2 = authed_client2.auth

            # invite user to tenants in two different orgs, should be dissallowed
            response = await aclient1.post(
                "/orgs/current/members",
                json={
                    "email": new_user_email,
                    "workspace_ids": [str(auth1.tenant_id), str(auth2.tenant_id)],
                },
            )
            assert response.status_code == 400, response.text


@pytest.mark.skip(reason="not enabled yet")
async def test_patch_user_empty_params_fails(
    http_tenant_one: AsyncClient,
    user_tenant_one: schemas.UserWithPassword,
    use_api_key: bool,
) -> None:
    """Test that user info does not change if params are empty."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    response = await http_tenant_one.patch(
        f"/orgs/current/members/{user_tenant_one.id}",
        json={
            "full_name": None,
            "password": None,
        },
    )

    assert response.status_code == 422


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_add_user_fails_if_over_limit(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that an organization's identity limit is enforced."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    org_admin_role_id = await db_asyncpg.fetchval(
        f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # delete org members minus just-created, owner, and long-lived user
        org_members = await get_org_members(auth)
        all_members = org_members.members + org_members.pending
        members_to_delete = [member.user_id for member in all_members]
        for id in members_to_delete:
            await client.delete(
                f"/orgs/current/members/{id}",
            )

        org_members = await get_org_members(auth)
        num_org_members = len(org_members.members) + len(org_members.pending)

        # create a new tenant
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
            },
        )

        # Tenant was created
        assert response.status_code == 200, response.text
        new_tenant_id = UUID(response.json()["id"])
        new_org_id = UUID(response.json()["organization_id"])

        new_tenant_headers = {
            "Authorization": client.headers["Authorization"],
            "X-Tenant-Id": str(new_tenant_id),
            "X-Organization-Id": str(new_org_id),
        }
        response = await http_no_auth.get("/orgs/current", headers=new_tenant_headers)
        assert response.status_code == 200, response.text
        new_tenant_config = response.json()["config"]

        # Check that org members cannot go over tenant limit
        # 1. First we exhaust the identities, inviting all org admins
        # subtract num_org_members because we may already have some members
        num_to_add = new_tenant_config["max_identities"] - num_org_members
        for i in range(num_to_add):
            response = await http_no_auth.post(
                "/orgs/current/members",
                headers=new_tenant_headers,
                json={"email": f"{i}@langchain.dev", "role_id": str(org_admin_role_id)},
            )
            assert response.status_code == 200, response.text

        # check tenant members (should only have one member, the creator)
        # while there should be one pending member for each invite (b/c org admin is auto-added)
        response = await http_no_auth.get(
            "/workspaces/current/members",
            headers=new_tenant_headers,
        )
        assert len(response.json()["pending"]) == num_to_add
        assert len(response.json()["members"]) == 1

        # check org members, should have pending members that have been added and one admin
        response = await http_no_auth.get(
            "/orgs/current/members",
            headers=new_tenant_headers,
        )
        assert response.status_code == 200, response.text
        org_members = schemas.OrganizationMembers(**response.json())
        assert (
            len(org_members.pending) + len(org_members.members)
            == new_tenant_config["max_identities"]
        )

        # 2. Then we try to add one more, which should fail
        response = await http_no_auth.post(
            "/orgs/current/members",
            headers=new_tenant_headers,
            json={"email": "<EMAIL>"},
        )
        assert response.status_code == 400, response.text


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_delete_user_cannot_delete_self(
    http_tenant_one: AsyncClient,
    auth_tenant_one: AuthInfo,
    use_api_key: bool,
) -> None:
    """Test that a user cannot delete themselves."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    response = await http_tenant_one.delete(
        f"/orgs/current/members/{auth_tenant_one.user_id}",
    )
    assert response.status_code == 404


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_delete_user_connected_records(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test organization member deletion."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # increase max workspaces
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 5}' WHERE id = $1",
            auth.organization_id,
        )

        # create 2 new workspaces in this org
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
                "organization_id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200
        tenant1 = schemas.Tenant(**response.json())
        response = await client.post(
            "/tenants",
            json={
                "display_name": random_lower_string(),
                "organization_id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200
        tenant2 = schemas.Tenant(**response.json())
        tenants = [tenant1, tenant2]

        # create and claim an invite for this user in this org and workspaces
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(tenant1.id), str(tenant2.id)],
            },
        )
        assert response.status_code == 200
        response = await client.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        claimed_identity = Identity(**response.json())

        # create and claim invites in both tenants
        auths = []
        for tenant in tenants:
            authinfo = AuthInfo(
                tenant_id=tenant.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
            )
            auths.append(authinfo)

        # Add API keys
        for authinfo in auths:
            await create_api_key(authinfo, schemas.APIKeyCreateRequest())

        # get workspace identities before deletion
        workspace_identities: List[Identity] = []
        for authinfo in auths:
            workspace_members = await get_tenant_members(authinfo)
            assert claimed_identity.user_id in [
                member.user_id for member in workspace_members.members
            ]
            workspace_identity = next(
                m
                for m in workspace_members.members
                if m.user_id == claimed_identity.user_id
            )
            workspace_identities.append(workspace_identity)

        # delete the user
        response = await client.delete(
            f"/orgs/current/members/{claimed_identity.id}",
        )
        assert response.status_code == 200

        # ensure user record but no organization identity
        user = await get_user(new_user_email)
        assert user is not None, "Deleted user should exist in users table"
        response = await client.get(
            "/orgs/current/members",
        )
        members = response.json()["members"]
        assert len([m for m in members if m["id"] == claimed_identity.id]) == 0

        # ensure no tenant identities and api keys
        for idx, authinfo in enumerate(auths):
            workspace_members = await get_tenant_members(authinfo)
            assert claimed_identity.user_id not in [
                member.id for member in workspace_members.members
            ], "User should not have an identity after deletion"
            assert new_user_email not in [
                member.email for member in workspace_members.pending
            ], "User should not have a pending identity after deletion"
            api_keys = await list_api_keys(authinfo)
            workspace_identity = workspace_identities[idx]
            deleted_users_api_keys = [
                key for key in api_keys if key["identity_id"] == workspace_identity.id
            ]
            assert len(deleted_users_api_keys) == 0, "User's API keys should be deleted"


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
@pytest.mark.parametrize("test_delete_by_user", [True, False])
async def test_delete_pending_org_identity(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    test_delete_by_user: bool,
):
    """Test that deleting a pending org identity also deletes pending workspace identities."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # increase max workspaces
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"max_workspaces\": 5}' WHERE id = $1",
            auth.organization_id,
        )
        org_admin_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN'"
        )

        # create an invite for this user in this org
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 200
        pending_org_identity = schemas.PendingIdentity(**response.json())

        # delete the pending org identity, either simulating the user themselves or an admin
        if test_delete_by_user:
            stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
            jwt = jwt_for_user(
                user_id=stored_user_id,
                user_email=new_user_email,
            )
            new_user_headers = {
                "Authorization": f"Bearer {jwt}",
            }
            response = await client.delete(
                f"/orgs/pending/{auth.organization_id}",
                headers=new_user_headers,
            )
        else:
            response = await client.delete(
                f"/orgs/current/members/{pending_org_identity.id}/pending",
            )
        assert response.status_code == 200

        # ensure no pending org identity
        response = await client.get(
            "/orgs/current/members",
        )
        assert response.status_code == 200
        org_members = schemas.OrganizationMembers(**response.json())
        assert len(org_members.pending) == 0

        # ensure no pending workspace identities
        tenant_members = await get_tenant_members(auth)
        assert len(tenant_members.members) == 1  # owner
        assert len(tenant_members.pending) == 0


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_create_delete_role(
    http_tenant_one: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a role can be created and deleted."""
    if use_api_key:
        pytest.skip("Endpoint not enabled for API key")
    # Cannot create empty role
    response = await http_tenant_one.post(
        "/orgs/current/roles",
        json={
            "display_name": "test role",
            "description": "test role description",
        },
    )
    assert response.status_code == 422

    response = await http_tenant_one.post(
        "/orgs/current/roles",
        json={
            "display_name": "test role",
            "description": "test role description",
            "permissions": ["projects:read", "workspaces:read"],
        },
    )
    assert response.status_code == 200
    new_role = Role(**response.json())
    assert new_role.display_name == "test role"
    assert new_role.organization_id
    assert new_role.description == "test role description"

    # Verify the role was created
    all_roles_response = await http_tenant_one.get(
        "/orgs/current/roles",
    )
    assert response.status_code == 200
    all_roles = all_roles_response.json()
    assert str(new_role.id) in [role["id"] for role in all_roles]
    role = next(role for role in all_roles if role["id"] == str(new_role.id))
    assert role is not None
    assert role["display_name"] == "test role"
    assert role["description"] == "test role description"
    assert "projects:read" in role["permissions"]
    assert "workspaces:read" in role["permissions"]

    # Cannot create role with same display name
    response = await http_tenant_one.post(
        "/orgs/current/roles",
        json={
            "display_name": "test role",
            "description": "test role description",
            "permissions": ["projects:read", "workspaces:read"],
        },
    )
    assert response.status_code == 409

    # Can update role
    response = await http_tenant_one.patch(
        f"/orgs/current/roles/{new_role.id}",
        json={
            "display_name": "test role123",
            "description": "test role description123",
            "permissions": ["projects:read", "workspaces:read", "projects:create"],
        },
    )
    assert response.status_code == 200
    new_role = Role(**response.json())
    assert new_role.display_name == "test role123"
    assert new_role.description == "test role description123"

    # Deleting the role succeeds if no users are assigned the role
    response = await http_tenant_one.delete(
        f"/orgs/current/roles/{new_role.id}",
    )
    assert response.status_code == 200


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_create_role_and_add_to_user(
    db_asyncpg: asyncpg.Connection,
    http_tenant_two: AsyncClient,
    auth_tenant_two: AuthInfo,
    use_api_key: bool,
) -> None:
    """Test that a role can be created and assigned to user with correct permissions"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    response = await http_tenant_two.post(
        "/orgs/current/roles",
        json={
            "display_name": "test role",
            "description": "test role description",
            "permissions": ["projects:read", "workspaces:read"],
        },
    )
    assert response.status_code == 200
    new_role = Role(**response.json())
    assert new_role.display_name == "test role"
    assert new_role.description == "test role description"
    assert new_role.organization_id

    # Verify the role was created
    all_roles_response = await http_tenant_two.get(
        "/orgs/current/roles",
    )
    assert response.status_code == 200
    all_roles = all_roles_response.json()
    role = next(role for role in all_roles if role["id"] == str(new_role.id))
    assert role is not None
    assert role["display_name"] == "test role"
    assert role["description"] == "test role description"
    assert "projects:read" in role["permissions"]
    assert "workspaces:read" in role["permissions"]
    workspace_admin_role = next(
        role for role in all_roles if role["name"] == "WORKSPACE_ADMIN"
    )

    # Can update role
    response = await http_tenant_two.patch(
        f"/orgs/current/roles/{new_role.id}",
        json={
            "display_name": "test role123",
            "description": "test role description123",
            "permissions": ["projects:read", "workspaces:read", "projects:create"],
        },
    )
    assert response.status_code == 200
    new_role = Role(**response.json())
    assert new_role.display_name == "test role123"
    assert new_role.description == "test role description123"

    # Create a new user
    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {jwt}",
    }
    response = await http_tenant_two.get("/tenants", headers=new_user_headers)

    assert response.status_code == 200

    # Invite user to tenant
    response = await http_tenant_two.post(
        "/orgs/current/members",
        json={
            "email": new_user_email,
            "workspace_role_id": str(new_role.id),
            "workspace_ids": [str(auth_tenant_two.tenant_id)],
        },
    )
    assert response.status_code == 200, response.text

    # Claim invite
    response = await http_tenant_two.post(
        f"/workspaces/pending/{auth_tenant_two.tenant_id}/claim",
        headers=new_user_headers,
    )
    assert response.status_code == 200
    claimed_identity = schemas.Identity(**response.json())
    identity_id = claimed_identity.id

    # confirm new user's workspace identity
    assert claimed_identity.access_scope == schemas.AccessScope.workspace
    assert claimed_identity.organization_id == auth_tenant_two.organization_id
    assert claimed_identity.tenant_id == auth_tenant_two.tenant_id
    assert claimed_identity.read_only is False
    assert claimed_identity.role_id == new_role.id

    # confirm new user's organization identity
    org_members = await get_org_members(auth_tenant_two)
    assert claimed_identity.user_id in [m.user_id for m in org_members.members]
    org_identity_full = next(
        m for m in org_members.members if m.user_id == claimed_identity.user_id
    )
    assert org_identity_full.role_name == "Organization User"
    assert org_identity_full.role_id == await db_asyncpg.fetchval(
        "SELECT id from roles WHERE name = 'ORGANIZATION_USER'"
    )
    assert org_identity_full.access_scope == schemas.AccessScope.organization
    assert org_identity_full.read_only is True
    assert org_identity_full.tenant_id is None
    assert org_identity_full.organization_id == auth_tenant_two.organization_id

    # Verify user has correct permissions
    response = await http_tenant_two.post(
        "/sessions", headers=new_user_headers, json={"name": "test"}
    )
    assert response.status_code == 200

    # Deleting the role fails if any users are assigned the role
    response = await http_tenant_two.delete(
        f"/orgs/current/roles/{new_role.id}",
    )
    assert response.status_code == 409

    # Change user role
    response = await http_tenant_two.patch(
        f"/workspaces/current/members/{identity_id}",
        json={
            "role_id": workspace_admin_role["id"],
        },
    )
    assert response.status_code == 200

    # Deleting the role succeeds if no users are assigned the role
    response = await http_tenant_two.delete(
        f"/orgs/current/roles/{new_role.id}",
    )
    assert response.status_code == 200


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_cannot_delete_self(
    db_asyncpg: asyncpg.Connection,
    http_tenant_one: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that a role with workspace:manage permissions still cannot delete themselves"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    # Create a new user
    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {jwt}",
    }
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # We must create a custom role for testing because organization admins have their own constraint
        response = await client.post(
            "/orgs/current/roles",
            json={
                "display_name": "test role with manage",
                "description": "test role description",
                "permissions": ["workspaces:manage", "workspaces:read"],
            },
        )
        assert response.status_code == 200
        new_role = Role(**response.json())

        org_user_role_id = await db_asyncpg.fetchval(
            "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
        )

        # Invite user to tenant
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(new_role.id),
            },
        )
        assert response.status_code == 200, response.text
        pending_org_identity = schemas.PendingIdentity(**response.json())

        ws_identities = await get_tenant_members(auth)
        assert len(ws_identities.members) == 1  # owner
        assert len(ws_identities.pending) == 1  # new user

        response = await http_tenant_one.delete(
            f"/workspaces/current/members/{ws_identities.pending[0].id}/pending",
        )
        assert response.status_code == 404, "Some other user cannot delete the invite"
        response = await client.delete(
            f"/workspaces/current/members/{ws_identities.pending[0].id}/pending",
        )
        assert response.status_code == 200, "Owner can delete the workspace invite"
        response = await client.delete(
            f"/orgs/current/members/{pending_org_identity.id}/pending",
        )
        assert response.status_code == 200, "Owner can delete the org invite"

        # Re-invite after deletion
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
                "workspace_role_id": str(new_role.id),
            },
        )
        assert response.status_code == 200

        # Claim invite
        response = await client.post(
            f"/workspaces/pending/{auth.tenant_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200
        claimed_identity = schemas.Identity(**response.json())
        identity_id = claimed_identity.id

        # confirm new user cannot delete self
        response = await client.delete(
            f"/workspaces/current/members/{identity_id}",
            headers=new_user_headers,
        )
        assert response.status_code == 404


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_list_roles(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that org roles are not present yet"""
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        aclient = authed_client.client

        response = await aclient.get(
            "/orgs/current/roles",
        )
        assert response.status_code == 200
        roles = response.json()
        assert len(roles) == 5
        assert {
            "WORKSPACE_ADMIN",
            "WORKSPACE_USER",
            "WORKSPACE_VIEWER",
            "ORGANIZATION_USER",
            "ORGANIZATION_ADMIN",
        } == {role["name"] for role in roles}


@pytest.mark.skipif(settings.AUTH_TYPE == "none", reason="single org/tenant")
async def test_list_permissions(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that you can list permissions"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        response = await client.get(
            "/orgs/permissions",
        )
        assert response.status_code == 200
        permissions = response.json()
        valid_permissions = [e.value for e in Permissions] + [
            e.value for e in OrganizationPermissions
        ]
        for permission in permissions:
            assert permission["name"] is not None
            assert permission["name"] in valid_permissions, (
                f"Unknown permission {permission}"
            )
            assert permission["description"] is not None

        # sanity check for permissions
        assert "organization:manage" in [p["name"] for p in permissions]
        assert "workspaces:manage" in [p["name"] for p in permissions]


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_create_role_with_colon(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that you cannot create a role with a colon in the display name"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        response = await client.post(
            "/orgs/current/roles",
            json={"display_name": "test:role", "description": "test role description"},
        )
        assert response.status_code == 422, response.text


@pytest.mark.skip(reason="Due to parallelization")
async def test_org_current_none_auth(
    http_no_auth: AsyncClient,
) -> None:
    """Test that you can read org config"""

    response = await http_no_auth.get(
        "/orgs/current",
    )
    assert response.status_code == 200, response.text


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_org_current_personal(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that you can read org config for a personal org"""
    if use_api_key:
        pytest.skip("Api keys don't work for org info")

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, organization_is_personal=True
    ) as authed_client:
        client = authed_client.client

        response = await client.get(
            "/orgs/current",
        )
        assert response.status_code == 200, response.text
        org_config = response.json()
        assert "config" in org_config
        assert "can_use_rbac" in org_config["config"]
        assert org_config["reached_max_workspaces"] is True
        assert set(org_config["permissions"]) == {
            "organization:manage",
            "organization:read",
        }


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
@pytest.mark.parametrize("endpoint", ["/orgs/current", "/orgs/current/info"])
async def test_org_current_shared(
    endpoint: str,
    http_tenant_one: AsyncClient,
    http_tenant_one_read_only: AsyncClient,
    use_api_key: bool,
) -> None:
    if use_api_key:
        pytest.skip("Api keys don't work for org info")

    response = await http_tenant_one.get(
        endpoint,
    )
    assert response.status_code == 200, response.text
    org_info = response.json()
    assert "config" in org_info
    assert "can_use_rbac" in org_info["config"]
    assert org_info["reached_max_workspaces"] is False
    assert set(org_info["permissions"]) == {
        "organization:manage",
        "organization:read",
    }

    response = await http_tenant_one_read_only.get(
        endpoint,
    )
    assert response.status_code == 200, response.text
    org_config_readonly = response.json()
    assert "config" in org_config_readonly
    assert "can_use_rbac" in org_config_readonly["config"]
    assert org_config_readonly["reached_max_workspaces"] is False
    assert set(org_config_readonly["permissions"]) == {
        "organization:read",
    }


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_org_current_info(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
) -> None:
    """Test that org info has expected fields"""
    if use_api_key:
        pytest.skip("Api keys don't work for org info")

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/orgs/current/info")
        assert response.status_code == 200, response.text

        org_info = schemas.OrganizationInfo(**response.json())
        assert org_info.id == auth.organization_id
        assert org_info.display_name is not None
        assert org_info.config is not None
        assert org_info.is_personal is False
        assert org_info.tier == schemas.PaymentPlanTier.enterprise_legacy
        assert org_info.reached_max_workspaces is False
        assert org_info.permissions is not None
        assert org_info.sso_login_slug is None


@pytest.mark.skipif(
    config.settings.AUTH_TYPE not in ["supabase", "oauth"],
    reason="multiple orgs",
)
async def test_org_config_insert_defaults(
    auth_tenant_one: AuthInfo,
    use_api_key: bool,
) -> None:
    """Test that org config is inserted with defaults when an org is created"""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    tenantless_auth_info = TenantlessAuthInfo(
        available_tenants=[],
        user_id=auth_tenant_one.user_id,
        ls_user_id=auth_tenant_one.ls_user_id,
        user_email=auth_tenant_one.user_email,
    )

    # create new tenant and org
    tenant = await create_tenant(
        tenantless_auth_info, schemas.TenantCreate(display_name="test tenant")
    )
    assert tenant is not None
    assert tenant.organization_id is not None

    org_config = await get_org_config(tenant.organization_id)
    assert org_config is not None
    assert org_config == settings.SHARED_ORG_DEFAULT_CONFIG
    assert org_config.model_dump() == {
        "max_identities": 10,
        "max_workspaces": 3,
        "max_langgraph_cloud_deployments": 3,
        "max_free_langgraph_cloud_deployments": 0,
        "can_use_rbac": False,
        "can_add_seats": True,
        "startup_plan_approval_date": None,
        "premier_plan_approval_date": None,
        "partner_plan_approval_date": None,
        "can_use_langgraph_cloud": True,
        "can_use_saml_sso": False,
        "datadog_rum_session_sample_rate": 20,
        "can_use_bulk_export": False,
        "demo_lgp_new_graph_enabled": False,
        "can_disable_public_sharing": False,
        "max_prompt_webhooks": 1,
        # these are overridden in .env.local_test
        "can_serve_datasets": False,
        "use_python_playground_service": False,
        "show_updated_sidenav": False,
        "show_updated_resource_tags": False,
        "kv_dataset_message_support": True,
        "show_playground_prompt_canvas": False,
        "allow_custom_iframes": False,
        "enable_langgraph_pricing": False,
        "enable_thread_view_playground": False,
        "enable_org_usage_charts": False,
        "enable_select_all_traces": False,
        "playground_evaluator_strategy": "sync",
        "use_exact_search_for_prompts": False,
        "langgraph_deploy_own_cloud_enabled": False,
        "enable_k8s_vanilla_platform": False,
        "prompt_optimization_jobs_enabled": False,
        "langgraph_remote_reconciler_enabled": False,
        "langsmith_alerts_poc_enabled": True,
        "enable_align_evaluators": False,
        "tenant_skip_topk_facets": False,
        "lgp_templates_enabled": False,
        "langsmith_alerts_legacy_poc_enabled": False,
        "langsmith_experimental_search_enabled": False,
        "enable_monthly_usage_charts": False,
        "enable_lgp_metrics_charts": False,
        "new_rule_evaluator_creation_version": 2,
        "enable_tracing_project_redesign": False,
        "org_scoped_service_accounts_enabled": False,
        "enable_lgp_listeners_page": False,
    }


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_delete_org_admin_disallowed(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Test that deleting an org admin at workspace level fails (both pending and claimed)."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    stored_user_id = uuid.uuid5(uuid.NAMESPACE_OID, str(new_user_email))
    new_user_jwt = jwt_for_user(
        user_id=stored_user_id,
        user_email=new_user_email,
    )
    new_user_headers = {
        "Authorization": f"Bearer {new_user_jwt}",
    }

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        # single workspace and disable RBAC so Org Admin is the default role
        await db_asyncpg.execute(
            'UPDATE organizations SET config = config || \'{"max_workspaces": 1, "can_use_rbac": false}\' WHERE id = $1',
            auth.organization_id,
        )

        # invite the user to the workspace as an admin, which also creates a pending
        # organization invite with org admin role
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
            },
        )
        assert response.status_code == 200, response.text

        response = await client.get(
            "/workspaces/current/members/pending",
        )
        assert response.status_code == 200, response.text
        res_json = response.json()
        assert len(res_json) == 1
        pending_ws_identity = schemas.PendingIdentity(**res_json[0])

        # test that deleting the org admin's pending invite at the workspace level fails because single workspace
        response = await client.delete(
            f"/workspaces/current/members/{pending_ws_identity.id}/pending",
        )
        assert response.status_code == 400, response.text

        # claim the invite
        response = await client.post(
            f"/workspaces/pending/{auth.tenant_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # find the user and attempt to delete them, which should fail
        response = await client.get(
            "/workspaces/current/members",
        )
        assert response.status_code == 200
        tenant_members = schemas.TenantMembers(**response.json())
        assert new_user_email in [m.email for m in tenant_members.members]
        member_to_delete = next(
            m for m in tenant_members.members if m.email == new_user_email
        )
        response = await client.delete(
            f"/workspaces/current/members/{member_to_delete.id}",
        )
        assert response.status_code == 400


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="none auth")
async def test_disabled_organization(
    disabled_tenant_headers: dict,
    auth_disabled_tenant: AuthInfo,
    disabled_tenant_tracer_session_id: uuid.UUID,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test that a disabled organization correctly returns 403."""

    await db_asyncpg.fetchrow(
        """
        UPDATE organizations
        SET disabled = true
        WHERE id = $1
        """,
        auth_disabled_tenant.organization_id,
    )

    organization = await db_asyncpg.fetchrow(
        """
        SELECT * FROM organizations
        WHERE id = $1
        """,
        auth_disabled_tenant.organization_id,
    )
    assert organization["disabled"] is True

    # Test that org is disabled
    async with AsyncClient(
        transport=ASGITransport(app=app), base_url="http://test"
    ) as client:
        run_id = uuid4()
        response = await client.post(
            "/runs",
            headers=disabled_tenant_headers,
            json={
                "name": "LLM",
                "start_time": "2023-05-05T05:13:24.571809",
                "end_time": "2023-05-05T05:13:32.022361",
                "extra": {"foo": "bar"},
                "error": None,
                "execution_order": 1,
                "serialized": {"name": "AgentExecutor"},
                "inputs": {"input": "How many people live in canada as of 2023?"},
                "outputs": {"output": "39,566,248"},
                "session_id": str(disabled_tenant_tracer_session_id),
                "parent_run_id": None,
                "run_type": "chain",
                "id": str(run_id),
            },
        )
        assert response.status_code == 403

        response = await client.get(
            "/datasets",
            headers=disabled_tenant_headers,
        )
        assert response.status_code == 403

        # Re-enable tenant
        await db_asyncpg.fetchrow(
            """
            UPDATE organizations
            SET disabled = false
            WHERE id = $1
            """,
            auth_disabled_tenant.organization_id,
        )

        response = await client.get(
            "/datasets",
            headers=disabled_tenant_headers,
        )
        assert response.status_code == 200


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
async def test_saml_sso_disabled_on_org_by_default(
    mock_supabase_context_mgr: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    mock_supabase_client, _ = _setup_supabase_client_mock(mock_supabase_context_mgr, 0)
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        response = await client.get("/orgs/current/sso-settings")
        assert response.status_code == 403, response.text

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": "https://example.com/sso/saml/metadata",
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 403, response.text

        response = await client.delete(f"/orgs/current/sso-settings/{uuid4()}")
        assert response.status_code == 403, response.text

    assert not mock_supabase_client.create_sso_provider.called
    assert not mock_supabase_client.delete_sso_provider.called


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
async def test_saml_sso_crud(
    mock_supabase_context_mgr: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    mock_supabase_client, responses = _setup_supabase_client_mock(
        mock_supabase_context_mgr, 2
    )
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]

    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        prev_sso_login_slug = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert prev_sso_login_slug is None
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}' WHERE id = $1",
            auth.organization_id,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        sso_settings = response.json()
        assert sso_settings["provider_id"] == str(responses[0]["id"])
        assert sso_settings["metadata_url"] == metadata_urls[0]
        assert sso_settings["default_workspace_role_id"] == str(workspace_admin_role_id)
        assert sso_settings["default_workspace_ids"] == [str(auth.tenant_id)]

        workspaces = await _create_workspaces(client, auth.organization_id, 1)

        sso_login_slug_post_create = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert sso_login_slug_post_create is not None
        assert len(sso_login_slug_post_create) == LOGIN_SLUG_LENGTH

        response = await client.get("/orgs/current/sso-settings")
        assert response.status_code == 200, response.text
        sso_settings_list = response.json()
        assert len(sso_settings_list) == 1
        sso_settings_created = sso_settings_list[0]
        assert sso_settings_created == sso_settings

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings_created['id']}",
            json={
                "default_workspace_role_id": str(workspace_viewer_role_id),
            },
        )
        assert response.status_code == 200, response.text
        sso_settings_updated = response.json()
        assert sso_settings_updated["default_workspace_role_id"] == str(
            workspace_viewer_role_id
        )
        assert sso_settings_updated["default_workspace_ids"] == [str(auth.tenant_id)]
        assert not mock_supabase_client.update_sso_provider.called

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings_created['id']}",
            json={
                "metadata_xml": "<xml>patched</xml>",
            },
        )
        assert response.status_code == 200, response.text
        sso_settings_updated = response.json()
        assert sso_settings_updated["metadata_xml"] == "<xml>patched</xml>"
        assert sso_settings_updated["metadata_url"] is None

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings_created['id']}",
            json={
                "metadata_url": "https://example.com/updated",
            },
        )
        assert response.status_code == 200, response.text
        sso_settings_updated = response.json()
        assert sso_settings_updated["metadata_url"] == "https://example.com/updated"
        assert sso_settings_updated["metadata_xml"] is None

        response = await client.delete(
            f"/orgs/current/sso-settings/{sso_settings_created['id']}"
        )
        assert response.status_code == 200, response.text
        response = await client.get("/orgs/current/sso-settings")
        assert response.status_code == 200, response.text
        sso_settings_list = response.json()
        assert len(sso_settings_list) == 0

        sso_login_slug_post_delete = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert sso_login_slug_post_delete == sso_login_slug_post_create

        # New SSO settings should not change SSO login slug
        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[1],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        new_sso_settings = response.json()
        sso_login_slug_post_create2 = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert sso_login_slug_post_create2 == sso_login_slug_post_create

        response = await client.get("/orgs/current/info")
        assert response.status_code == 200, response.text
        org_info = response.json()
        assert org_info["sso_login_slug"] == sso_login_slug_post_create

        # Deleting workspace should remove from default_workspace_ids
        response = await client.patch(
            f"/orgs/current/sso-settings/{new_sso_settings['id']}",
            json={
                "default_workspace_ids": [str(auth.tenant_id), str(workspaces[0].id)],
            },
        )
        assert response.status_code == 200, response.text
        sso_settings_updated = response.json()
        assert set(sso_settings_updated["default_workspace_ids"]) == {
            str(auth.tenant_id),
            str(workspaces[0].id),
        }
        await db_asyncpg.execute("DELETE FROM tenants WHERE id = $1", workspaces[0].id)
        response = await client.get("/orgs/current/sso-settings")
        assert response.status_code == 200, response.text
        sso_settings_list = response.json()
        assert len(sso_settings_list) == 1
        sso_settings_post_ws_delete = sso_settings_list[0]
        assert set(sso_settings_post_ws_delete["default_workspace_ids"]) == {
            str(auth.tenant_id)
        }


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
async def test_saml_sso_create_multiple_orgs(
    mock_supabase_context_mgr: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 4)
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses[:2]
    ]
    metadata_xmls = [f"<xml>{response['id']}</xml>" for response in responses[2:]]

    params = [
        param
        for pair in zip(
            [
                {"metadata_xml": metadata_xml, "metadata_url": ""}
                for metadata_xml in metadata_xmls
            ],
            [
                {"metadata_url": metadata_url, "metadata_xml": ""}
                for metadata_url in metadata_urls
            ],
        )
        for param in pair
    ]

    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    for i, param in enumerate(params):
        async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
            client = authed_client.client
            auth = authed_client.auth
            await db_asyncpg.execute(
                "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}' WHERE id = $1",
                auth.organization_id,
            )

            response = await client.post(
                "/orgs/current/sso-settings",
                json={
                    **param,
                    "default_workspace_ids": [str(auth.tenant_id)],
                    "default_workspace_role_id": str(workspace_admin_role_id),
                },
            )
            assert response.status_code == 201, response.text
            sso_settings = response.json()
            assert sso_settings["provider_id"] == str(responses[i]["id"])
            assert sso_settings["metadata_url"] == (
                param.get("metadata_url") if param.get("metadata_url") else None
            )
            assert sso_settings["metadata_xml"] == (
                param.get("metadata_xml") if param.get("metadata_xml") else None
            )
            assert sso_settings["default_workspace_role_id"] == str(
                workspace_admin_role_id
            )
            assert sso_settings["default_workspace_ids"] == [str(auth.tenant_id)]


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
async def test_saml_sso_crud_failure_modes(
    mock_supabase_context_mgr: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 2)
    provider_ids = [response["id"] for response in responses]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    metadata_xmls = [f"<xml>{response['id']}</xml>" for response in responses]

    org_admin_role_id = await db_asyncpg.fetchval(
        f"SELECT id FROM roles WHERE name = '{OrganizationRoles.ADMIN.value}'"
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        other_tenant_id = authed_client.auth.tenant_id

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}' WHERE id = $1",
            auth.organization_id,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(other_tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 400, (
            "Should not be able to create with workspace outside org"
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 400, (
            "Should not be able to create with organization role"
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        sso_settings = response.json()
        assert sso_settings["provider_id"] == str(provider_ids[0])

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings['id']}",
            json={
                "default_workspace_ids": [],
            },
        )
        assert response.status_code == 422, response.text

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings['id']}",
            json={
                "default_workspace_ids": None,
            },
        )
        assert response.status_code == 422, response.text

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings['id']}",
            json={
                "default_workspace_ids": [str(other_tenant_id)],
            },
        )
        assert response.status_code == 400, (
            "Should not be able to set to workspace outside org"
        )

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings['id']}",
            json={
                "default_workspace_role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 400, (
            "Should not be able to set to organization role"
        )

        response = await client.patch(
            f"/orgs/current/sso-settings/{sso_settings['id']}",
        )
        assert response.status_code == 422, (
            "Should not be able to set with empty params"
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 409, (
            f"Duplicate SSO settings should not be allowed: {response.text}"
        )
        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[1],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 409, (
            f"Multiple SSO settings should not be allowed for the same organization: {response.text}"
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "metadata_xml": metadata_xmls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": "https://example.com/sso/saml/metadata/abc",
                "metadata_xml": "<xml><content></content></xml>",
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 422, (
            f"Only one parameter allowed: {response.text}"
        )
        response = await client.post(
            "/orgs/current/sso-settings",
        )
        assert response.status_code == 422, (
            f"At least one param is required: {response.text}"
        )

        response = await client.delete(f"/orgs/current/sso-settings/{uuid4()}")
        assert response.status_code == 404, response.text

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}' WHERE id = $1",
            authed_client.auth.organization_id,
        )
        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_xml": metadata_xmls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_xml": metadata_xmls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 409, (
            f"Duplicate SSO settings should not be allowed: {response.text}"
        )


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
async def test_sso_settings_from_login_slug(
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamsosso"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    response = await http_no_auth.get("/sso/settings/doesnotexist")
    assert response.status_code == 404

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        prev_sso_login_slug = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert prev_sso_login_slug is None
        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        sso_settings_created = response.json()

        sso_login_slug = await db_asyncpg.fetchval(
            "SELECT sso_login_slug FROM organizations WHERE id = $1",
            auth.organization_id,
        )
        assert sso_login_slug is not None

        response = await client.get(f"/sso/settings/{sso_login_slug}")
        assert response.status_code == 200
        sso_settings_list = response.json()
        assert len(sso_settings_list) == 1
        sso_settings = sso_settings_list[0]
        assert sso_settings["provider_id"] == str(responses[0]["id"])
        assert sso_settings["organization_id"] == str(auth.organization_id)
        assert sso_settings["organization_display_name"] == org_name

        # should respond with an empty list after deletion
        response = await client.delete(
            f"/orgs/current/sso-settings/{sso_settings_created['id']}"
        )
        assert response.status_code == 200, response.text
        response = await client.get(f"/sso/settings/{sso_login_slug}")
        assert response.status_code == 200
        assert response.json() == []


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@pytest.mark.parametrize(
    "jit_provisioning_enabled",
    [True, False],
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_email_verification_success(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    jit_provisioning_enabled: bool,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    org_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.ADMIN.value
    )
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2, jit_provisioning_enabled = $3 WHERE id = $1",
            auth.organization_id,
            org_name,
            jit_provisioning_enabled,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 1)

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        sso_settings = response.json()

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}

        # set up a user without an identity
        user_id = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id),
            email=f"test+{user_id}@langchain.dev",
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}

        supabase_jwt = jwt_for_user(
            user_id=user_id,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id)}"},
        )
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        # call to /tenants should NOT create a personal organization
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        response_json = response.json()
        assert response_json["email_confirmed_at"] is not None

        # call to /tenants should NOT create a personal organization
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()

        # should not be able to create new organization
        response = await http_no_auth.post(
            "/tenants",
            json={"display_name": "new sso org"},
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 400, response.text

        # verify JIT provisioning
        if jit_provisioning_enabled:
            assert len(tenants) == 1
            assert tenants[0]["is_personal"] is False
            org_members = await get_org_members(auth)
            assert len(org_members.members) == 2
            assert user_info.email in [m.email for m in org_members.members]
            org_member = next(
                m for m in org_members.members if m.email == user_info.email
            )
            assert org_member.role_id == org_user_role_id
            workspace_members = await get_tenant_members(auth)
            assert len(workspace_members.members) == 2
            assert user_info.email in [m.email for m in workspace_members.members]
            workspace_member = next(
                m for m in workspace_members.members if m.email == user_info.email
            )
            assert workspace_member.role_id == workspace_admin_role_id
            seat_events = await _list_seat_events_for_org(auth.organization_id)
            assert len(seat_events) == _NUM_SEAT_TYPES
            _assert_valid_transaction_chain_for_org(
                seat_events, [seat_txn.SeatChangeOperation.ADD_TO_ORGANIZATION]
            )
            _assert_reporting_status(seat_events)
            assert seat_events[0].seat_type == OrganizationRoles.ADMIN.value
            assert seat_events[0].seats_before == 1
            assert seat_events[0].seats_after == 1
            assert seat_events[0].pending_seats_before == 0
            assert seat_events[0].pending_seats_after == 0
            assert seat_events[1].seat_type == OrganizationRoles.USER.value
            assert seat_events[1].seats_before == 0
            assert seat_events[1].seats_after == 1
            assert seat_events[1].pending_seats_before == 0
            assert seat_events[1].pending_seats_after == 0

            # verify not part of other workspace
            for workspace in workspaces:
                tenant_members = await get_tenant_members(
                    AuthInfo(
                        tenant_id=workspace.id,
                        organization_id=auth.organization_id,
                        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
                    )
                )
                assert len(tenant_members.members) == 1
                assert len(tenant_members.pending) == 0
                assert user_info.email not in [m.email for m in tenant_members.members]

            # SSO user should not be able to delete SSO settings
            if not use_api_key:
                response = await client.patch(
                    f"/orgs/current/members/{org_member.id}",
                    json={
                        "role_id": str(org_admin_role_id),
                    },
                )
                assert response.status_code == 200, response.text
                response = await client.delete(
                    f"/orgs/current/sso-settings/{sso_settings['id']}",
                    headers={"Authorization": f"Bearer {supabase_jwt}"},
                )
                assert response.status_code == 400, response.text
        else:
            # verify JIT provisioning did not run if disabled
            assert len(tenants) == 0
            org_members = await get_org_members(auth)
            assert len(org_members.members) == 1
            assert user_info.email not in [m.email for m in org_members.members]
            seat_events = await _list_seat_events_for_org(auth.organization_id)
            assert len(seat_events) == 0


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_sso_only_organization(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("Cannot change role for API key")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    org_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.ADMIN.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        # Create a pending invite before setting to SSO only
        invited_user_id = uuid4()
        invited_user_email = f"test+{invited_user_id}@langchain.dev"
        invite_payload = {
            "email": invited_user_email,
            "workspace_ids": [str(auth.tenant_id)],
        }

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/members",
            json=invite_payload,
        )
        assert response.status_code == 200, response.text
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(invited_user_id),
            email=invited_user_email,
            provider="supabase:non-sso",
        )
        await ensure_user(user_info)
        invited_user_jwt = jwt_for_user(
            user_id=invited_user_id,
            user_email=user_info.email,
            user_full_name="",
        )
        invited_user_headers = {"Authorization": f"Bearer {invited_user_jwt}"}

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}

        # set up a user without an identity
        user_id = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id),
            email=f"test+{user_id}@langchain.dev",
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}

        supabase_jwt = jwt_for_user(
            user_id=user_id,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id)}"},
        )
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        # call to /tenants should NOT create a personal organization
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        response_json = response.json()
        assert response_json["email_confirmed_at"] is not None

        # call to /tenants should NOT create a personal organization
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 1
        assert tenants[0]["is_personal"] is False

        # get org member, verify pending invite is returned before SSO only is set
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert len(org_members.pending) == 1

        # Non-SSO user cannot set organization to SSO only
        response = await client.patch(
            "/orgs/current/login-methods",
            json={
                "sso_only": True,
            },
        )
        assert response.status_code == 400, response.text

        # set to org admin first to add permissions
        sso_org_headers = {
            "Authorization": f"Bearer {supabase_jwt}",
            "X-Organization-Id": str(auth.organization_id),
        }
        response = await client.patch(
            f"/orgs/current/members/{org_member.id}",
            json={
                "role_id": str(org_admin_role_id),
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.patch(
            "/orgs/current/login-methods",
            json={
                "sso_only": True,
            },
            headers=sso_org_headers,
        )
        assert response.status_code == 202, response.text
        response = await client.get("/orgs/current/info", headers=sso_org_headers)
        assert response.status_code == 200, response.text
        assert response.json()["sso_only"] is True

        # pending invite should not be returned
        org_members = await get_org_members(auth)
        assert len(org_members.pending) == 0
        response = await client.get("/orgs/pending", headers=invited_user_headers)
        assert response.status_code == 200, response.text
        assert len(response.json()) == 0

        # should not be able to invite user to organization or workspace
        new_user_email = f"test+{uuid4()}@langchain.dev"
        invite_payload = {
            "email": new_user_email,
            "workspace_ids": [str(auth.tenant_id)],
        }
        response = await client.post(
            "/orgs/current/members",
            json=invite_payload,
        )
        assert response.status_code == 403, response.text
        response = await client.post(
            "/orgs/current/members/batch",
            json=[invite_payload],
        )
        old_flag = settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED
        config.settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED = True
        response = await client.post(
            "/workspaces/current/members/batch",
            json=[invite_payload],
        )
        config.settings.FF_WORKSPACE_SCOPE_ORG_INVITES_ENABLED = old_flag

        # non-SSO user should not have access
        response = await client.get("/orgs/current/info")
        assert response.status_code == 403, response.text
        response = await client.get("/orgs")
        assert response.status_code == 200, response.text
        orgs = response.json()
        assert auth.organization_id not in [org["id"] for org in orgs]


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_sso_only_user_membership_crud(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("API key auth not supported for inviting to separate workspace")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 2)
    user_id_orig = uuid4()
    email = f"test+{user_id_orig}@langchain.dev"
    provider_id_orig = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamlinked"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 1)

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        # set up a user without an identity
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id_orig),
            email=email,
            provider="supabase:sso",
            saml_provider_id=str(provider_id_orig),
        )
        await ensure_user(user_info)

        supabase_jwt = jwt_for_user(
            user_id=user_id_orig,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id_orig)}"},
        )
        sso_auth_headers = {"Authorization": f"Bearer {supabase_jwt}"}
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id_orig),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        # call to /tenants should NOT create a personal organization
        # but should provision user and login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # minimal JIT provisioning verification
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert org_member.role_id == org_user_role_id
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 2
        assert user_info.email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == user_info.email
        )
        assert workspace_member.role_id == workspace_admin_role_id

        # should have 1 login method
        response = await http_no_auth.get(
            "/orgs/current/user/login-methods",
            headers={
                **sso_auth_headers,
                "X-Organization-Id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 1

        # test update and removal from both workspace and org, addition to workspace
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(user_id_orig),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        response = await client.get(
            "/workspaces/current/members",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        assert len(response.json()["members"]) == 2
        response = await client.patch(
            f"/workspaces/current/members/{workspace_member.id}",
            json={"role_id": str(workspace_viewer_role_id)},
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/workspaces/current/members/{workspace_member.id}",
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/orgs/current/members/{org_member.id}",
        )
        assert response.status_code == 200, response.text
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 1

        # email confirmation status should say unconfirmed.
        # avoid additional calls with sso headers to avoid the provider_user being reprovisioned
        response = await client.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id_orig),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}

    # Removed user should be able to login to different org and SAML provider
    provider_id_new = responses[1]["id"]
    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            "new linked",
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[1],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        # set up a user without an identity with a new user ID
        # and the same email address
        user_id = uuid4()

        supabase_jwt = jwt_for_user(
            user_id=user_id,
            user_email=email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id_new)}"},
        )
        sso_auth_headers_new = {"Authorization": f"Bearer {supabase_jwt}"}

        # call to /tenants should NOT create a personal organization
        # but should provision login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers_new)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": email,
                "saml_provider_id": str(provider_id_new),
            },
            headers=sso_auth_headers_new,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload = args[0][0]
        assert payload["email"] == email
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # minimal JIT provisioning verification
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == email)
        assert org_member.role_id == org_user_role_id
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 2
        assert email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == email
        )
        assert workspace_member.role_id == workspace_admin_role_id

        # should have 1 login method
        response = await http_no_auth.get(
            "/orgs/current/user/login-methods",
            headers={
                **sso_auth_headers_new,
                "X-Organization-Id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, response.text
        res = response.json()
        assert len(res) == 1
        assert res[0]["provider_user_id"] == str(user_id)


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_sso_first_membership_crud(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("API key auth not supported for inviting to separate workspace")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamlinked"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 1)

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        # set up a user without an identity
        user_id_sso = uuid4()
        user_name = "SSO user"
        email = f"test+{user_id_sso}@langchain.dev"
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id_sso),
            full_name=user_name,
            email=email,
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        sso_jwt = jwt_for_user(
            user_id=user_id_sso,
            user_email=user_info.email,
            user_full_name=user_name,
            app_metadata={"provider": f"sso:{str(provider_id)}"},
        )
        sso_auth_headers = {"Authorization": f"Bearer {sso_jwt}"}
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        # call to /tenants should NOT create a personal organization
        # but should provision user and login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # minimal JIT provisioning verification
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert org_member.role_id == org_user_role_id
        assert set([p.provider_user_id for p in org_member.linked_login_methods]) == {
            user_id_sso,
        }
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 2
        assert user_info.email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == user_info.email
        )
        assert workspace_member.role_id == workspace_admin_role_id
        assert set(
            [p.provider_user_id for p in workspace_member.linked_login_methods]
        ) == {
            user_id_sso,
        }

        # link a new non-SSO user
        user_id_non_sso = uuid4()
        non_sso_jwt = jwt_for_user(
            user_id=user_id_non_sso,
            user_email=user_info.email,
            user_full_name=user_name,
        )
        non_sso_auth_headers = {"Authorization": f"Bearer {non_sso_jwt}"}
        response = await http_no_auth.get("/tenants", headers=non_sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 1

        # should have 2 login methods
        response = await http_no_auth.get(
            "/orgs/current/user/login-methods",
            headers={
                **non_sso_auth_headers,
                "X-Organization-Id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 2

        # should not be able to re-invite user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": user_info.email,
            },
        )
        assert response.status_code == 409, response.text

        # verify linked user IDs
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert org_member.role_id == org_user_role_id
        assert set([p.provider_user_id for p in org_member.linked_login_methods]) == {
            user_id_non_sso,
            user_id_sso,
        }
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 2
        assert user_info.email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == user_info.email
        )
        assert workspace_member.role_id == workspace_admin_role_id
        assert set(
            [p.provider_user_id for p in workspace_member.linked_login_methods]
        ) == {
            user_id_non_sso,
            user_id_sso,
        }

        # Verify both logins can create and view feedback
        run_id = await create_test_run(auth, uuid4())
        response = await client.post(
            "/feedback",
            json={
                "run_id": run_id.hex,
                "score": 0.5,
                "key": "test_app",
                "feedback_source": {
                    "type": "app",
                },
            },
            headers=non_sso_auth_headers,
        )
        assert response.status_code == 200
        response = await client.post(
            "/feedback",
            json={
                "run_id": run_id.hex,
                "score": 0.7,
                "key": "test_app",
                "feedback_source": {
                    "type": "app",
                },
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 200

        await wait_until_task_queue_empty()

        # SSO/Non-sso and org owner should all have same view of feedback
        for headers in [sso_auth_headers, non_sso_auth_headers, {}]:
            response = await client.get(
                "/feedback?include_user_names=true", headers=headers
            )
            assert response.status_code == 200
            assert len(response.json()) == 2
            res = response.json()
            feedbacks = [schemas.FeedbackSchema(**f) for f in res]
            assert all(f.feedback_source is not None for f in feedbacks)
            assert all(f.feedback_source.user_id is not None for f in feedbacks)  # type: ignore[union-attr]
            assert set([user_id_non_sso, user_id_sso]) == set(
                f.feedback_source.user_id  # type: ignore[union-attr]
                for f in feedbacks
            )
            assert all(f.feedback_source.user_name == user_name for f in feedbacks)  # type: ignore[union-attr]

        # User name should prefer exact match of user_id
        non_sso_user_name = "I am non SSO"
        await db_asyncpg.execute(
            "update provider_users set full_name = $1 where provider_user_id = $2",
            non_sso_user_name,
            user_id_non_sso,
        )
        response = await client.get(
            "/feedback?include_user_names=true",
        )
        assert response.status_code == 200
        assert len(response.json()) == 2
        res = response.json()
        feedbacks = [schemas.FeedbackSchema(**f) for f in res]
        assert all(f.feedback_source is not None for f in feedbacks)
        assert set([user_id_non_sso, user_id_sso]) == set(
            f.feedback_source.user_id  # type: ignore[union-attr]
            for f in feedbacks
        )
        sso_feedback = next(
            f
            for f in feedbacks
            if f.feedback_source.user_id == user_id_sso  # type: ignore[union-attr]
        )
        non_sso_feedback = next(
            f
            for f in feedbacks
            if f.feedback_source.user_id == user_id_non_sso  # type: ignore[union-attr]
        )
        assert user_name == sso_feedback.feedback_source.user_name  # type: ignore[union-attr]
        assert non_sso_user_name == non_sso_feedback.feedback_source.user_name  # type: ignore[union-attr]

        # Empty user name
        await db_asyncpg.execute(
            "update provider_users set full_name = NULL where email = $1",
            email,
        )
        response = await client.get(
            "/feedback?include_user_names=true",
        )
        assert response.status_code == 200
        assert len(response.json()) == 2
        res = response.json()
        feedbacks = [schemas.FeedbackSchema(**f) for f in res]
        assert all(f.feedback_source is not None for f in feedbacks)
        assert all(f.feedback_source.user_name is None for f in feedbacks)  # type: ignore[union-attr]

        # should be able to add to other workspace using org_identity_id
        response = await client.post(
            "/workspaces/current/members",
            json={
                "org_identity_id": str(org_member.id),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        new_ws_member = response.json()
        response = await client.delete(
            f"/workspaces/current/members/{new_ws_member['id']}",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text

        # test update and removal from both workspace and org, addition to workspace
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(user_id_non_sso),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, (
            f"Should be able to add by later user ID: {response.text}"
        )
        new_ws_member = response.json()
        response = await client.delete(
            f"/workspaces/current/members/{new_ws_member['id']}",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )

        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(user_id_sso),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        response = await client.get(
            "/workspaces/current/members",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        assert len(response.json()["members"]) == 2
        response = await client.patch(
            f"/workspaces/current/members/{workspace_member.id}",
            json={"role_id": str(workspace_viewer_role_id)},
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/workspaces/current/members/{workspace_member.id}",
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/orgs/current/members/{org_member.id}",
        )
        assert response.status_code == 200, response.text
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 1

        # check email confirmation status
        response = await client.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_non_sso_first_membership_crud(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("API key auth not supported for inviting to separate workspace")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamlinked"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    workspace_viewer_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_VIEWER'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        workspaces = await _create_workspaces(client, auth.organization_id, 1)

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        user_id_non_sso = uuid4()
        new_user_email = f"test{user_id_non_sso}@langchain.dev"
        non_sso_jwt = jwt_for_user(
            user_id=user_id_non_sso,
            user_email=new_user_email,
            user_full_name="",
        )
        new_user_headers = {
            "Authorization": f"Bearer {non_sso_jwt}",
        }

        # create a pending invite for this user and claim
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "workspace_ids": [str(auth.tenant_id)],
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # set up an SSO user without an identity
        user_id_sso = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id_sso),
            email=new_user_email,
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        sso_jwt = jwt_for_user(
            user_id=user_id_sso,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id)}"},
        )
        sso_auth_headers = {"Authorization": f"Bearer {sso_jwt}"}

        # call to /tenants should NOT create a personal organization
        # but should provision user and login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        # call to /tenants should NOT create a personal organization
        # but should provision user and login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # minimal JIT provisioning verification
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert org_member.role_id == org_user_role_id
        assert set([p.provider_user_id for p in org_member.linked_login_methods]) == {
            user_id_non_sso,
            user_id_sso,
        }
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 2
        assert user_info.email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == user_info.email
        )
        assert workspace_member.role_id == workspace_admin_role_id
        assert set(
            [p.provider_user_id for p in workspace_member.linked_login_methods]
        ) == {
            user_id_non_sso,
            user_id_sso,
        }

        # should have 2 login methods
        response = await http_no_auth.get(
            "/orgs/current/user/login-methods",
            headers={
                **sso_auth_headers,
                "X-Organization-Id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 2

        # should not be able to re-invite user
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": user_info.email,
            },
        )
        assert response.status_code == 409, response.text

        # should be able to add to other workspace using org_identity_id
        response = await client.post(
            "/workspaces/current/members",
            json={
                "org_identity_id": str(org_member.id),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        new_ws_member = response.json()
        response = await client.delete(
            f"/workspaces/current/members/{new_ws_member['id']}",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text

        # test update and removal from both workspace and org, addition to workspace
        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(user_id_sso),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, (
            f"Should be able to add by later user ID: {response.text}"
        )
        new_ws_member = response.json()
        response = await client.delete(
            f"/workspaces/current/members/{new_ws_member['id']}",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text

        response = await client.post(
            "/workspaces/current/members",
            json={
                "user_id": str(user_id_non_sso),
                "role_id": str(workspace_viewer_role_id),
            },
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        response = await client.get(
            "/workspaces/current/members",
            headers={"X-Tenant-ID": str(workspaces[0].id)},
        )
        assert response.status_code == 200, response.text
        assert len(response.json()["members"]) == 2
        response = await client.patch(
            f"/workspaces/current/members/{workspace_member.id}",
            json={"role_id": str(workspace_viewer_role_id)},
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/workspaces/current/members/{workspace_member.id}",
        )
        assert response.status_code == 200, response.text
        response = await client.delete(
            f"/orgs/current/members/{org_member.id}",
        )
        assert response.status_code == 200, response.text
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 1

        # check email confirmation status
        response = await client.post(
            "/sso/email-verification/status",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        assert response.json() == {"email_confirmed_at": None}


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_linked_sso_user_new_workspace(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("API key auth not supported for inviting to separate workspace")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "sso new workspace"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.USER.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    org_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.ADMIN.value
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        user_id_non_sso = uuid4()
        new_user_email = f"test{user_id_non_sso}@langchain.dev"
        non_sso_jwt = jwt_for_user(
            user_id=user_id_non_sso,
            user_email=new_user_email,
            user_full_name="",
        )
        new_user_headers = {
            "Authorization": f"Bearer {non_sso_jwt}",
        }

        # create a pending invite for this user and claim
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "workspace_ids": [str(auth.tenant_id)],
            },
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            f"/orgs/pending/{auth.organization_id}/claim",
            headers=new_user_headers,
        )
        assert response.status_code == 200

        # set up an SSO user without an identity
        user_id_sso = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id_sso),
            email=new_user_email,
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        user = await ensure_user(user_info)

        sso_jwt = jwt_for_user(
            user_id=user_id_sso,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{str(provider_id)}"},
        )
        sso_auth_headers = {"Authorization": f"Bearer {sso_jwt}"}

        # call to /tenants should NOT create a personal organization
        # but should provision user and login method
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # create a new workspace
        workspaces = await _create_workspaces(client, auth.organization_id, 1)
        assert len(workspaces) == 1
        workspace = workspaces[0]

        # check that SSO user does NOT have access to new workspace, only the old,
        # since they're an organization user
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 2
        assert user_info.email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == user_info.email)
        assert org_member.role_id == org_user_role_id
        assert set([p.provider_user_id for p in org_member.linked_login_methods]) == {
            user_id_non_sso,
            user_id_sso,
        }
        old_workspace_members = await get_tenant_members(auth)
        assert len(old_workspace_members.members) == 2
        assert user_info.email in [m.email for m in old_workspace_members.members]
        old_workspace_member = next(
            m for m in old_workspace_members.members if m.email == user_info.email
        )
        assert old_workspace_member.role_id == workspace_admin_role_id
        assert set(
            [p.provider_user_id for p in old_workspace_member.linked_login_methods]
        ) == {
            user_id_non_sso,
            user_id_sso,
        }
        workspace_members = await get_tenant_members(
            AuthInfo(
                tenant_id=workspace.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
            )
        )
        assert len(workspace_members.members) == 1
        assert user_info.email not in [m.email for m in workspace_members.members]
        org_auth_new_user = authed_client.org_auth.model_copy(
            update={
                "user_id": user_id_sso,
                "ls_user_id": user["ls_user_id"],
                "identity_id": org_member.id,
            }
        )
        workspaces_for_user = await list_tenants_for_identity_in_org(
            org_auth_new_user, False
        )
        assert len(workspaces_for_user) == 1

        # should have 2 login methods
        response = await http_no_auth.get(
            "/orgs/current/user/login-methods",
            headers={
                **sso_auth_headers,
                "X-Organization-Id": str(auth.organization_id),
            },
        )
        assert response.status_code == 200, response.text
        assert len(response.json()) == 2

        # change role and create a new workspace, should have access
        response = await client.patch(
            f"/orgs/current/members/{org_member.id}",
            json={"role_id": str(org_admin_role_id)},
        )
        assert response.status_code == 200, response.text

        workspaces_next = await _create_workspaces(client, auth.organization_id, 1)
        assert len(workspaces_next) == 1
        workspace_next = workspaces_next[0]
        workspace_members = await get_tenant_members(
            AuthInfo(
                tenant_id=workspace_next.id,
                organization_id=auth.organization_id,
                tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
            )
        )
        assert len(workspace_members.members) == 2
        assert user_info.email in [m.email for m in workspace_members.members]
        workspaces_for_user = await list_tenants_for_identity_in_org(
            org_auth_new_user, False
        )
        assert len(workspaces_for_user) == 3


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_linked_login_methods_seat_count(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    org_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.ADMIN.value
    )
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        sso_user_id = uuid4()
        supabase_jwt = jwt_for_user(
            user_id=sso_user_id,
            user_email=auth.user_email,
            user_full_name=auth.user_full_name,
            app_metadata={"provider": f"sso:{provider_id}"},
        )

        # call to /tenants should NOT create a personal organization
        # but should link the SSO login method to the existing non-SSO
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == auth.user_email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        response = await http_no_auth.post(
            "/sso/email-verification/status",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
        )
        assert response.status_code == 200, response.text
        response_json = response.json()
        assert response_json["email_confirmed_at"] is not None

        # call to /tenants should NOT create a personal organization
        # but the user should now have access to the workspace
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 1
        assert tenants[0]["is_personal"] is False

        # verify JIT provisioning - should NOT change membership,
        # since user already had access as non-SSO
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 1
        assert auth.user_email in [m.email for m in org_members.members]
        org_member = next(m for m in org_members.members if m.email == auth.user_email)
        assert org_member.role_id == org_admin_role_id
        workspace_members = await get_tenant_members(auth)
        assert len(workspace_members.members) == 1
        assert auth.user_email in [m.email for m in workspace_members.members]
        workspace_member = next(
            m for m in workspace_members.members if m.email == auth.user_email
        )
        assert workspace_member.role_id == workspace_admin_role_id
        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 0
        seats = await get_seat_count_for_org(
            cast(UUID, auth.organization_id), db_asyncpg
        )
        assert OrganizationRoles.USER.value not in [s.role_name for s in seats]
        assert OrganizationRoles.ADMIN.value in [s.role_name for s in seats]
        admin_seats = next(
            s for s in seats if s.role_name == OrganizationRoles.ADMIN.value
        )
        assert admin_seats.count == 1
        assert admin_seats.count_pending == 0


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_cannot_provision_sso_user_over_seat_count(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            'UPDATE organizations SET config = config || \'{"can_use_saml_sso": true, "max_identities": 1}\', display_name = $2 WHERE id = $1',
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        sso_user_id = uuid4()
        email = f"test{sso_user_id}@langchain.dev"
        supabase_jwt = jwt_for_user(
            user_id=sso_user_id,
            user_email=email,
            user_full_name="",
            app_metadata={"provider": f"sso:{provider_id}"},
        )

        # call to /tenants should NOT create a personal organization
        # but should link the SSO login method to the existing non-SSO
        response = await http_no_auth.get(
            "/tenants", headers={"Authorization": f"Bearer {supabase_jwt}"}
        )
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 400, response.text

        # user should not have access
        org_members = await get_org_members(auth)
        assert len(org_members.members) == 1
        assert email not in [m.email for m in org_members.members]
        seat_events = await _list_seat_events_for_org(auth.organization_id)
        assert len(seat_events) == 0
        seats = await get_seat_count_for_org(
            cast(UUID, auth.organization_id), db_asyncpg
        )
        assert OrganizationRoles.USER.value not in [s.role_name for s in seats]
        assert OrganizationRoles.ADMIN.value in [s.role_name for s in seats]
        admin_seats = next(
            s for s in seats if s.role_name == OrganizationRoles.ADMIN.value
        )
        assert admin_seats.count == 1
        assert admin_seats.count_pending == 0


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_linked_login_methods_api_keys(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("service keys can't be used to create API keys")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        ws_org_headers = {
            "X-Organization-Id": str(auth.organization_id),
            "X-Tenant-Id": str(auth.tenant_id),
        }

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        sso_user_id = uuid4()
        supabase_jwt = jwt_for_user(
            user_id=sso_user_id,
            user_email=auth.user_email,
            user_full_name=auth.user_full_name,
            app_metadata={"provider": f"sso:{provider_id}"},
        )

        # call to /tenants should NOT create a personal organization
        # but should link the SSO login method to the existing non-SSO
        sso_auth_headers = {"Authorization": f"Bearer {supabase_jwt}"}
        sso_ws_headers = {
            **sso_auth_headers,
            **ws_org_headers,
        }
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == auth.user_email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # Create PATs with both login methods
        response = await client.post(
            "/api-key/current",
            json={"description": "non-sso"},
        )
        assert response.status_code == 200, response.text
        non_sso_api_key = response.json()
        response = await http_no_auth.post(
            "/api-key/current",
            json={"description": "sso"},
            headers=sso_ws_headers,
        )
        assert response.status_code == 200, response.text
        sso_api_key = response.json()
        api_keys_created = [non_sso_api_key, sso_api_key]

        # Both PATs should be present and valid
        response_sso = await http_no_auth.get(
            "/api-key/current", headers=sso_ws_headers
        )
        assert response_sso.status_code == 200, response.text
        response_non_sso = await http_no_auth.get(
            "/api-key/current", headers=sso_ws_headers
        )
        assert response_non_sso.status_code == 200, response.text
        assert response_sso.json() == response_non_sso.json()
        api_keys = response_sso.json()
        assert len(api_keys) == 2
        {k["id"] for k in api_keys} == {k["id"] for k in api_keys_created}
        for api_key in [non_sso_api_key, sso_api_key]:
            key = api_key["key"]
            response = await http_no_auth.get(
                "/sessions", headers={"X-API-Key": key, **ws_org_headers}
            )
            assert response.status_code == 200, response.text


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_linked_login_methods_membership(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    if use_api_key:
        pytest.skip("service keys can't be used to create API keys")
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )
    org_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = $1", OrganizationRoles.ADMIN.value
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        ws_org_headers = {
            "X-Organization-Id": str(auth.organization_id),
            "X-Tenant-Id": str(auth.tenant_id),
        }

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        sso_user_id = uuid4()
        supabase_jwt = jwt_for_user(
            user_id=sso_user_id,
            user_email=auth.user_email,
            user_full_name=auth.user_full_name,
            app_metadata={"provider": f"sso:{provider_id}"},
        )

        # call to /tenants should NOT create a personal organization
        # but should link the SSO login method to the existing non-SSO
        sso_auth_headers = {"Authorization": f"Bearer {supabase_jwt}"}
        sso_ws_headers = {
            **sso_auth_headers,
            **ws_org_headers,
        }
        response = await http_no_auth.get("/tenants", headers=sso_auth_headers)
        assert response.status_code == 200, response.text
        tenants = response.json()
        assert len(tenants) == 0

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": auth.user_email,
                "saml_provider_id": str(provider_id),
            },
            headers=sso_auth_headers,
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == auth.user_email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        # Members should have expected information and should be
        # the same regardless of login method
        response = await client.get("/orgs/current/members")
        assert response.status_code == 200, response.text
        org_members_res = response.json()
        response = await http_no_auth.get(
            "/orgs/current/members", headers=sso_ws_headers
        )
        assert response.status_code == 200, response.text
        assert response.json() == org_members_res
        org_members = schemas.OrganizationMembers.model_validate(org_members_res)
        assert len(org_members.members) == 1
        org_member = org_members.members[0]
        assert org_member.email == auth.user_email
        assert org_member.organization_id == auth.organization_id
        assert org_member.role_id == org_admin_role_id
        assert (
            org_member.user_id == auth.user_id
        )  # should be the non-SSO user's ID b/c created first
        assert org_member.ls_user_id == auth.ls_user_id
        assert set([p.provider_user_id for p in org_member.linked_login_methods]) == {
            auth.user_id,
            sso_user_id,
        }
        response = await http_no_auth.get(
            "/workspaces/current/members", headers=sso_ws_headers
        )
        assert response.status_code == 200, response.text
        ws_members_res = response.json()
        ws_members = schemas.TenantMembers.model_validate(ws_members_res)
        assert len(ws_members.members) == 1
        ws_member = ws_members.members[0]
        assert ws_member.email == auth.user_email
        assert ws_member.organization_id == auth.organization_id
        assert ws_member.role_id == workspace_admin_role_id
        assert set([p.provider_user_id for p in ws_member.linked_login_methods]) == {
            auth.user_id,
            sso_user_id,
        }


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_email_verification_failure_modes(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "iamverified"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text
        sso_settings = response.json()

        # set up a user without an identity
        user_id = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id),
            email=f"test+{user_id}@langchain.dev",
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        # manually set email confirmed
        await db_asyncpg.execute(
            "UPDATE provider_users SET email_confirmed_at = NOW() WHERE provider_user_id = $1",
            user_id,
        )

        supabase_jwt = jwt_for_user(
            user_id=user_id,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": f"sso:{provider_id}"},
        )
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 400, (
            f"Should not be able to send email for already confirmed user: {response.text}"
        )

        # manually unset email confirmed
        await db_asyncpg.execute(
            "UPDATE provider_users SET email_confirmed_at = NULL WHERE provider_user_id = $1",
            user_id,
        )

        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {supabase_jwt}"},
        )
        assert response.status_code == 202, response.text
        assert mock_send_sso_email_verifications_batch.called
        args, _ = mock_send_sso_email_verifications_batch.call_args
        payload: SSOEmailVerificationPayload = args[0][0]
        assert payload["email"] == user_info.email
        assert payload["org"]["display_name"] == org_name
        confirmation_url = payload["confirmation_url"]
        token = confirmation_url.split("token=")[-1]
        assert token is not None

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 200, response.text

        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 400, (
            f"Should not be able to confirm twice: {response.text}"
        )

        # delete the SSO settings, then should not be able to find user
        response = await client.delete(
            f"/orgs/current/sso-settings/{sso_settings['id']}"
        )
        assert response.status_code == 200, response.text
        response = await http_no_auth.post(
            "/sso/email-verification/confirm",
            json={
                "token": token,
            },
        )
        assert response.status_code == 404, (
            f"Should not be able to confirm without SSO settings: {response.text}"
        )


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
@patch("app.api.endpoints.orgs.supabase_client")
@patch("app.api.endpoints.auth.send_sso_email_verifications_batch")
async def test_email_send_failure_invalid_provider(
    mock_send_sso_email_verifications_batch: AsyncMock,
    mock_supabase_context_mgr: Mock,
    http_no_auth: AsyncClient,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    _, responses = _setup_supabase_client_mock(mock_supabase_context_mgr, 1)
    provider_id = responses[0]["id"]
    metadata_urls = [
        f"https://example.com/sso/saml/{response['id']}/metadata"
        for response in responses
    ]
    org_name = "invalid provider"
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client
        auth = authed_client.auth

        await db_asyncpg.execute(
            "UPDATE organizations SET config = config || '{\"can_use_saml_sso\": true}', display_name = $2 WHERE id = $1",
            auth.organization_id,
            org_name,
        )

        response = await client.post(
            "/orgs/current/sso-settings",
            json={
                "metadata_url": metadata_urls[0],
                "default_workspace_ids": [str(auth.tenant_id)],
                "default_workspace_role_id": str(workspace_admin_role_id),
            },
        )
        assert response.status_code == 201, response.text

        # set up a user without an identity
        user_id = uuid4()
        user_info = DecodedUserInfo(
            sub="fake",
            id=str(user_id),
            email=f"test+{user_id}@langchain.dev",
            provider="supabase:sso",
            saml_provider_id=str(provider_id),
        )
        await ensure_user(user_info)

        invalid_jwt = jwt_for_user(
            user_id=user_id,
            user_email=user_info.email,
            user_full_name="",
            app_metadata={"provider": "sso:notauuid"},
        )
        response = await http_no_auth.post(
            "/sso/email-verification/send",
            json={
                "email": user_info.email,
                "saml_provider_id": str(provider_id),
            },
            headers={"Authorization": f"Bearer {invalid_jwt}"},
        )
        response.status_code == 403
        assert not mock_send_sso_email_verifications_batch.called


@pytest.mark.skipif(
    not config.settings.AUTH_TYPE == "supabase",
    reason="SSO is only enabled for Supabase auth",
)
async def test_large_saml_metadata_xml(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    workspace_admin_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'"
    )

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        # Should succeed
        await db_asyncpg.execute(
            """
            INSERT INTO saml_providers (organization_id, provider_id, metadata_xml, default_workspace_ids, default_workspace_role_id)
            VALUES ($1, $2, $3, $4, $5)
            """,
            authed_client.auth.organization_id,
            uuid4(),
            "a" * 20000,
            [authed_client.auth.tenant_id],
            workspace_admin_role_id,
        )


@pytest.mark.skipif(not _org_auth_enabled, reason="new auth only enabled for supabase")
async def test_claim_invite_no_auth_header(
    db_asyncpg: asyncpg.Connection,
    http_no_auth: AsyncClient,
    use_api_key: bool,
) -> None:
    """Test that claiming an invite without an Authorization header fails."""
    if use_api_key:
        pytest.skip("Api keys are single tenant")

    new_user_email = "<EMAIL>"
    org_user_role_id = await db_asyncpg.fetchval(
        "SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'"
    )

    async with fresh_tenant_client(
        db_asyncpg, use_api_key, set_org_id=True
    ) as authed_client:
        client = authed_client.client
        auth = authed_client.auth
        org_id = auth.organization_id

        # invite user to org and workspace
        response = await client.post(
            "/orgs/current/members",
            json={
                "email": new_user_email,
                "role_id": str(org_user_role_id),
                "workspace_ids": [str(auth.tenant_id)],
            },
        )
        assert response.status_code == 200, response.text

        # check that user is pending on workspace
        workspace_auth = AuthInfo(
            tenant_id=auth.tenant_id,
            organization_id=auth.organization_id,
            tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
        )
        tenant_members = await get_tenant_members(workspace_auth)
        assert new_user_email in [member.email for member in tenant_members.pending]

        # Test claiming invite without Authorization header
        response = await http_no_auth.post(
            f"/orgs/pending/{org_id}/claim",
            headers={},  # Empty headers - no Authorization
        )
        assert response.status_code == 403, response.text

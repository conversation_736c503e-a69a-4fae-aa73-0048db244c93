import pytest
from httpx import AsyncClient

from app import config

CORS_HEADERS = {
    "accept": "*/*",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9",
    "access-control-request-headers": "authorization,x-user-id",
    "access-control-request-method": "GET",
    "cache-control": "no-cache",
    "connection": "keep-alive",
    "host": "localhost:1984",
    "origin": "http://example.com",
    "pragma": "no-cache",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-site",
    "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
}


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
@pytest.mark.parametrize(
    "path",
    [
        "/public/fake-token/run|GET",
        "/public/fake-token/run/fake-id|GET",
        "/public/fake-token/runs/query|POST",
        "/public/fake-token/feedbacks|GET",
        "/public/fake-token/datasets|GET",
        "/public/fake-token/examples/count|GET",
        "/public/fake-token/examples|GET",
        "/public/fake-token/datasets/sessions|GET",
        "/public/fake-token/datasets/sessions-bulk|GET",
        "/public/fake-token/examples/runs|POST",
        "/public/fake-token/datasets/runs/delta|POST",
        "/public/fake-token/datasets/runs/query|POST",
        "/public/fake-token/datasets/runs/generate-query|POST",
        "/public/fake-token/datasets/runs/stats|POST",
        "/public/fake-token/datasets/runs/fake-id|GET",
        "/public/fake-token/datasets/feedback|GET",
        "/public/fake-token/datasets/comparative|GET",
        "/feedback/tokens/fake-token|GET",
        "/feedback/tokens/fake-token|POST",
    ],
)
@pytest.mark.parametrize("origin", ["http://localhost:3000", "http://example.com"])
async def test_cors_public(
    http_no_auth: AsyncClient,
    origin: str,
    path: str,
):
    """Test that CORS allows any origin for public endpoints."""
    headers = CORS_HEADERS.copy()
    headers["origin"] = origin
    endpoint, method = path.split("|")
    headers["access-control-request-method"] = method
    response = await http_no_auth.options(endpoint, headers=headers)
    assert response.status_code == 200
    assert response.headers["Access-Control-Allow-Origin"] == headers["origin"]
    assert (
        response.headers["Access-Control-Allow-Headers"]
        == CORS_HEADERS["access-control-request-headers"]
    )
    assert (
        response.headers["Access-Control-Allow-Methods"]
        == "DELETE, GET, HEAD, OPTIONS, PATCH, POST, PUT"
    )
    assert response.headers["Access-Control-Allow-Credentials"] == "true"


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="single tenant")
async def test_cors_non_public(
    http_no_auth: AsyncClient,
):
    # non-public endpoints should fail with unknown origin
    headers = CORS_HEADERS.copy()
    headers["origin"] = "http://unknown.com"
    response = await http_no_auth.options("/sessions", headers=headers)
    assert response.status_code == 400

    # non-public endpoints should fail with unknown origin
    response = await http_no_auth.options("/feedback", headers=headers)
    assert response.status_code == 400

import unittest
from typing import AsyncItera<PERSON>, List, TypeVar

from app.models.datasets.utils.sse import <PERSON><PERSON><PERSON><PERSON>, BytesLineDecoder, SSEDecoder

T = TypeVar("T")


async def gather_async(iterator: AsyncIterator[T]) -> List[T]:
    """Collect all items from an async iterator into a list."""
    results: List[T] = []
    async for chunk in iterator:
        results.append(chunk)
    return results


def create_stream(chunks: List[bytes]) -> AsyncIterator[bytes]:
    """Create an async iterator from a list of bytes chunks."""

    async def stream():
        for chunk in chunks:
            yield chunk

    return stream()


class TestBytesLineDecoder(unittest.TestCase):
    def setUp(self):
        self.decoder = BytesLineDecoder()

    def _decode_all(self, chunks: List[bytes]) -> List[BytesLike]:
        """Helper to decode all chunks and flush."""
        results = []
        for chunk in chunks:
            results.extend(self.decoder.decode(chunk))
        results.extend(self.decoder.flush())
        return results

    def test_handles_single_line_with_newline(self):
        input_chunks = [b"hello\n"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], b"hello")

    def test_handles_multiple_lines(self):
        input_chunks = [b"line1\nline2\nline3\n"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 3)
        self.assertEqual(results[0], b"line1")
        self.assertEqual(results[1], b"line2")
        self.assertEqual(results[2], b"line3")

    def test_handles_split_chunks(self):
        input_chunks = [b"li", b"ne1\nli", b"ne2\n"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], b"line1")
        self.assertEqual(results[1], b"line2")

    def test_handles_cr_lf_line_endings(self):
        input_chunks = [b"line1\r\nline2\r\n"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], b"line1")
        self.assertEqual(results[1], b"line2")

    def test_handles_split_cr_lf(self):
        input_chunks = [b"line1\r", b"\nline2\r\n"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0], b"line1")
        self.assertEqual(results[1], b"line2")

    def test_handles_stale_line(self):
        input_chunks = [b"hello"]
        results = self._decode_all(input_chunks)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0], b"hello")


class TestSSEDecoder(unittest.TestCase):
    def setUp(self):
        self.line_decoder = BytesLineDecoder()
        self.sse_decoder = SSEDecoder()

    def _decode_all(self, lines: List[str]) -> List[dict]:
        """Helper to decode all lines and get SSE events."""
        results = []

        # First decode lines
        decoded_lines = []
        for line in lines:
            decoded_lines.extend(self.line_decoder.decode(line.encode("utf-8")))
        decoded_lines.extend(self.line_decoder.flush())

        # Then decode SSE events
        for line in decoded_lines:
            event = self.sse_decoder.decode(line)
            if event is not None:
                results.append(event)

        # Handle any final event in the buffer
        final_event = self.sse_decoder.decode(b"")
        if final_event is not None:
            results.append(final_event)

        return results

    def test_decodes_simple_event(self):
        input_lines = [
            "event: test\n",
            'data: {"message": "hello"}\n',
            "\n",
        ]
        results = self._decode_all(input_lines)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].event, "test")
        self.assertEqual(results[0].data, {"message": "hello"})

    def test_ignores_comments(self):
        input_lines = [
            ": this is a comment\n",
            "event: test\n",
            'data: {"message": "hello"}\n',
            "\n",
        ]
        results = self._decode_all(input_lines)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].event, "test")
        self.assertEqual(results[0].data, {"message": "hello"})

    def test_handles_multiple_events(self):
        input_lines = [
            "event: test1\n",
            'data: {"message": "hello"}\n',
            "\n",
            "event: test2\n",
            'data: {"message": "world"}\n',
            "\n",
        ]
        results = self._decode_all(input_lines)

        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].event, "test1")
        self.assertEqual(results[0].data, {"message": "hello"})
        self.assertEqual(results[1].event, "test2")
        self.assertEqual(results[1].data, {"message": "world"})

    def test_end_event_without_data(self):
        input_lines = ["event: test\n", "\n"]
        results = self._decode_all(input_lines)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].event, "test")
        self.assertEqual(results[0].data, None)

    def test_end_event_without_newline(self):
        input_lines = ["event: end"]
        results = self._decode_all(input_lines)

        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].event, "end")
        self.assertEqual(results[0].data, None)


if __name__ == "__main__":
    unittest.main()

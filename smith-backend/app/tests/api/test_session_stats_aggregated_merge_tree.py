from contextlib import ExitStack
from datetime import datetime, timedelta, timezone
from typing import Any, Awaitable, Callable
from unittest.mock import patch
from uuid import uuid4

import asyncpg
import pytest

from app import config
from app.models.runs.utils import calculate_amt_time_windows
from app.tests.utils import fresh_tenant_client, post_runs


def _get_llm_run(run_id, child_run_id, start_time, end_time, session_id, dotted_order):
    return {
        "name": "LLMChain",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "extra": {"foo": "bar"},
        "error": None,
        "execution_order": 1,
        "inputs": {"input": "hello how are you?"},
        "outputs": {
            "generations": [[{"text": "39,566,248"}]],
            "llm_output": {
                "token_usage": {
                    "prompt_tokens": 25,
                    "completion_tokens": 100,
                },
            },
        },
        "session_id": str(session_id),
        "parent_run_id": str(run_id),
        "run_type": "llm",
        "id": str(child_run_id),
        "trace_id": str(run_id),
        "dotted_order": f"{dotted_order}.{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{child_run_id}",
    }


def _get_chain_root_run(run_id, start_time, end_time, session_id):
    return {
        "name": "AgentExecutor",
        "start_time": start_time.isoformat(),
        "end_time": end_time.isoformat(),
        "extra": {"foo": "bar"},
        "error": None,
        "dotted_order": f"{start_time.strftime('%Y%m%dT%H%M%S%f')}Z{run_id}",
        "id": str(run_id),
        "trace_id": str(run_id),
        "run_type": "chain",
        "serialized": {"name": "AgentExecutor"},
        "inputs": {"input": "foo"},
        "session_id": str(session_id),
        "outputs": None,
    }


@pytest.mark.skipif(config.settings.AUTH_TYPE == "none", reason="")
async def test_session_stats_aggregated_merge_tree(
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
    wait_until_task_queue_empty: Callable[[], Awaitable[Any]],
) -> None:
    """Test session stats aggregated merge tree"""
    # no tokens provided

    async with fresh_tenant_client(db_asyncpg, use_api_key) as authed_client:
        client = authed_client.client

        session_id = str(uuid4())
        response = await client.post(
            "/sessions",
            json={"name": "yolo", "id": session_id},
        )
        assert response.status_code == 200

        # post first batch of runs at 3:55:55
        n_runs_first_batch = 10
        start_time = datetime(2025, 1, 2, 3, 55, 55)
        end_time = datetime(2025, 1, 2, 3, 56, 55)

        runs_post = []
        for i in range(n_runs_first_batch):
            run_id = uuid4()
            dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
            runs_post.append(
                _get_chain_root_run(run_id, start_time, end_time, session_id)
            )
            child_run_id = uuid4()
            runs_post.append(
                _get_llm_run(
                    run_id, child_run_id, start_time, end_time, session_id, dotted_order
                )
            )

        # post run
        await post_runs(
            "/runs/multipart",
            client,
            post=runs_post,
        )

        await wait_until_task_queue_empty()

        # post second batch of runs at 4:15:15
        n_runs_second_batch = 5
        start_time = datetime(2025, 1, 2, 4, 15, 15)
        end_time = datetime(2025, 1, 2, 4, 16, 15)

        runs_post = []
        for i in range(n_runs_second_batch):
            run_id = uuid4()
            dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
            runs_post.append(
                _get_chain_root_run(run_id, start_time, end_time, session_id)
            )
            child_run_id = uuid4()
            runs_post.append(
                _get_llm_run(
                    run_id, child_run_id, start_time, end_time, session_id, dotted_order
                )
            )

        await post_runs(
            "/runs/multipart",
            client,
            post=runs_post,
        )

        await wait_until_task_queue_empty()

        # post third batch of runs at 5:27:30
        n_runs_third_batch = 7
        start_time = datetime(2025, 1, 2, 5, 27, 30)
        end_time = datetime(2025, 1, 2, 5, 31, 30)

        runs_post = []
        for i in range(n_runs_third_batch):
            run_id = uuid4()
            dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
            runs_post.append(
                _get_chain_root_run(run_id, start_time, end_time, session_id)
            )
            child_run_id = uuid4()
            runs_post.append(
                _get_llm_run(
                    run_id, child_run_id, start_time, end_time, session_id, dotted_order
                )
            )

        await post_runs(
            "/runs/multipart",
            client,
            post=runs_post,
        )

        await wait_until_task_queue_empty()

        # post fourth batch of runs at 5:55:55
        n_runs_fourth_batch = 10
        start_time = datetime(2025, 1, 2, 5, 55, 55)
        end_time = datetime(2025, 1, 2, 5, 56, 55)

        runs_post = []
        for i in range(n_runs_fourth_batch):
            run_id = uuid4()
            dotted_order = f"{start_time.strftime('%Y%m%dT%H%M%S%fZ')}{run_id}"
            runs_post.append(
                _get_chain_root_run(run_id, start_time, end_time, session_id)
            )
            child_run_id = uuid4()
            runs_post.append(
                _get_llm_run(
                    run_id, child_run_id, start_time, end_time, session_id, dotted_order
                )
            )

        await post_runs(
            "/runs/multipart",
            client,
            post=runs_post,
        )

        await wait_until_task_queue_empty()

        with ExitStack() as stack:
            # Setup all patches
            stack.enter_context(
                patch.multiple(
                    "app.models.runs.stats.settings",
                    STATS_AGGREGATION_TENANT_IDS=[str(authed_client.auth.tenant_id)],
                )
            )

            # first check time outside window works
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T02:00:55.000000",
                    "end_time": "2025-01-02T03:30:55.000000",
                },
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["completion_tokens"] == 0
            assert resp["total_tokens"] == 0
            assert resp["run_count"] == 0

            # check trailing edge + leading edge only
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:45:00.000000",
                    "end_time": "2025-01-02T04:30:55.000000",
                },
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 30
            assert resp["completion_tokens"] == 1500
            assert resp["prompt_tokens"] == 375
            assert resp["total_tokens"] == 1875
            assert resp["completion_tokens_p50"] == 100
            assert resp["prompt_tokens_p50"] == 25
            assert resp["median_tokens"] == 125
            assert resp["completion_tokens_p99"] == 100
            assert resp["prompt_tokens_p99"] == 25
            assert resp["tokens_p99"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T04:15:15"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check trailing edge + aggregated + leading edge
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:45:00.000000",
                    "end_time": "2025-01-02T05:10:55.000000",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 30
            assert resp["completion_tokens"] == 1500
            assert resp["prompt_tokens"] == 375
            assert resp["total_tokens"] == 1875

            # no token p50 and p99 stored aggregated merge tree
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None

            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T04:15:15"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check trailing edge + aggregated + leading edge + is_root=1
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:45:00.000000",
                    "end_time": "2025-01-02T05:10:55.000000",
                    "filter": "eq(is_root, true)",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 15
            assert resp["completion_tokens"] == 1500
            assert resp["prompt_tokens"] == 375
            assert resp["total_tokens"] == 1875

            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None

            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T04:15:15"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check trailing edge + aggregated (no leading edge)
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:45:00.000000",
                    "end_time": "2025-01-02T06:00:00.000000",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 64
            assert resp["completion_tokens"] == 3200
            assert resp["prompt_tokens"] == 800
            assert resp["total_tokens"] == 4000
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 240.0

            # check aggregated + leading edge (no trailing edge)
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T04:00:00.000000",
                    "end_time": "2025-01-02T05:58:00.000000",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 44
            assert resp["completion_tokens"] == 2200
            assert resp["prompt_tokens"] == 550
            assert resp["total_tokens"] == 2750
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            # note this won't be correct because we are using the p50, p99 latency from the amt
            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:55:55"
            # note this won't be correct because we are using the p50, p99 latency from the amt
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check aggregated only
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:00:00.000000",
                    "end_time": "2025-01-02T05:59:59.999999",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 64
            assert resp["completion_tokens"] == 3200
            assert resp["prompt_tokens"] == 800
            assert resp["total_tokens"] == 4000
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 240.0

            # check sub hour interval
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:55:00.000000",
                    "end_time": "2025-01-02T03:57:00.000000",
                    "filter": "eq(is_root, false)",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 10
            assert resp["completion_tokens"] == 1000
            assert resp["prompt_tokens"] == 250
            assert resp["total_tokens"] == 1250
            assert resp["completion_tokens_p50"] == 100
            assert resp["prompt_tokens_p50"] == 25
            assert resp["median_tokens"] == 125
            assert resp["completion_tokens_p99"] == 100
            assert resp["prompt_tokens_p99"] == 25
            assert resp["tokens_p99"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T03:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check when end_time is None (should include all runs from start_time to current time)
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:45:00.000000",
                    # end_time is None
                },
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 64  # all runs from start_time onward
            assert resp["completion_tokens"] == 3200
            assert resp["prompt_tokens"] == 800
            assert resp["total_tokens"] == 4000
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            assert resp["median_tokens"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 240.0

            # check when start_time is None (should include all runs from beginning to end_time)
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    # start_time is None
                    "end_time": "2025-01-02T05:30:00.000000",
                },
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 44  # first 3 batches (10 + 5 + 7) * 2 = 44 runs
            assert resp["completion_tokens"] == 2200
            assert resp["prompt_tokens"] == 550
            assert resp["total_tokens"] == 2750
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["median_tokens"] == 125
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:27:30"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0

            # check when both start_time and end_time are None (should include all runs)
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    # both start_time and end_time are None
                },
            )
            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 64  # all runs
            assert resp["completion_tokens"] == 3200
            assert resp["prompt_tokens"] == 800
            assert resp["total_tokens"] == 4000
            assert resp["completion_tokens_p50"] is None
            assert resp["prompt_tokens_p50"] is None
            assert resp["median_tokens"] == 125
            assert resp["completion_tokens_p99"] is None
            assert resp["prompt_tokens_p99"] is None
            assert resp["tokens_p99"] is None
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T05:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 240.0

            # check middle hour
            response = await client.post(
                "/runs/stats",
                json={
                    "session": [str(session_id)],
                    "start_time": "2025-01-02T03:15:00.000000",
                    "end_time": "2025-01-02T04:15:00.000000",
                },
            )

            assert response.status_code == 200
            resp = response.json()
            assert resp["run_count"] == 20
            assert resp["completion_tokens"] == 1000
            assert resp["prompt_tokens"] == 250
            assert resp["total_tokens"] == 1250
            assert resp["completion_tokens_p50"] == 100
            assert resp["prompt_tokens_p50"] == 25
            assert resp["median_tokens"] == 125
            assert resp["completion_tokens_p99"] == 100
            assert resp["prompt_tokens_p99"] == 25
            assert resp["tokens_p99"] == 125
            assert resp["streaming_rate"] == 0.0
            assert resp["last_run_start_time"] == "2025-01-02T03:55:55"
            assert resp["latency_p50"] == 60.0
            assert resp["latency_p99"] == 60.0


def test_calculate_amt_time_windows():
    """Test the calculate_amt_time_windows function with various scenarios."""

    # Test case 1: start_time exactly on the hour, end_time not at hour boundary
    start_time = "2025-01-02T03:00:00.000000"
    end_time = "2025-01-02T05:30:15.000000"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times (now timezone-aware)
    assert result.raw_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.raw_end == datetime(2025, 1, 2, 5, 30, 15, 0, tzinfo=timezone.utc)

    # AMT window: 3:00 to 5:00 (next hour boundary before end_time)
    assert result.amt_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.amt_end == datetime(
        2025, 1, 2, 4, 0, 0, 0, tzinfo=timezone.utc
    )  # 5:00 - 1 hour

    # No trailing edge needed (start_time is on hour)
    assert result.trailing_start is None
    assert result.trailing_end is None

    # Leading edge needed (end_time not at hour boundary)
    assert result.leading_start == datetime(2025, 1, 2, 5, 0, 0, 0, tzinfo=timezone.utc)
    assert result.leading_end == datetime(2025, 1, 2, 5, 30, 15, 0, tzinfo=timezone.utc)

    # Test case 2: start_time not on hour, end_time exactly at 59:59:59.999999
    start_time = "2025-01-02T03:15:30.000000"
    end_time = "2025-01-02T05:59:59.999999"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc)
    assert result.raw_end == datetime(
        2025, 1, 2, 5, 59, 59, 999999, tzinfo=timezone.utc
    )

    # AMT window: 4:00 to 5:00
    assert result.amt_start == datetime(
        2025, 1, 2, 4, 0, 0, 0, tzinfo=timezone.utc
    )  # next hour after start
    assert result.amt_end == datetime(
        2025, 1, 2, 5, 0, 0, 0, tzinfo=timezone.utc
    )  # hour of end_time

    # Trailing edge needed (start_time not on hour)
    assert result.trailing_start == datetime(
        2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc
    )
    assert result.trailing_end == datetime(
        2025, 1, 2, 3, 59, 59, 999999, tzinfo=timezone.utc
    )

    # No leading edge needed (end_time is at 59:59:59.999999)
    assert result.leading_start is None
    assert result.leading_end is None

    # Test case 3: Both start and end times not on hour boundaries
    start_time = "2025-01-02T03:25:45.123456"
    end_time = "2025-01-02T06:45:30.789012"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(
        2025, 1, 2, 3, 25, 45, 123456, tzinfo=timezone.utc
    )
    assert result.raw_end == datetime(
        2025, 1, 2, 6, 45, 30, 789012, tzinfo=timezone.utc
    )

    # AMT window: 4:00 to 5:00 (6:00 - 1 hour)
    assert result.amt_start == datetime(2025, 1, 2, 4, 0, 0, 0, tzinfo=timezone.utc)
    assert result.amt_end == datetime(2025, 1, 2, 5, 0, 0, 0, tzinfo=timezone.utc)

    # Trailing edge needed
    assert result.trailing_start == datetime(
        2025, 1, 2, 3, 25, 45, 123456, tzinfo=timezone.utc
    )
    assert result.trailing_end == datetime(
        2025, 1, 2, 3, 59, 59, 999999, tzinfo=timezone.utc
    )

    # Leading edge needed
    assert result.leading_start == datetime(2025, 1, 2, 6, 0, 0, 0, tzinfo=timezone.utc)
    assert result.leading_end == datetime(
        2025, 1, 2, 6, 45, 30, 789012, tzinfo=timezone.utc
    )

    # Test case 4: Time window too small (no valid AMT window)
    start_time = "2025-01-02T03:25:45.123456"
    end_time = "2025-01-02T03:55:30.789012"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(
        2025, 1, 2, 3, 25, 45, 123456, tzinfo=timezone.utc
    )
    assert result.raw_end == datetime(
        2025, 1, 2, 3, 55, 30, 789012, tzinfo=timezone.utc
    )

    # No AMT window (amt_end < amt_start)
    assert result.amt_start is None
    assert result.amt_end is None

    # Trailing edge needed
    assert result.trailing_start == datetime(
        2025, 1, 2, 3, 25, 45, 123456, tzinfo=timezone.utc
    )
    assert result.trailing_end == datetime(
        2025, 1, 2, 3, 59, 59, 999999, tzinfo=timezone.utc
    )

    # Leading edge needed
    assert result.leading_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.leading_end == datetime(
        2025, 1, 2, 3, 55, 30, 789012, tzinfo=timezone.utc
    )

    # Test case 5: Both times exactly on hour boundaries
    start_time = "2025-01-02T03:00:00.000000"
    end_time = "2025-01-02T05:59:59.999999"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.raw_end == datetime(
        2025, 1, 2, 5, 59, 59, 999999, tzinfo=timezone.utc
    )

    # AMT window: 3:00 to 5:00
    assert result.amt_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.amt_end == datetime(2025, 1, 2, 5, 0, 0, 0, tzinfo=timezone.utc)

    # No edges needed
    assert result.trailing_start is None
    assert result.trailing_end is None
    assert result.leading_start is None
    assert result.leading_end is None

    # Test case 6: None start_time (should use current time for end_time)
    start_time = None
    end_time = "2025-01-02T05:30:15.000000"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start is None
    assert result.raw_end == datetime(2025, 1, 2, 5, 30, 15, 0, tzinfo=timezone.utc)

    # No AMT window (no start_time)
    assert result.amt_start is None
    assert result.amt_end == datetime(2025, 1, 2, 4, 0, 0, 0, tzinfo=timezone.utc)

    # No trailing edge
    assert result.trailing_start is None
    assert result.trailing_end is None

    # Leading edge needed
    assert result.leading_start == datetime(2025, 1, 2, 5, 0, 0, 0, tzinfo=timezone.utc)
    assert result.leading_end == datetime(2025, 1, 2, 5, 30, 15, 0, tzinfo=timezone.utc)

    # Test case 7: None end_time (should use current UTC time)
    start_time = "2025-01-02T03:15:30.000000"
    end_time = None

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc)
    assert result.raw_end is not None  # Should be current UTC time
    assert result.raw_end.tzinfo == timezone.utc

    # AMT window should be calculated based on current time
    assert result.amt_start == datetime(2025, 1, 2, 4, 0, 0, 0, tzinfo=timezone.utc)
    assert result.amt_end is not None
    assert result.amt_end.tzinfo == timezone.utc

    # Trailing edge needed
    assert result.trailing_start == datetime(
        2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc
    )
    assert result.trailing_end == datetime(
        2025, 1, 2, 3, 59, 59, 999999, tzinfo=timezone.utc
    )

    # Test case 8: Both times None
    start_time = None
    end_time = None

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start is None
    assert result.raw_end is not None  # Should be current UTC time
    assert result.raw_end.tzinfo == timezone.utc

    # No AMT window (no start_time)
    assert result.amt_start is None
    assert result.amt_end is not None
    assert result.amt_end.tzinfo == timezone.utc

    # No trailing edge
    assert result.trailing_start is None
    assert result.trailing_end is None

    # Test case 9: Single hour window with partial start and end
    start_time = "2025-01-02T03:15:30.000000"
    end_time = "2025-01-02T03:45:45.000000"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc)
    assert result.raw_end == datetime(2025, 1, 2, 3, 45, 45, 0, tzinfo=timezone.utc)

    # No AMT window (would be 4:00 to 2:00 which is invalid)
    assert result.amt_start is None
    assert result.amt_end is None

    # Both edges needed but collapse to a single query
    assert result.trailing_start == datetime(
        2025, 1, 2, 3, 15, 30, 0, tzinfo=timezone.utc
    )
    assert result.trailing_end == datetime(
        2025, 1, 2, 3, 59, 59, 999999, tzinfo=timezone.utc
    )
    assert result.leading_start == datetime(2025, 1, 2, 3, 0, 0, 0, tzinfo=timezone.utc)
    assert result.leading_end == datetime(2025, 1, 2, 3, 45, 45, 0, tzinfo=timezone.utc)

    # Test case 10: Multi-hour span with exact boundaries
    start_time = "2025-01-02T02:00:00.000000"
    end_time = "2025-01-02T07:59:59.999999"

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times
    assert result.raw_start == datetime(2025, 1, 2, 2, 0, 0, 0, tzinfo=timezone.utc)
    assert result.raw_end == datetime(
        2025, 1, 2, 7, 59, 59, 999999, tzinfo=timezone.utc
    )

    # AMT window: 2:00 to 7:00
    assert result.amt_start == datetime(2025, 1, 2, 2, 0, 0, 0, tzinfo=timezone.utc)
    assert result.amt_end == datetime(2025, 1, 2, 7, 0, 0, 0, tzinfo=timezone.utc)

    # No edges needed
    assert result.trailing_start is None
    assert result.trailing_end is None
    assert result.leading_start is None
    assert result.leading_end is None

    # Test case 11: Test with explicit timezone in input
    start_time = "2025-01-02T03:00:00.000000+05:00"  # UTC+5
    end_time = "2025-01-02T05:30:15.000000-03:00"  # UTC-3

    result = calculate_amt_time_windows(start_time, end_time)

    # Raw times should preserve original timezone
    assert result.raw_start.tzinfo.utcoffset(None) == timedelta(hours=5)
    assert result.raw_end.tzinfo.utcoffset(None) == timedelta(hours=-3)

    # AMT calculations should work with timezone-aware datetimes
    assert result.amt_start is not None
    assert result.amt_end is not None

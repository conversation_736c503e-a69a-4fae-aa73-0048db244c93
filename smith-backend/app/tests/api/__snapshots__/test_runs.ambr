# serializer version: 1
# name: test_run_stats[/runs/batch|go][group_by_metadata]
  dict({
    '1': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    '2': dict({
      'completion_cost': 0.015015,
      'completion_tokens': 1001,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 398,
      'cost_p50': 0.0055,
      'cost_p99': 0.0089398,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.431,
      'latency_p99': 7.4502,
      'median_tokens': 500,
      'prompt_cost': 0.004995,
      'prompt_tokens': 999,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 591,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 990,
      'total_cost': 0.02001,
      'total_tokens': 2000,
    }),
  })
# ---
# name: test_run_stats[/runs/batch|go][group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 2,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    'LLM': dict({
      'completion_cost': 0.009,
      'completion_tokens': 600,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 300,
      'cost_p50': 0.0055,
      'cost_p99': 0.0055,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.436,
      'latency_p99': 7.4507,
      'median_tokens': 500,
      'prompt_cost': 0.002,
      'prompt_tokens': 400,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 200,
      'run_count': 2,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 500,
      'total_cost': 0.011,
      'total_tokens': 1000,
    }),
    'LLM1': dict({
      'completion_cost': 0.006015,
      'completion_tokens': 401,
      'completion_tokens_p50': 401,
      'completion_tokens_p99': 401,
      'cost_p50': 0.00901,
      'cost_p99': 0.00901,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.591809',
      'latency_p50': 7.431,
      'latency_p99': 7.431,
      'median_tokens': 1000,
      'prompt_cost': 0.002995,
      'prompt_tokens': 599,
      'prompt_tokens_p50': 599,
      'prompt_tokens_p99': 599,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1000,
      'total_cost': 0.00901,
      'total_tokens': 1000,
    }),
    'LLMChain': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.441,
      'latency_p99': 7.441,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))",
          'value': 'ls_run_depth == 1',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
  })
# ---
# name: test_run_stats[/runs/batch|go][group_by_run_type]
  dict({
    'chain': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.4509,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))",
          'value': 'ls_run_depth == 1',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    'llm': dict({
      'completion_cost': 0.015015,
      'completion_tokens': 1001,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 398,
      'cost_p50': 0.0055,
      'cost_p99': 0.0089398,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.431,
      'latency_p99': 7.4506,
      'median_tokens': 500,
      'prompt_cost': 0.004995,
      'prompt_tokens': 999,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 591,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 990,
      'total_cost': 0.02001,
      'total_tokens': 2000,
    }),
  })
# ---
# name: test_run_stats[/runs/batch|go][group_by_tags]
  dict({
  })
# ---
# name: test_run_stats[/runs/batch|go][is_root_group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': 0.010515,
      'completion_tokens': 701,
      'completion_tokens_p50': 701,
      'completion_tokens_p99': 701,
      'cost_p50': 0.01451,
      'cost_p99': 0.01451,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 2,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 1500,
      'prompt_cost': 0.003995,
      'prompt_tokens': 799,
      'prompt_tokens_p50': 799,
      'prompt_tokens_p99': 799,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1500,
      'total_cost': 0.01451,
      'total_tokens': 1500,
    }),
    'LLM': dict({
      'completion_cost': 0.0045,
      'completion_tokens': 300,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 300,
      'cost_p50': 0.0055,
      'cost_p99': 0.0055,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 500,
      'prompt_cost': 0.001,
      'prompt_tokens': 200,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 200,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 500,
      'total_cost': 0.0055,
      'total_tokens': 500,
    }),
  })
# ---
# name: test_run_stats[/runs/batch|go][is_root_run_type_chain_group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': 0.010515,
      'completion_tokens': 701,
      'completion_tokens_p50': 701,
      'completion_tokens_p99': 701,
      'cost_p50': 0.01451,
      'cost_p99': 0.01451,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 1,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 1500,
      'prompt_cost': 0.003995,
      'prompt_tokens': 799,
      'prompt_tokens_p50': 799,
      'prompt_tokens_p99': 799,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1500,
      'total_cost': 0.01451,
      'total_tokens': 1500,
    }),
  })
# ---
# name: test_run_stats[/runs/batch|go][run_type_chain_group_by_metadata]
  dict({
    '1': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][group_by_metadata]
  dict({
    '1': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    '2': dict({
      'completion_cost': 0.015015,
      'completion_tokens': 1001,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 398,
      'cost_p50': 0.0055,
      'cost_p99': 0.0089398,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.431,
      'latency_p99': 7.4502,
      'median_tokens': 500,
      'prompt_cost': 0.004995,
      'prompt_tokens': 999,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 591,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 990,
      'total_cost': 0.02001,
      'total_tokens': 2000,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 2,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    'LLM': dict({
      'completion_cost': 0.009,
      'completion_tokens': 600,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 300,
      'cost_p50': 0.0055,
      'cost_p99': 0.0055,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.436,
      'latency_p99': 7.4507,
      'median_tokens': 500,
      'prompt_cost': 0.002,
      'prompt_tokens': 400,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 200,
      'run_count': 2,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 500,
      'total_cost': 0.011,
      'total_tokens': 1000,
    }),
    'LLM1': dict({
      'completion_cost': 0.006015,
      'completion_tokens': 401,
      'completion_tokens_p50': 401,
      'completion_tokens_p99': 401,
      'cost_p50': 0.00901,
      'cost_p99': 0.00901,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.591809',
      'latency_p50': 7.431,
      'latency_p99': 7.431,
      'median_tokens': 1000,
      'prompt_cost': 0.002995,
      'prompt_tokens': 599,
      'prompt_tokens_p50': 599,
      'prompt_tokens_p99': 599,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1000,
      'total_cost': 0.00901,
      'total_tokens': 1000,
    }),
    'LLMChain': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.441,
      'latency_p99': 7.441,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))",
          'value': 'ls_run_depth == 1',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][group_by_run_type]
  dict({
    'chain': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.4509,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 1))",
          'value': 'ls_run_depth == 1',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
    'llm': dict({
      'completion_cost': 0.015015,
      'completion_tokens': 1001,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 398,
      'cost_p50': 0.0055,
      'cost_p99': 0.0089398,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.601809',
      'latency_p50': 7.431,
      'latency_p99': 7.4506,
      'median_tokens': 500,
      'prompt_cost': 0.004995,
      'prompt_tokens': 999,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 591,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2022?"))',
          'value': 'prompts == "how many people live in canada as of 2022?"',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 2))",
          'value': 'ls_run_depth == 2',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM1")',
          'value': 'LLM1',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 401))",
          'value': 'llm_output.token_usage.completion_tokens == 401',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 599))",
          'value': 'llm_output.token_usage.prompt_tokens == 599',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 1000))",
          'value': 'llm_output.token_usage.total_tokens == 1000',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 990,
      'total_cost': 0.02001,
      'total_tokens': 2000,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][group_by_tags]
  dict({
  })
# ---
# name: test_run_stats[/runs/multipart|go][is_root_group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': 0.010515,
      'completion_tokens': 701,
      'completion_tokens_p50': 701,
      'completion_tokens_p99': 701,
      'cost_p50': 0.01451,
      'cost_p99': 0.01451,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 2,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 1500,
      'prompt_cost': 0.003995,
      'prompt_tokens': 799,
      'prompt_tokens_p50': 799,
      'prompt_tokens_p99': 799,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1500,
      'total_cost': 0.01451,
      'total_tokens': 1500,
    }),
    'LLM': dict({
      'completion_cost': 0.0045,
      'completion_tokens': 300,
      'completion_tokens_p50': 300,
      'completion_tokens_p99': 300,
      'cost_p50': 0.0055,
      'cost_p99': 0.0055,
      'error_rate': 0.0,
      'feedback_stats': dict({
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 500,
      'prompt_cost': 0.001,
      'prompt_tokens': 200,
      'prompt_tokens_p50': 200,
      'prompt_tokens_p99': 200,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "prompts")',
          'value': 'prompts',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'prompts\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'prompts == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_model_name")',
          'value': 'ls_model_name',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_provider")',
          'value': 'ls_provider',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_model_name\'), eq(metadata_value, "gpt-4o"))',
          'value': 'ls_model_name == "gpt-4o"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': 'and(eq(metadata_key, \'ls_provider\'), eq(metadata_value, "openai"))',
          'value': 'ls_provider == "openai"',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 2))",
          'value': 'mkey == 2',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLM")',
          'value': 'LLM',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "generations.text")',
          'value': 'generations.text',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.completion_tokens")',
          'value': 'llm_output.token_usage.completion_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.prompt_tokens")',
          'value': 'llm_output.token_usage.prompt_tokens',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "llm_output.token_usage.total_tokens")',
          'value': 'llm_output.token_usage.total_tokens',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'generations.text\'), eq(output_value, "39,566,248"))',
          'value': 'generations.text == "39,566,248"',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.completion_tokens'), eq(output_value, 300))",
          'value': 'llm_output.token_usage.completion_tokens == 300',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.prompt_tokens'), eq(output_value, 200))",
          'value': 'llm_output.token_usage.prompt_tokens == 200',
        }),
        dict({
          'key': 'output_key_value',
          'query': "and(eq(output_key, 'llm_output.token_usage.total_tokens'), eq(output_value, 500))",
          'value': 'llm_output.token_usage.total_tokens == 500',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "llm")',
          'value': 'llm',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 500,
      'total_cost': 0.0055,
      'total_tokens': 500,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][is_root_run_type_chain_group_by_name]
  dict({
    'AgentExecutor': dict({
      'completion_cost': 0.010515,
      'completion_tokens': 701,
      'completion_tokens_p50': 701,
      'completion_tokens_p99': 701,
      'cost_p50': 0.01451,
      'cost_p99': 0.01451,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'foo': dict({
          'avg': 50.0,
          'errors': 0,
          'n': 1,
          'stdev': 50.0,
          'values': dict({
            'blue': 2,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.571809',
      'latency_p50': 7.451,
      'latency_p99': 7.451,
      'median_tokens': 1500,
      'prompt_cost': 0.003995,
      'prompt_tokens': 799,
      'prompt_tokens_p50': 799,
      'prompt_tokens_p99': 799,
      'run_count': 1,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "ls_run_depth")',
          'value': 'ls_run_depth',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'ls_run_depth'), eq(metadata_value, 0))",
          'value': 'ls_run_depth == 0',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 1500,
      'total_cost': 0.01451,
      'total_tokens': 1500,
    }),
  })
# ---
# name: test_run_stats[/runs/multipart|go][run_type_chain_group_by_metadata]
  dict({
    '1': dict({
      'completion_cost': None,
      'completion_tokens': 0,
      'completion_tokens_p50': 0,
      'completion_tokens_p99': 0,
      'cost_p50': 0.0,
      'cost_p99': 0.0,
      'error_rate': 0.0,
      'feedback_stats': dict({
        'bar': dict({
          'avg': 75.0,
          'errors': 0,
          'n': 4,
          'stdev': 43.30127018922193,
          'values': dict({
            'blue': 1,
            'red': 2,
          }),
        }),
        'foo': dict({
          'avg': 60.0,
          'errors': 0,
          'n': 5,
          'stdev': 48.98979485566356,
          'values': dict({
            'blue': 4,
            'green': 1,
          }),
        }),
      }),
      'first_token_p50': None,
      'first_token_p99': None,
      'last_run_start_time': '2023-05-05T05:13:24.581809',
      'latency_p50': 7.446,
      'latency_p99': 7.451,
      'median_tokens': 0,
      'prompt_cost': None,
      'prompt_tokens': 0,
      'prompt_tokens_p50': 0,
      'prompt_tokens_p99': 0,
      'run_count': 3,
      'run_facets': list([
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "bar")',
          'value': 'bar',
        }),
        dict({
          'key': 'feedback_key',
          'query': 'eq(feedback_key, "foo")',
          'value': 'foo',
        }),
        dict({
          'key': 'feedback_key_score',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_score, "100"))',
          'value': 'bar == 100',
        }),
        dict({
          'key': 'feedback_source',
          'query': 'eq(feedback_source, "api")',
          'value': 'api',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "blue"))',
          'value': 'bar == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "bar"), eq(feedback_value, "red"))',
          'value': 'bar == "red"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "blue"))',
          'value': 'foo == "blue"',
        }),
        dict({
          'key': 'feedback_value',
          'query': 'and(eq(feedback_key, "foo"), eq(feedback_value, "green"))',
          'value': 'foo == "green"',
        }),
        dict({
          'key': 'input_key',
          'query': 'eq(input_key, "input")',
          'value': 'input',
        }),
        dict({
          'key': 'input_key_value',
          'query': 'and(eq(input_key, \'input\'), eq(input_value, "how many people live in canada as of 2023?"))',
          'value': 'input == "how many people live in canada as of 2023?"',
        }),
        dict({
          'key': 'metadata_key',
          'query': 'eq(metadata_key, "mkey")',
          'value': 'mkey',
        }),
        dict({
          'key': 'metadata_key_value',
          'query': "and(eq(metadata_key, 'mkey'), eq(metadata_value, 1))",
          'value': 'mkey == 1',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "AgentExecutor")',
          'value': 'AgentExecutor',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "LLMChain")',
          'value': 'LLMChain',
        }),
        dict({
          'key': 'name',
          'query': 'eq(name, "RunnableSequence")',
          'value': 'RunnableSequence',
        }),
        dict({
          'key': 'output_key',
          'query': 'eq(output_key, "output")',
          'value': 'output',
        }),
        dict({
          'key': 'output_key_value',
          'query': 'and(eq(output_key, \'output\'), eq(output_value, "39,566,248"))',
          'value': 'output == "39,566,248"',
        }),
        dict({
          'key': 'run_type',
          'query': 'eq(run_type, "chain")',
          'value': 'chain',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "pending")',
          'value': 'pending',
        }),
        dict({
          'key': 'status',
          'query': 'eq(status, "success")',
          'value': 'success',
        }),
      ]),
      'streaming_rate': 0.0,
      'tokens_p99': 0,
      'total_cost': None,
      'total_tokens': 0,
    }),
  })
# ---

from datetime import datetime, timezone

import orjson
from dateutil.parser import isoparse
from deepdiff import DeepDiff
from httpx import AsyncClient
from jwt import decode
from lc_database.curl import platform_request

import app
from app.config import shared_settings


async def test_get_version(aclient: AsyncClient) -> None:
    """Test the /info endpoint."""
    get_response = await aclient.get(
        "/info",
    )
    get_response.raise_for_status()
    assert "cache-control" in get_response.headers
    assert get_response.headers.get("cache-control") == "public, max-age=60"
    get_response_data = get_response.json()
    assert "version" in get_response_data
    assert get_response_data["version"] == app.__version__
    decoded_key = decode(
        shared_settings.LANGSMITH_LICENSE_KEY, options={"verify_signature": False}
    )
    assert "license_expiration_time" in get_response_data
    expiration_time = decoded_key["exp"]
    assert datetime.fromisoformat(
        get_response_data["license_expiration_time"]
    ) == datetime.fromtimestamp(expiration_time, tz=timezone.utc)
    assert "instance_flags" in get_response_data
    instance_flags = get_response_data["instance_flags"]
    assert instance_flags.get("payment_enabled") is False
    assert "batch_ingest_config" in get_response_data
    batch_ingest_config = get_response_data["batch_ingest_config"]
    assert "scale_up_qsize_trigger" in batch_ingest_config
    assert "scale_up_nthreads_limit" in batch_ingest_config
    assert "scale_down_nempty_trigger" in batch_ingest_config
    assert "size_limit" in batch_ingest_config
    assert "customer_info" in get_response_data
    assert get_response_data["customer_info"] == {
        "customer_id": decoded_key["sub"],
        "customer_name": decoded_key.get("customer_name", ""),
    }


async def test_get_version_production(aclient: AsyncClient, monkeypatch) -> None:
    """Test the /info endpoint with LANGCHAIN_ENV=production."""
    monkeypatch.setattr("app.config.shared_settings.LANGCHAIN_ENV", "production")

    get_response = await aclient.get(
        "/info",
    )
    get_response.raise_for_status()
    get_response_data = get_response.json()

    assert get_response_data["version"] == app.__version__
    assert get_response_data["license_expiration_time"] is None
    assert get_response_data["customer_info"] is None


async def test_get_version_go(aclient: AsyncClient) -> None:
    """Test the /info endpoint."""
    app.__version__ = "dev"
    python_res = await aclient.get(
        "/info",
    )
    python_res.raise_for_status()
    python_res_data = python_res.json()
    python_res_data["license_expiration_time"] = isoparse(
        python_res_data["license_expiration_time"]
    )
    go_res = await platform_request("GET", "/info")
    go_res_data = orjson.loads(go_res.body)
    go_res_data["license_expiration_time"] = isoparse(
        go_res_data["license_expiration_time"]
    )
    diff = DeepDiff(go_res_data, python_res_data, ignore_order=True, verbose_level=2)
    assert not diff


async def test_get_health_info(aclient: AsyncClient) -> None:
    """Test the /info endpoint."""
    get_response = await aclient.get(
        "/info/health",
    )
    get_response.raise_for_status()
    assert "clickhouse_disk_free_pct" in get_response.json()
    assert float(get_response.json()["clickhouse_disk_free_pct"]) >= 0.0

import uuid

import pytest
from httpx import AsyncClient

from app.tests.utils import jwt_for_user, random_lower_string


@pytest.fixture
async def org_admin_and_org(http_no_auth: AsyncClient, db_asyncpg):
    user_id = uuid.uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"orgadmin+{user_id}@langchain.dev",
        user_full_name="Org Admin",
    )
    auth_headers = {"Authorization": f"Bearer {jwt}"}
    # Create a shared org (not personal)
    resp = await http_no_auth.post(
        "/orgs",
        headers=auth_headers,
        json={"display_name": f"Test Org {user_id}", "is_personal": False},
    )
    assert resp.status_code == 200, resp.text
    org_id = resp.json()["id"]

    # Patch org config to allow more workspaces
    await db_asyncpg.execute(
        """
        UPDATE organizations
        SET config = jsonb_set(config, '{max_workspaces}', to_jsonb(10))
        WHERE id = $1
        """,
        uuid.UUID(org_id),
    )

    identity_exists = await db_asyncpg.fetchval(
        """
        SELECT 1 FROM identities WHERE organization_id = $1 AND user_id = $2 AND access_scope = 'organization'
        """,
        uuid.UUID(org_id),
        user_id,
    )
    if not identity_exists:
        await db_asyncpg.execute(
            """
            INSERT INTO identities (id, organization_id, ls_user_id, user_id, role_id, access_scope, created_at)
            VALUES ($1, $2, $3, $4, (SELECT id FROM roles WHERE name = 'ORGANIZATION_ADMIN'), 'organization', NOW())
            """,
            uuid.uuid4(),
            uuid.UUID(org_id),
            user_id,
            user_id,
        )

    identity_row = await db_asyncpg.fetchrow(
        """
        SELECT * FROM identities WHERE organization_id = $1 AND user_id = $2 AND access_scope = 'organization'
        """,
        uuid.UUID(org_id),
        user_id,
    )
    if not identity_row:
        raise AssertionError(
            f"Missing org admin identity for org {org_id}, user {user_id}. Row: {identity_row}"
        )

    # Qualify org admin as workspace admin for all workspaces
    workspace_ids = []
    for _ in range(3):
        resp = await http_no_auth.post(
            "/workspaces",
            headers={**auth_headers, "X-Organization-Id": org_id},
            json={"display_name": random_lower_string(), "organization_id": org_id},
        )
        assert resp.status_code == 200, resp.text
        ws_id = resp.json()["id"]
        workspace_ids.append(ws_id)

        identity_exists = await db_asyncpg.fetchval(
            """
            SELECT 1 FROM identities WHERE tenant_id = $1 AND user_id = $2 AND access_scope = 'workspace'
            """,
            uuid.UUID(ws_id),
            user_id,
        )

        if not identity_exists:
            await db_asyncpg.execute(
                """
                INSERT INTO identities (id, tenant_id, organization_id, user_id, ls_user_id, role_id, access_scope, parent_identity_id, created_at)
                VALUES ($1, $2, $3, $4, $5, (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'), 'workspace', $6, NOW())
                """,
                uuid.uuid4(),
                uuid.UUID(ws_id),
                uuid.UUID(org_id),
                user_id,
                user_id,
                identity_row["id"],
            )

    return org_id, {**auth_headers, "X-Organization-Id": org_id}, workspace_ids


@pytest.fixture
async def org_id(org_admin_and_org):
    return org_admin_and_org[0]


@pytest.fixture
async def org_admin_headers(org_admin_and_org):
    return org_admin_and_org[1]


@pytest.fixture
async def workspace_ids(http_no_auth: AsyncClient, org_admin_and_org) -> list[str]:
    org_id, org_admin_headers, workspace_ids = org_admin_and_org
    ws_ids = []
    for _ in range(3):
        resp = await http_no_auth.post(
            "/workspaces",
            headers=org_admin_headers,
            json={"display_name": random_lower_string(), "organization_id": org_id},
        )
        assert resp.status_code == 200, resp.text
        ws_ids.append(resp.json()["id"])
    return ws_ids


async def make_workspace_admin_headers(
    http_no_auth: AsyncClient, org_id: str, workspace_id: str, db_asyncpg
) -> dict:
    user_id = uuid.uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"wsadmin+{user_id}@langchain.dev",
        user_full_name="WS Admin",
    )
    headers = {
        "Authorization": f"Bearer {jwt}",
        "X-Organization-Id": org_id,
        "X-Tenant-Id": workspace_id,
    }

    await db_asyncpg.execute(
        """
        WITH user_insert AS (
            INSERT INTO users (id, email, full_name, ls_user_id)
            VALUES ($1, $2, $3, $4)
            ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email, full_name = EXCLUDED.full_name
            RETURNING ls_user_id
        )
        INSERT INTO provider_users (provider, ls_user_id, email, full_name, provider_user_id)
        SELECT 'email', $4, $2, $3, NULL
        FROM user_insert
        ON CONFLICT (ls_user_id, provider_user_id) WHERE provider <> 'supabase:sso' DO UPDATE SET email = EXCLUDED.email
        """,
        user_id,
        f"wsadmin+{user_id}@langchain.dev",
        "WS Admin",
        user_id,
    )

    org_identity_id = uuid.uuid4()
    await db_asyncpg.execute(
        """
        INSERT INTO identities (id, organization_id, ls_user_id, user_id, role_id, access_scope, created_at)
        VALUES ($1, $2, $3, $4, (SELECT id FROM roles WHERE name = 'ORGANIZATION_USER'), 'organization', NOW())
        ON CONFLICT (organization_id, ls_user_id) WHERE access_scope = 'organization' DO NOTHING
        """,
        org_identity_id,
        uuid.UUID(org_id),
        user_id,
        user_id,
    )

    await db_asyncpg.execute(
        """
        INSERT INTO identities (id, tenant_id, organization_id, user_id, ls_user_id, role_id, access_scope, parent_identity_id, created_at)
        VALUES ($1, $2, $3, $4, $5, (SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'), 'workspace', $6, NOW())
        ON CONFLICT (tenant_id, ls_user_id) WHERE access_scope = 'workspace' DO NOTHING
        """,
        uuid.uuid4(),
        uuid.UUID(workspace_id),
        uuid.UUID(org_id),
        user_id,
        user_id,
        org_identity_id,
    )

    return headers


@pytest.fixture
async def org_member_headers(http_no_auth: AsyncClient, org_id: str) -> dict:
    user_id = uuid.uuid4()
    jwt = jwt_for_user(
        user_id=user_id,
        user_email=f"member+{user_id}@langchain.dev",
        user_full_name="Org Member",
    )
    return {"Authorization": f"Bearer {jwt}", "X-Organization-Id": org_id}


async def set_org_scoped_service_key_flag(
    db_asyncpg, org_id: str, enabled: bool = True
):
    await db_asyncpg.execute(
        """
        UPDATE organizations SET config = jsonb_set(config, '{org_scoped_service_accounts_enabled}', to_jsonb($1::bool)) WHERE id = $2
        """,
        enabled,
        uuid.UUID(org_id),
    )

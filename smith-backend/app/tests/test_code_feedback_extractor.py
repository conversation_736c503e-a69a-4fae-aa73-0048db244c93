from app.models.runs.utils import extract_code_evaluator_feedback_keys

# IMPORTANT: If you add any test cases here, you need to add them to extractCodeEvaluatorFeedbackKeys.test.ts as well


def test_correctly_extracts_feedback_keys_with_a_space():
    code = """# Import supported libraries, e.g.:
# import json

def perform_eval(run, example):
  # run is a Run object
  # example is an Example object

  # Example usages:
  # equals_str = run['outputs']['my_key'] == example['outputs']['my_key']

  # contains_str = 'expected_to_contain' in run['outputs']['my_key']

  # decoded_json = json.loads(run['outputs']['stringified_json_key'])

  output = run['outputs']['output']['content']
  score = 1
  if len(output) > 5000:
    score = 5
  elif len(output) > 3000:
    score = 4
  elif len(output) > 1500:
    score = 3
  elif len(output) > 500:
    score = 2
  
  return { "big booms": score }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["big booms"]


def test_correctly_extracts_feedback_keys_from_the_sample_code():
    code = """# Import supported libraries, e.g.:
# import json

def perform_eval(run, example):
  # run is a Run object
  # example is an Example object

  # Example usages:
  # equals_str = run['outputs']['my_key'] == example['outputs']['my_key']

  # contains_str = 'expected_to_contain' in run['outputs']['my_key']

  # decoded_json = json.loads(run['outputs']['stringified_json_key'])
  
  score = 1
  return { "feedback_key": score }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["feedback_key"]


def test_correctly_extracts_multiple_feedback_keys():
    code = """
def perform_eval(run, example):
    accuracy = calculate_accuracy()
    style_score = evaluate_style()
    return {
        "accuracy": accuracy,
        "style": style_score,
        "total score": accuracy + style_score
    }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["accuracy", "style", "total score"]


def test_handles_code_with_multiple_functions_but_extracts_only_from_perform_eval():
    code = """
def calculate_score(output):
    return {"temp": 5}

def perform_eval(run, example):
    score = calculate_score(run['outputs'])
    return {"final score": score, "feedback": "Good job!"}

def another_function():
    return {"should": "not be included"}"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["final score", "feedback"]


def test_handles_nested_dictionaries():
    code = """
def perform_eval(run, example):
    nested = {
        "temp": {
            "nested": "value"
        }
    }
    return {
        "outer key": nested,
        "second key": "value"
    }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["outer key", "second key"]


def test_handles_single_quotes_and_double_quotes_mixed():
    code = """
def perform_eval(run, example):
    return {
        'single quoted': 1,
        "double quoted": 2,
        'mixed "quotes"': 3,
        "mixed 'quotes'": 4
    }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == [
        "single quoted",
        "double quoted",
        'mixed "quotes"',
        "mixed 'quotes'",
    ]


def test_handles_when_there_is_no_dictionary_returned():
    code = """
def perform_eval(run, example):
    return score"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == []


def test_handles_multi_line_dictionary_definitions():
    code = """
def perform_eval(run, example):
    return {
        "key1":
            some_long_computation(),
        "key2":
            another_computation(),
        "key3": final_computation()
    }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["key1", "key2", "key3"]


def test_handles_comments_in_dictionary_definition():
    code = """
def perform_eval(run, example):
    return {
        # This is accuracy
        "accuracy": 0.95,
        # This is style
        "style score": 0.85  # End score
    }"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["accuracy", "style score"]


def test_handles_dynamic_keys():
    code = """
def perform_eval(run, example):
    return {f"foo_{i}": 0 for i in range(5)}"""
    result = extract_code_evaluator_feedback_keys(code, "evaluator_name")
    assert result == ["evaluator_name"]


def test_handles_dictionary_comprehension_with_items():
    code = """
def perform_eval(run, example):
    foo = {"accuracy": 0.95, "style": 0.85}
    return {k: v for k, v in foo.items()}"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["accuracy", "style"]


def test_handles_string_format_method():
    code = """
def perform_eval(run, example):
    return {"foo_{i}".format(i=i): 0 for i in range(5)}"""
    result = extract_code_evaluator_feedback_keys(code, "evaluator_name")
    assert result == ["evaluator_name"]


def test_handles_dict_constructor():
    code = """
def perform_eval(run, example):
    return dict(accuracy=0.95, style=0.85, feedback="Good job")"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["accuracy", "style", "feedback"]


def test_handles_dict_constructor_with_newlines():
    code = """
def perform_eval(run, example):
    return dict(
        accuracy=0.95,
        style=0.85,
        feedback="Good job"
    )"""
    result = extract_code_evaluator_feedback_keys(code)
    assert result == ["accuracy", "style", "feedback"]

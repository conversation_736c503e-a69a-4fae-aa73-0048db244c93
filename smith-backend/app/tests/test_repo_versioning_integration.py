"""
Integration tests for repository version-based cache invalidation.

This module tests the complete flow of version-based cache invalidation:
1. Caching functions with repo version keys
2. Cache version increment on repo changes
3. Natural cache invalidation through version mismatch
"""

from unittest.mock import AsyncMock, patch

import pytest

from app.memoize import redis_cache


@pytest.mark.asyncio
async def test_repo_version_based_cache_invalidation():
    """Test complete repo version-based cache invalidation flow."""

    call_count = 0
    mock_version = 1

    async def mock_get_repo_cache_version(owner: str, repo: str) -> int:
        return mock_version

    async def mock_increment_repo_cache_version(owner: str, repo: str) -> int:
        nonlocal mock_version
        mock_version += 1
        return mock_version

    async def repo_version_key(*args, **kwargs) -> str:
        """Version key function that uses mocked repo cache version."""
        owner = kwargs.get("owner", "test_owner")
        repo = kwargs.get("repo", "test_repo")
        version = await mock_get_repo_cache_version(owner, repo)
        return f"repo_v={version}"

    @redis_cache(
        ttl=300, enabled=False, version_func=repo_version_key
    )  # Disabled to test logic only
    async def get_cached_data(owner: str, repo: str, data: str) -> str:
        nonlocal call_count
        call_count += 1
        return f"data_{call_count}_{owner}_{repo}_{data}"

    # First call - should NOT cache (caching disabled)
    result1 = await get_cached_data("test_owner", "test_repo", "content1")
    assert result1 == "data_1_test_owner_test_repo_content1"
    assert call_count == 1

    # Second call with same params - should NOT use cache (caching disabled)
    result2 = await get_cached_data("test_owner", "test_repo", "content1")
    assert (
        result2 == "data_2_test_owner_test_repo_content1"
    )  # New result since no caching
    assert call_count == 2  # Function called again

    # Test that version key function is still called correctly
    # This verifies the version key integration works even when caching is disabled
    version_before = await mock_get_repo_cache_version("test_owner", "test_repo")
    assert version_before == 1

    # Increment repo cache version (simulating commit creation)
    new_version = await mock_increment_repo_cache_version("test_owner", "test_repo")
    assert new_version == 2

    # Verify version was incremented
    version_after = await mock_get_repo_cache_version("test_owner", "test_repo")
    assert version_after == 2


@pytest.mark.asyncio
async def test_different_repos_have_independent_versions():
    """Test that different repositories have independent cache versions."""

    call_count = 0
    mock_versions = {}

    async def mock_get_repo_cache_version(owner: str, repo: str) -> int:
        key = f"{owner}/{repo}"
        return mock_versions.get(key, 1)

    async def mock_increment_repo_cache_version(owner: str, repo: str) -> int:
        key = f"{owner}/{repo}"
        current = mock_versions.get(key, 1)
        mock_versions[key] = current + 1
        return mock_versions[key]

    async def repo_version_key(*args, **kwargs) -> str:
        owner = kwargs.get("owner", "test_owner")
        repo = kwargs.get("repo", "test_repo")
        version = await mock_get_repo_cache_version(owner, repo)
        return f"repo_v={version}"

    @redis_cache(
        ttl=300, enabled=False, version_func=repo_version_key
    )  # Disabled to test logic only
    async def get_cached_data(owner: str, repo: str) -> str:
        nonlocal call_count
        call_count += 1
        return f"data_{call_count}_{owner}_{repo}"

    # Test that different repos have independent version tracking
    version1_before = await mock_get_repo_cache_version("owner1", "repo1")
    version2_before = await mock_get_repo_cache_version("owner1", "repo2")
    assert version1_before == 1
    assert version2_before == 1

    # Call functions to verify they work
    _ = await get_cached_data("owner1", "repo1")
    assert call_count == 1

    _ = await get_cached_data("owner1", "repo2")
    assert call_count == 2

    # Increment version for repo1 only
    await mock_increment_repo_cache_version("owner1", "repo1")

    # Verify only repo1 version was incremented
    version1_after = await mock_get_repo_cache_version("owner1", "repo1")
    version2_after = await mock_get_repo_cache_version("owner1", "repo2")
    assert version1_after == 2  # Incremented
    assert version2_after == 1  # Unchanged

    # Continue calling functions to verify they still work
    _ = await get_cached_data("owner1", "repo1")
    assert call_count == 3  # New call since caching disabled

    _ = await get_cached_data("owner1", "repo2")
    assert call_count == 4  # New call since caching disabled


@pytest.mark.asyncio
async def test_version_key_fallback_on_redis_failure():
    """Test that version key functions handle Redis failures gracefully."""

    call_count = 0

    async def failing_repo_version_key(*args, **kwargs) -> str:
        """Version key that simulates Redis failure."""
        # This would normally call get_repo_cache_version but we simulate failure
        raise Exception("Redis connection failed")

    @redis_cache(
        ttl=300, enabled=False, version_func=failing_repo_version_key
    )  # Disabled to test logic only
    async def get_cached_data(data: str) -> str:
        nonlocal call_count
        call_count += 1
        return f"data_{call_count}_{data}"

    # Should work despite version key failure (caching disabled, so no Redis involved)
    result1 = await get_cached_data("test")
    assert result1 == "data_1_test"
    assert call_count == 1

    # Should call function again since caching is disabled
    result2 = await get_cached_data("test")
    assert result2 == "data_2_test"  # New result since caching disabled
    assert call_count == 2  # Function called again


@pytest.mark.asyncio
async def test_real_repo_versioning_functions():
    """Test the actual repo versioning functions work correctly."""

    with patch("app.hub.repo_versioning.aredis_caching_pool") as mock_pool:
        # Mock Redis operations
        mock_redis = AsyncMock()
        mock_pool.return_value.__aenter__.return_value = mock_redis

        from app.hub.repo_versioning import (
            get_repo_cache_version,
            increment_repo_cache_version,
        )

        # Test get_repo_cache_version with existing version
        mock_redis.get.return_value = b"5"
        version = await get_repo_cache_version("test_owner", "test_repo")
        assert version == 5

        # Test increment_repo_cache_version
        mock_redis.incr.return_value = 6
        new_version = await increment_repo_cache_version("test_owner", "test_repo")
        assert new_version == 6

        # Test get_repo_cache_version with no existing version (auto-initialization)
        mock_redis.get.return_value = None
        mock_redis.set.return_value = True
        version = await get_repo_cache_version("new_owner", "new_repo")
        assert version > 0  # Should be initialized with timestamp

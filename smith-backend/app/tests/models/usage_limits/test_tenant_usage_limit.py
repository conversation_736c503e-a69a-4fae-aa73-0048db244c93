from unittest.mock import patch

import asyncpg
import pytest
from lc_database import redis

from app.models.runs import usage_limits
from app.models.tenants import tenant_usage_limit
from app.models.usage_limits import user_defined_limits
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE,
    fresh_tenant_client,
)

pytestmark = [
    pytest.mark.asyncio,
]

# Set very high values that would normally exceed any reasonable limits
# but should not trigger limits when set to -1 (unlimited)
very_high_value = 9_999_999_999


class TestUnlimitedUsageLimits:
    """Test cases for unlimited usage limits (-1 values)."""

    async def test_all_limits_set_to_unlimited(
        self, db_asyncpg: asyncpg.Connection, use_api_key: bool
    ):
        """Test when all limits are set to -1 (unlimited)."""
        # Create tenant config with all limits set to -1
        tenant_config = (
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
                update={
                    "max_events_ingested_per_minute": -1,
                    "max_hourly_tracing_requests": -1,
                    "max_hourly_tracing_bytes": -1,
                    "max_monthly_total_unique_traces": -1,
                }
            )
        )

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, tenant_config=tenant_config
        ) as authed_client:
            auth = authed_client.auth

            # Set high values for all counters
            async with redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis:
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_minute_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,
                )
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,
                )
                await aredis.set(
                    usage_limits.usage_limit_payload_size_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,
                )

                # Add many unique traces to the HLL
                hll_key = (
                    user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                        auth.tenant_id
                    )
                )
                for i in range(1000):
                    element = f"unique-trace-unlimited-{i}"
                    await aredis.pfadd(hll_key, element)

            result = await tenant_usage_limit.get_tenant_usage_limit_info(auth)

            # Should not be in reject set when all limits are unlimited
            assert result.in_reject_set is False
            assert result.usage_limit_type is None
            assert result.tenant_limit is None

    async def test_individual_limit_set_to_unlimited(
        self, db_asyncpg: asyncpg.Connection, use_api_key: bool
    ):
        """Test when one limit is set to -1 while others have normal values."""
        # Create tenant config with one unlimited limit
        tenant_config = (
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
                update={
                    "max_events_ingested_per_minute": -1,  # Unlimited
                    "max_hourly_tracing_requests": 100,  # Limited
                    "max_hourly_tracing_bytes": 1000000,  # Limited
                    "max_monthly_total_unique_traces": 1000,  # Limited
                }
            )
        )

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, tenant_config=tenant_config
        ) as authed_client:
            auth = authed_client.auth

            # Set high value for the unlimited limit (per-minute events)
            # and low values for the limited ones
            async with redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis:
                # Set very high value for unlimited limit
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_minute_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,
                )
                # Set low values for limited limits
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    50,  # Under limit of 100
                )
                await aredis.set(
                    usage_limits.usage_limit_payload_size_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    500000,  # Under limit of 1000000
                )

                # Add few unique traces
                hll_key = (
                    user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                        auth.tenant_id
                    )
                )
                for i in range(500):  # Under limit of 1000
                    element = f"unique-trace-individual-test-{i}"
                    await aredis.pfadd(hll_key, element)

            result = await tenant_usage_limit.get_tenant_usage_limit_info(auth)

            # Should not be in reject set when the unlimited limit is the only one that would be exceeded
            assert result.in_reject_set is False
            assert result.usage_limit_type is None
            assert result.tenant_limit is None

    async def test_zero_limits_are_not_unlimited(
        self, db_asyncpg: asyncpg.Connection, use_api_key: bool
    ):
        """Test that 0 limits are not treated as unlimited."""
        # Create tenant config with 0 limits
        tenant_config = (
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
                update={
                    "max_events_ingested_per_minute": 0,
                    "max_hourly_tracing_requests": 0,
                    "max_hourly_tracing_bytes": 0,
                    "max_monthly_total_unique_traces": 0,
                }
            )
        )

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, tenant_config=tenant_config
        ) as authed_client:
            auth = authed_client.auth

            # Set minimal usage that would exceed 0 limits
            async with redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis:
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_minute_counter_key(
                        auth.tenant_id
                    ),
                    1,  # Any usage exceeds 0 limit
                )
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    1,  # Any usage exceeds 0 limit
                )
                await aredis.set(
                    usage_limits.usage_limit_payload_size_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    1,  # Any usage exceeds 0 limit
                )

                # Add one unique trace
                hll_key = (
                    user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                        auth.tenant_id
                    )
                )
                await aredis.pfadd(hll_key, "unique-trace-zero-test")

            result = await tenant_usage_limit.get_tenant_usage_limit_info(auth)

            # Should be in reject set because 0 limits are very restrictive
            # (any usage would exceed them)
            assert result.in_reject_set is True
            assert result.usage_limit_type is not None
            assert result.tenant_limit == 0

    async def test_self_hosted_always_returns_false(
        self, db_asyncpg: asyncpg.Connection, use_api_key: bool
    ):
        """Test that self-hosted environments always return not in reject set."""
        # Create tenant config with normal limits
        tenant_config = (
            SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_SINGLE_WORKSPACE.model_copy(
                update={
                    "max_events_ingested_per_minute": 1000,
                    "max_hourly_tracing_requests": 100,
                    "max_hourly_tracing_bytes": 1000000,
                    "max_monthly_total_unique_traces": 1000,
                }
            )
        )

        async with fresh_tenant_client(
            db_asyncpg, use_api_key, tenant_config=tenant_config
        ) as authed_client:
            auth = authed_client.auth

            # Set high usage that would normally exceed limits
            async with redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis:
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_minute_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,  # Would exceed limit of 1000
                )
                await aredis.set(
                    usage_limits.usage_limit_events_ingested_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,  # Would exceed limit of 100
                )
                await aredis.set(
                    usage_limits.usage_limit_payload_size_per_hour_counter_key(
                        auth.tenant_id
                    ),
                    very_high_value,  # Would exceed limit of 1000000
                )

                # Add many unique traces
                hll_key = (
                    user_defined_limits.usage_limit_unique_traces_per_month_hll_key(
                        auth.tenant_id
                    )
                )
                for i in range(1001):  # Would exceed limit of 1000
                    element = f"unique-trace-self-hosted-test-{i}"
                    await aredis.pfadd(hll_key, element)

            with patch("app.config.settings.IS_SELF_HOSTED", True):
                result = await tenant_usage_limit.get_tenant_usage_limit_info(auth)

                # Should always return not in reject set for self-hosted
                assert result.in_reject_set is False
                assert result.usage_limit_type is None
                assert result.tenant_limit is None

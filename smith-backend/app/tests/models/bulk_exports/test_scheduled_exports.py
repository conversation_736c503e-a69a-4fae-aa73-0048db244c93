import datetime
from datetime import timezone
from unittest.mock import Mock, patch

import asyncpg
import pytest
from fastapi import HTTPException

import app.models.bulk_exports.jobs as bulk_export_jobs
from app import config, crud, schemas
from app.models.bulk_exports import crud as bulk_exports_crud
from app.tests.models.bulk_exports.test_crud import TEST_DESTINATION
from app.tests.utils import (
    SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    fresh_tenant_client,
)

pytestmark = pytest.mark.skipif(
    config.settings.AUTH_TYPE in ["none", "mixed"], reason="single tenant/org"
)


@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_scheduled_bulk_export(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Test that the scheduled bulk export cron job creates new exports correctly."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await bulk_exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        start_time_hours_ago = 24
        start_time = datetime.datetime.now(timezone.utc) - datetime.timedelta(
            hours=start_time_hours_ago
        )
        interval_hours = 6
        time_ranges = int(start_time_hours_ago / interval_hours)

        # Create a scheduled bulk export in the database with no end time
        scheduled_export_payload = schemas.BulkExportCreateInternal(
            bulk_export_destination_id=destination.id,
            session_id=project.id,
            start_time=start_time,
            end_time=None,
            filter=None,
            format=schemas.BulkExportFormat.PARQUET,
            compression=schemas.BulkExportCompression.GZIP,
            interval_hours=interval_hours,
            source_bulk_export_id=None,
        )
        scheduled_export = await bulk_exports_crud.create_bulk_export(
            auth, scheduled_export_payload
        )

        # Verify it was created with the correct status
        assert scheduled_export.status == schemas.BulkExportStatus.INTERVAL_SCHEDULED
        assert scheduled_export.interval_hours == interval_hours
        assert scheduled_export.start_time == start_time
        assert scheduled_export.end_time is None
        assert scheduled_export.filter is None
        assert scheduled_export.source_bulk_export_id is None

        # Run the cron job
        await bulk_export_jobs.cron_schedule_bulk_exports_with_interval()

        # Check that a new bulk export was created
        all_exports = await bulk_exports_crud.list_bulk_exports(auth)
        assert len(all_exports) == 2

        # Find the new export (the one with source_bulk_export_id)
        new_export = None
        for exp in all_exports:
            if exp.source_bulk_export_id == scheduled_export.id:
                new_export = exp
                break

        assert new_export is not None, (
            f"No new export found for scheduled export {scheduled_export.id}"
        )
        assert new_export.status == schemas.BulkExportStatus.CREATED
        assert new_export.interval_hours is None
        assert new_export.source_bulk_export_id == scheduled_export.id
        assert (
            new_export.bulk_export_destination_id
            == scheduled_export.bulk_export_destination_id
        )
        assert new_export.session_id == scheduled_export.session_id
        assert new_export.format == scheduled_export.format
        assert new_export.compression == scheduled_export.compression
        assert new_export.filter == scheduled_export.filter

        # Verify the time window is correct (from scheduled export start time to interval hours later)
        expected_start = scheduled_export.start_time
        expected_end = scheduled_export.start_time + datetime.timedelta(
            hours=interval_hours
        )
        assert new_export.start_time == expected_start
        assert new_export.end_time == expected_end

        # Verify I can't create a new export with the same time window
        conflicting_payload = schemas.BulkExportCreateInternal(
            bulk_export_destination_id=destination.id,
            session_id=new_export.session_id,
            start_time=new_export.start_time,
            end_time=new_export.end_time,
            filter=new_export.filter,
            format=new_export.format,
            compression=new_export.compression,
            interval_hours=new_export.interval_hours,
            source_bulk_export_id=scheduled_export.id,
        )
        with pytest.raises(asyncpg.UniqueViolationError):
            await bulk_exports_crud.create_bulk_export(auth, conflicting_payload)

        # Run the cron job 2 more times, should create another export each time
        # with each time window starting at the end of the previous export
        expected_start = new_export.end_time
        expected_end = expected_start + datetime.timedelta(hours=interval_hours)
        for i in range(
            time_ranges - 2
        ):  # 2 because we already created 1 export and we expect 1 to be skipped
            await bulk_export_jobs.cron_schedule_bulk_exports_with_interval()
            all_exports = await bulk_exports_crud.list_bulk_exports(auth)
            assert len(all_exports) == 2 + i + 1
            newest_export = all_exports[0]
            assert newest_export.status == schemas.BulkExportStatus.CREATED
            assert newest_export.interval_hours is None
            assert newest_export.source_bulk_export_id == scheduled_export.id
            assert (
                newest_export.bulk_export_destination_id
                == scheduled_export.bulk_export_destination_id
            )
            assert newest_export.session_id == scheduled_export.session_id
            assert newest_export.format == scheduled_export.format
            assert newest_export.compression == scheduled_export.compression
            assert newest_export.filter == scheduled_export.filter
            assert newest_export.start_time == expected_start
            assert newest_export.end_time == expected_end
            expected_start = newest_export.end_time
            expected_end = expected_start + datetime.timedelta(hours=interval_hours)

        # Run the cron job again, should not create another export due to the buffer time
        await bulk_export_jobs.cron_schedule_bulk_exports_with_interval()
        all_exports = await bulk_exports_crud.list_bulk_exports(auth)
        assert len(all_exports) == 4  # 1 scheduled + 3 created


@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_scheduled_bulk_export_with_start_time_in_future(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Test that the scheduled bulk export cron job creates new exports correctly."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await bulk_exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        start_time_hours_from_now = 24
        start_time = datetime.datetime.now(timezone.utc) + datetime.timedelta(
            hours=start_time_hours_from_now
        )
        interval_hours = 6

        # Create a scheduled bulk export in the database with no end time
        scheduled_export_payload = schemas.BulkExportCreateInternal(
            bulk_export_destination_id=destination.id,
            session_id=project.id,
            start_time=start_time,
            end_time=None,
            filter=None,
            format=schemas.BulkExportFormat.PARQUET,
            compression=schemas.BulkExportCompression.GZIP,
            interval_hours=interval_hours,
            source_bulk_export_id=None,
        )
        scheduled_export = await bulk_exports_crud.create_bulk_export(
            auth, scheduled_export_payload
        )

        # Verify it was created with the correct status
        assert scheduled_export.status == schemas.BulkExportStatus.INTERVAL_SCHEDULED
        assert scheduled_export.interval_hours == interval_hours
        assert scheduled_export.start_time == start_time
        assert scheduled_export.end_time is None
        assert scheduled_export.filter is None
        assert scheduled_export.source_bulk_export_id is None

        # Run the cron job
        await bulk_export_jobs.cron_schedule_bulk_exports_with_interval()

        # Check that no new bulk export was created
        all_exports = await bulk_exports_crud.list_bulk_exports(auth)
        assert len(all_exports) == 1

        only_export = all_exports[0]
        assert only_export.id == scheduled_export.id


@patch("app.models.bulk_exports.crud.validate_bulk_export_destination_connection")
async def test_scheduled_bulk_export_invalid_params(
    mock_validate: Mock,
    db_asyncpg: asyncpg.Connection,
    use_api_key: bool,
):
    """Test that the scheduled bulk export cron job creates new exports correctly."""
    async with fresh_tenant_client(
        db_asyncpg,
        use_api_key,
        tenant_config=SHARED_TENANT_DEFAULT_CONFIG_RBAC_ENABLED_MULTIPLE_WORKSPACES,
    ) as authed_client:
        auth = authed_client.auth

        project = await crud.create_tracer_session(auth, schemas.TracerSessionCreate())
        destination = await bulk_exports_crud.create_bulk_export_destination(
            auth, TEST_DESTINATION
        )
        now = datetime.datetime.now(timezone.utc)
        start_end_time_interval = 24
        interval_hours = 6
        filter = "eq(run_type, 'chain')"
        start_time = now - datetime.timedelta(hours=start_end_time_interval)
        end_time = now + datetime.timedelta(hours=start_end_time_interval)

        # Create a scheduled bulk export in the database with an end time
        payload_orig = schemas.BulkExportCreateInternal(
            bulk_export_destination_id=destination.id,
            session_id=project.id,
            start_time=start_time,
            end_time=end_time,
            filter=filter,
            format=schemas.BulkExportFormat.PARQUET,
            compression=schemas.BulkExportCompression.GZIP,
            interval_hours=interval_hours,
            source_bulk_export_id=None,
        )

        # End time is not supported for scheduled exports
        payload = payload_orig.model_copy(update={"end_time": end_time})
        with pytest.raises(HTTPException) as ex:
            await bulk_exports_crud.create_bulk_export(auth, payload)
        assert ex.value.status_code == 400

        # End time is required for non-scheduled exports
        payload = payload_orig.model_copy(
            update={"end_time": None, "interval_hours": None}
        )
        with pytest.raises(HTTPException) as ex:
            await bulk_exports_crud.create_bulk_export(auth, payload)
        assert ex.value.status_code == 400

        # Filter must be a valid filter
        payload = payload_orig.model_copy(update={"filter": "invalid"})
        with pytest.raises(HTTPException) as ex:
            await bulk_exports_crud.create_bulk_export(auth, payload)
        assert ex.value.status_code == 400

        # Interval hours must be between 1 and 168
        payload = payload_orig.model_copy(update={"interval_hours": 0})
        with pytest.raises(HTTPException) as ex:
            await bulk_exports_crud.create_bulk_export(auth, payload)
        assert ex.value.status_code == 400
        payload = payload_orig.model_copy(update={"interval_hours": 169})
        with pytest.raises(HTTPException) as ex:
            await bulk_exports_crud.create_bulk_export(auth, payload)
        assert ex.value.status_code == 400

from app.models.utils import get_first_path


def test_get_first_path_successful():
    """Test that get_first_path returns value when path exists."""
    # Test with a single path
    data = {"a": {"b": {"c": "value"}}}
    result = get_first_path(data, ["a", "b", "c"])
    assert result == "value"

    # Test with multiple paths, first path exists
    result = get_first_path(data, ["a", "b", "c"], ["x", "y", "z"])
    assert result == "value"

    # Test with multiple paths, second path exists
    data = {"x": {"y": {"z": "other_value"}}}
    result = get_first_path(data, ["a", "b", "c"], ["x", "y", "z"])
    assert result == "other_value"

    # Test with multiple paths, both paths exists
    data = {"x": {"y": {"z1": "value", "z2": "other_value"}}}
    result = get_first_path(data, ["x", "y", "z1"], ["x", "y", "z2"])
    assert result == "value"


def test_get_first_path_missing_paths():
    """Test get_first_path when all paths are missing."""
    data = {"a": {"b": "value"}}

    # All paths missing
    result = get_first_path(data, ["x", "y", "z"], ["p", "q", "r"])
    assert result is None

    # Path partially exists
    result = get_first_path(data, ["a", "b", "c"])
    assert result is None


def test_get_first_path_type_error():
    """Test get_first_path when a TypeError would be raised."""
    # Try to access key in a non-dict
    data = {"a": 5}
    result = get_first_path(data, ["a", "b"])
    assert result is None

    # List instead of dict
    data = {"a": [1, 2, 3]}
    result = get_first_path(data, ["a", "b"])
    assert result is None


def test_get_first_path_empty_paths():
    """Test get_first_path with empty paths."""
    data = {"a": "value"}

    # Empty path list
    result = get_first_path(data, [])
    assert result == data

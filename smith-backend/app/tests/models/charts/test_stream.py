from collections import defaultdict
from datetime import datetime, timedelta, timezone
from unittest.mock import patch

import pytest

from app import schemas
from app.models.charts.stream import (
    _group_by_to_filters,
    _unpack_feedback_datapoints,
)
from app.models.charts.utils import (
    _floor_to_nearest_multiple,
    get_prefetch_enabled_start_end_time,
    get_prefetch_end_time,
)


def make_feedback_series_obj(
    series: dict, feedback_key: str = "key1"
) -> schemas.CustomChartSeries:
    return schemas.CustomChartSeries(
        id=series["id"],
        name=f"series-{series['id']}",
        metric=series.get("metric", schemas.CustomChartMetric.feedback_values),
        feedback_key=feedback_key,
        filters=None,
        workspace_id=None,
        group_by=None,
    )


def test__unpack_categorical_feedback_datapoints() -> None:
    series_id = "series1"
    chart_id = "chart1"
    ts = datetime(2023, 1, 1, tzinfo=timezone.utc)
    val = {"key1": {"values": {"A": 2, "B": 3}, "avg": None}}
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=val, group=None
    )
    all_series_data = [dp]

    chart: dict = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 2
    got_ids = {r.series_id for r in result}
    assert got_ids == {f"{series_id}:A", f"{series_id}:B"}

    by_id = {r.series_id: r.value for r in result}
    assert by_id[f"{series_id}:A"] == 2
    assert by_id[f"{series_id}:B"] == 3

    series_ids_in_chart = {s["id"] for s in chart["series"]}
    assert series_ids_in_chart == {f"{series_id}:A", f"{series_id}:B"}

    assert series_id_to_chart_id[f"{series_id}:A"] == chart_id
    assert series_id_to_chart_id[f"{series_id}:B"] == chart_id
    assert f"{series_id}:A" in series_id_to_series_obj
    assert f"{series_id}:B" in series_id_to_series_obj


def test__unpack_categorical_feedback_datapoints_no_categoricals() -> None:
    series_id = "s2"
    chart_id = "c2"
    ts = datetime(2023, 2, 2, tzinfo=timezone.utc)
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id,
        timestamp=ts,
        value={"key1": {"values": {}, "avg": 5}},
        group=None,
    )
    all_series_data = [dp]

    chart: dict = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 1
    out = result[0]
    assert out.series_id == series_id
    assert out.value == 5

    assert chart["series"][0]["id"] == series_id


def test__unpack_categorical_feedback_datapoints_no_feedback():
    series_id = "s3"
    chart_id = "c3"
    ts = datetime(2023, 3, 3, tzinfo=timezone.utc)
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=42, group=None
    )
    all_series_data = [dp]

    chart = {"series": [{"id": series_id}], "id": chart_id}
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {}
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert result == [dp]


def test__unpack_categorical_feedback_datapoints_wrong_metric() -> None:
    series_id = "series1"
    chart_id = "chart1"
    ts = datetime(2023, 1, 1, tzinfo=timezone.utc)
    val = {"key1": {"values": {"A": 2, "B": 3}, "avg": None}}
    dp = schemas.CustomChartsDataPoint(
        series_id=series_id, timestamp=ts, value=val, group=None
    )
    all_series_data = [dp]

    chart: dict = {
        "series": [{"id": series_id, "metric": "feedback_score_avg"}],
        "id": chart_id,
    }
    chart_id_to_chart = {chart_id: chart}
    series_id_to_chart_id = {series_id: chart_id}
    series_id_to_series_obj = {
        s["id"]: make_feedback_series_obj(s) for s in chart["series"]
    }
    categorical_feedback_keys_to_categories: dict[str, set[str]] = defaultdict(set)

    result = _unpack_feedback_datapoints(
        all_series_data,
        chart_id_to_chart,
        series_id_to_chart_id,
        series_id_to_series_obj,
        categorical_feedback_keys_to_categories,
    )

    assert len(result) == 1
    got_ids = {r.series_id for r in result}
    assert got_ids == {series_id}

    by_id = {r.series_id: r.value for r in result}
    assert by_id[series_id] is None


def test_floor_to_nearest_multiple_basic():
    assert _floor_to_nearest_multiple(10, 3) == 9
    assert _floor_to_nearest_multiple(10, 5) == 10
    assert _floor_to_nearest_multiple(10, 10) == 10

    assert _floor_to_nearest_multiple(0, 5) == 0
    assert _floor_to_nearest_multiple(4, 5) == 0


def testget_prefetch_end_time():
    dt = datetime(2023, 5, 15, 10, 37, 42, 123456, tzinfo=timezone.utc)
    expected = datetime(2023, 5, 15, 10, 30, 0, 0, tzinfo=timezone.utc)
    assert get_prefetch_end_time(dt, 15) == expected

    dt = datetime(2023, 5, 15, 10, 37, 42, 123456, tzinfo=timezone.utc)
    expected = datetime(2023, 5, 15, 10, 0, 0, 0, tzinfo=timezone.utc)
    assert get_prefetch_end_time(dt, 60) == expected

    dt = datetime(2023, 5, 15, 10, 45, 42, 123456, tzinfo=timezone.utc)
    expected = datetime(2023, 5, 15, 10, 30, 0, 0, tzinfo=timezone.utc)
    assert get_prefetch_end_time(dt, 30) == expected

    dt = datetime(2023, 5, 15, 10, 30, 0, 0, tzinfo=timezone.utc)
    expected = datetime(2023, 5, 15, 10, 30, 0, 0, tzinfo=timezone.utc)
    assert get_prefetch_end_time(dt, 30) == expected


def testget_prefetch_end_time_invalid_refresh_rates():
    dt = datetime(2023, 5, 15, 10, 30, 0, 0, tzinfo=timezone.utc)

    with pytest.raises(ValueError, match="Refresh rate must be at least 1 minute"):
        get_prefetch_end_time(dt, 0)

    with pytest.raises(ValueError, match="Refresh rates must be less than 24 hours"):
        get_prefetch_end_time(dt, 24 * 60 + 1)


@patch("app.models.charts.utils.settings")
def test_get_prefetch_enabled_start_end_time_eight_hour_stride(mock_settings):
    mock_settings.PREFETCH_PREBUILT_DASHBOARDS_REFRESH_RATE_MINUTES = 60

    current_time = datetime(2023, 5, 15, 10, 37, 42, 123456, tzinfo=timezone.utc)
    time_window_days = 7
    stride_hrs = 8

    start_time, end_time = get_prefetch_enabled_start_end_time(
        current_time, time_window_days, stride_hrs
    )

    # End time should be rounded to 9:00
    expected_end = datetime(2023, 5, 15, 9, 0, 0, 0, tzinfo=timezone.utc)
    assert end_time == expected_end

    # Start time should be 7 days before, with hour set to 8 (closest multiple of 8 below 10)
    expected_start = (expected_end - timedelta(days=7)).replace(hour=8, minute=0)
    assert start_time == expected_start


@patch("app.models.charts.utils.settings")
def test_get_prefetch_enabled_start_end_time_twelve_hour_stride(mock_settings):
    mock_settings.PREFETCH_PREBUILT_DASHBOARDS_REFRESH_RATE_MINUTES = 15

    current_time = datetime(2023, 5, 15, 14, 20, 0, 0, tzinfo=timezone.utc)
    time_window_days = 14
    stride_hrs = 12

    start_time, end_time = get_prefetch_enabled_start_end_time(
        current_time, time_window_days, stride_hrs
    )

    # End time should be rounded to 14:00
    expected_end = datetime(2023, 5, 15, 14, 0, 0, 0, tzinfo=timezone.utc)
    assert end_time == expected_end

    # Start time should be 14 days before, with hour set to 12 (closest multiple of 12 below 14)
    expected_start = (expected_end - timedelta(days=14)).replace(hour=12, minute=0)
    assert start_time == expected_start


def test_group_by_to_filters_metadata():
    group_by = schemas.RunStatsGroupBy(attribute="metadata", path="ls_run_depth")

    # Numbers should be unquoted
    result = _group_by_to_filters(group_by, "2")
    assert (
        result["filter"]
        == 'and(eq(metadata_key, "ls_run_depth"), eq(metadata_value, 2))'
    )

    result = _group_by_to_filters(group_by, "3.14")
    assert (
        result["filter"]
        == 'and(eq(metadata_key, "ls_run_depth"), eq(metadata_value, 3.14))'
    )

    # Strings should be quoted
    group_by = schemas.RunStatsGroupBy(attribute="metadata", path="env")
    result = _group_by_to_filters(group_by, "production")
    assert (
        result["filter"]
        == 'and(eq(metadata_key, "env"), eq(metadata_value, "production"))'
    )


def test_group_by_to_filters_other_attributes():
    group_by = schemas.RunStatsGroupBy(attribute="name", path="")
    result = _group_by_to_filters(group_by, "test_name")
    assert result["filter"] == 'eq(name, "test_name")'

import pytest
from fastapi.exceptions import HTTPException

from app.models.query_lang.parse import parse_as_filter_directive_ftsearch
from app.models.query_lang.translate_qw import QuickwitVisitor
from app.models.runs.qw_attrs import get_quickwit_attributes


@pytest.mark.parametrize(
    "query_str,expected",
    [
        (
            "eq(session_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8')",
            "session_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8",
        ),
        (
            "search('foo bar')",
            '(inputs_flat:"foo bar" OR outputs_flat:"foo bar" OR error:"foo bar")',
        ),
        ("eq(inputs_text, 'some text')", 'inputs_flat:"some text"'),
        ("eq(outputs_text, 'xyz')", "outputs_flat:xyz"),
        ("eq(error, 'oops*')", "error:oops*"),
        ("eq(error, 'oops?')", "error:oops?"),
        ("eq(inputs_json.key1.key2, 'val1')", "inputs.key1.key2:val1"),
        ("eq(inputs_json.key1.key2, 'val1*')", "inputs.key1.key2:val1*"),
        ("eq(outputs_json.key.nested, 'val2')", "outputs.key.nested:val2"),
        ("eq(extra.some_key, 123)", "extra.some_key:123"),
        ("eq(is_root, true)", "is_root:true"),
        ("eq(name, 'test-run')", "name:test-run"),
        ("eq(run_type, 'chain')", "run_type:chain"),
        ("has(tags, 'some-tag')", "tags:some-tag"),  # TODO should use has for tags
        ("eq(status, 'finished')", "status:finished"),
        (
            "eq(trace_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8')",
            "trace_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8",
        ),
        (
            "eq(thread_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8')",
            "thread_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8",
        ),
        (
            "eq(start_time, '2023-01-01T00:00:00Z')",
            "start_time_micros:1672531200000000",
        ),
        (
            "gt(start_time, '2023-01-01T00:00:00.123Z')",
            "start_time_micros:>1672531200123000 AND start_time:>2023-01-01T00:00:00Z",
        ),
        (
            "gte(start_time, '2023-01-01T00:00:00.123Z')",
            "start_time_micros:>=1672531200123000 AND start_time:>=2023-01-01T00:00:00Z",
        ),
        (
            "lt(start_time, '2023-01-01T00:00:00Z')",
            "start_time_micros:<1672531200000000 AND start_time:<2023-01-01T00:00:01Z",
        ),
        (
            "lte(start_time, '2023-01-01T00:00:00Z')",
            "start_time_micros:<=1672531200000000 AND start_time:<2023-01-01T00:00:01Z",
        ),
        ("search('foo')", "(inputs_flat:foo OR outputs_flat:foo OR error:foo)"),
    ],
)
def test_quickwit_comparison(query_str, expected):
    """
    Tests single-comparison directives.
    """
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected


@pytest.mark.parametrize(
    "query_str,expected",
    [
        (
            "and(eq(session_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), gt(start_time, '2023-01-01T00:00:00Z'))",
            "(session_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8) AND (start_time_micros:>1672531200000000 AND start_time:>2023-01-01T00:00:00Z)",
        ),
        (
            "or(eq(session_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), eq(session_id, '20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'))",
            "(session_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8) OR (session_id:20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8)",
        ),
        (
            "not(eq(status, 'finished'), eq(session_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'))",
            "NOT ((status:finished) OR (session_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8))",
        ),
        (
            "and(eq(tenant_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), or(eq(session_id, '20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), eq(session_id, '30bfdfd1-c8e5-4635-882b-ebdb57ad6ca8')))",
            "(tenant_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8) AND ((session_id:20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8) OR (session_id:30bfdfd1-c8e5-4635-882b-ebdb57ad6ca8))",
        ),
    ],
)
def test_quickwit_operations(query_str, expected):
    """
    Tests AND / OR / NOT operators combining sub-clauses.
    """
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected


@pytest.mark.parametrize(
    "query_str",
    [
        "eq(name.some_subkey, 'val')",
        "eq(this_is_not_real, 'val')",
    ],
)
def test_quickwit_bad_attribute(query_str):
    """
    Verify that we raise an error when the attribute is invalid or doesn't match the schema.
    """
    with pytest.raises(ValueError):
        directive = parse_as_filter_directive_ftsearch(query_str)
        visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
        _ = directive.accept(visitor)


@pytest.mark.parametrize(
    "query_str",
    [
        # Not implemented comparator
        "like(name, 'foo')",
        "neq(status, 'bar')",
        "in(tags, 'bar')",
    ],
)
def test_quickwit_unsupported_comparator(query_str):
    """
    Verifies that we raise a ValueError for unsupported comparators.
    """
    with pytest.raises(HTTPException):
        directive = parse_as_filter_directive_ftsearch(query_str)
        visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
        _ = directive.accept(visitor)


@pytest.mark.parametrize(
    "query_str",
    [
        ("eq(start_time, 'not-a-datetime')"),
        ("eq(session_id, 'not-a-uuid')"),
        ("eq(is_root, 'maybe?')"),
        ("has(status, 'foo')"),
        ("gt(is_root, 'true')"),
        ("eq(tags, 123)"),
        ("eq(session_id, 12345)"),
    ],
)
def test_quickwit_type_mismatch_errors(query_str):
    """
    Expect ValueError for invalid combos of field type + value or comparator.
    """
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    with pytest.raises(ValueError):
        _ = directive.accept(visitor)


@pytest.mark.parametrize(
    "query_str,expected",
    [
        (
            "gt(cursor, '1672531200000000|1234567890123456789')",
            "((start_time:>2023-01-01T00:00:00Z) AND ((start_time_micros:>1672531200000000) OR (start_time_micros:1672531200000000 AND id_sort:>1234567890123456789)))",
        ),
        (
            "lt(cursor, '1672531200000000|1234567890123456789')",
            "((start_time:<2023-01-01T00:00:01Z) AND ((start_time_micros:<1672531200000000) OR (start_time_micros:1672531200000000 AND id_sort:<1234567890123456789)))",
        ),
    ],
)
def test_quickwit_cursor(query_str, expected):
    """
    Tests cursor-based filtering.
    """
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected


@pytest.mark.parametrize(
    "query_str, expected",
    [
        # single key existence check
        (
            'eq(input_key, "foo.bar")',
            "inputs.foo.bar:*",
        ),
        (
            'eq(output_value, "bar")',
            "outputs_flat:bar",
        ),
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'))",
            "(inputs.foo.bar:xyz)",
        ),
        (
            "and(eq(output_value, 'val123'), eq(output_key, 'nested.path'))",
            "(outputs.nested.path:val123)",
        ),
        (
            "and(eq(extra_key, 'someKey'), eq(extra_value, 'someVal'))",
            "(extra.someKey:someVal)",
        ),
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), eq(output_value, 'val123'), eq(output_key, 'nested.path'))",
            "(inputs.foo.bar:xyz) AND (outputs.nested.path:val123)",
        ),
        # two input key/value pairs
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), eq(input_key, 'foo.bar2'), eq(input_value, 'xyz2'))",
            "(inputs.foo.bar:xyz) AND (inputs.foo.bar2:xyz2)",
        ),
        # left over key
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), eq(input_value, 'val123'), eq(input_key, 'foo.bar2'), eq(input_key, 'foo.bar3'))",
            "(inputs.foo.bar:xyz) AND (inputs.foo.bar2:val123) AND (inputs.foo.bar3:*)",
        ),
        # left over value
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), eq(input_value, 'val123'))",
            "(inputs.foo.bar:xyz) AND (inputs_flat:val123)",
        ),
        (
            'and(and(eq(input_key, "foo"), eq(input_value, "bar")), and(eq(output_key, "baz"), eq(output_value, "qux")), eq(name, "ChatAnthropic"))',
            "(name:ChatAnthropic) AND (inputs.foo:bar) AND (outputs.baz:qux)",
        ),
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), eq(name, 'extra'))",
            "(name:extra) AND (inputs.foo.bar:xyz)",
        ),
        # AND[a, b, OR[c, d, AND[AND[e, f], g, h]]]
        (
            "and(eq(input_key, 'foo.bar'), eq(input_value, 'xyz'), or(eq(input_key, 'foo.bar2'), eq(input_value, 'val123'), and(and(eq(input_key, 'foo.bar3'), eq(input_value, 'val456')), eq(input_key, 'foo.bar4'), eq(input_value, 'val789'))))",
            "((inputs.foo.bar2:*) OR (inputs_flat:val123) OR ((inputs.foo.bar3:val456) AND (inputs.foo.bar4:val789))) AND (inputs.foo.bar:xyz)",
        ),
    ],
)
def test_key_value_pair_success(query_str, expected):
    """
    These queries use AND with exactly two arguments, recognized as key/value pairs.
    They should rewrite into eq(prefix_json.key.path, val).
    """
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected


@pytest.mark.parametrize(
    "query_str, expected",
    [
        (
            "and(eq(status, 'finished'), eq(name, 'foo'))",
            "(status:finished) AND (name:foo)",
        ),
        (
            "and(eq(status, 'finished'), eq(inputs_json.key1.key2, 'foo'))",
            "(status:finished) AND (inputs.key1.key2:foo)",
        ),
        (
            "and(eq(status, 'finished'), eq(name, 'foo'), eq(is_root, true))",
            "(status:finished) AND (name:foo) AND (is_root:true)",
        ),
    ],
)
def test_and_fallback_normal_usage(query_str, expected):
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected


@pytest.mark.parametrize(
    "query_str,expected",
    [
        ("eq(name, '')", ""),
        ("search('')", ""),
        ("and(eq(name, ''))", ""),
        (
            "and(eq(session_id, '10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), eq(tenant_id, '20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8'), eq(name, ''))",
            "(session_id:10bfdfd1-c8e5-4635-882b-ebdb57ad6ca8) AND (tenant_id:20bfdfd1-c8e5-4635-882b-ebdb57ad6ca8)",
        ),
    ],
)
def test_quickwit_empty_strings(query_str, expected):
    directive = parse_as_filter_directive_ftsearch(query_str)
    visitor = QuickwitVisitor(attributes=get_quickwit_attributes())
    result = directive.accept(visitor)
    assert result["query_str"] == expected

import orjson
import pytest

from app.models.runs.ingest_qw import QuickwitBatch, _serialize_quickwit_docs


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "max_batch_size,skip_over_limit,expected_batch_count,expected_doc_count",
    [
        # Max size that fits all docs in one batch
        (300, False, 1, 5),
        # Max size for 2 batches (first 3 docs, then last 2)
        (150, <PERSON>alse, 2, 5),
        # Max size for 4 batches
        (90, False, 4, 5),
        # Confirm newline accounting (15 doesn't fit in the batch with existing 20+60+2=82, batch needs 97 to fit)
        (96, False, 4, 5),
        # Very small max size (each doc in its own batch)
        (10, False, 5, 5),
        # Small max size with skip_over_limit
        (50, True, 2, 3),
    ],
)
async def test_serialize_quickwit_docs_batching(
    max_batch_size, skip_over_limit, expected_batch_count, expected_doc_count
):
    # Test documents (orjson serialized size = len of value + 10)
    docs = [
        # Small doc (size = 20)
        {"key": "a" * 10},
        # Medium doc (size = 60)
        {"key": "b" * 50},
        # Small doc (size = 15)
        {"key": "c" * 5},
        # Large doc (size = 90)
        {"key": "d" * 80},
        # Medium doc (size = 40)
        {"key": "e" * 30},
    ]

    # Calculate expected total size for validation
    expected_total_size = sum(
        size
        for doc in docs
        if (size := len(orjson.dumps(doc))) < max_batch_size or not skip_over_limit
    )

    # Run the serialization
    serialized = await _serialize_quickwit_docs(
        docs=docs, max_batch_size=max_batch_size, skip_docs_over_limit=skip_over_limit
    )

    # Check return type
    assert isinstance(serialized.batches, list)
    assert all(isinstance(batch, QuickwitBatch) for batch in serialized.batches)

    # Check the correct number of batches was created
    assert len(serialized.batches) == expected_batch_count, (
        f"Expected {expected_batch_count} batches with max size {max_batch_size}, got {len(serialized.batches)}"
    )

    # Check the reported total size
    assert serialized.total_size == expected_total_size, (
        f"Expected total size {expected_total_size}, got {serialized.total_size}"
    )

    # Verify each batch is within the max size limit (unless a single doc is larger)
    total_doc_count = 0

    for i, batch in enumerate(serialized.batches):
        # Check the batch payload type
        assert isinstance(batch.payload, bytes)

        # Count doc_count
        total_doc_count += batch.doc_count

        # Deserialize the batch payload
        batch_docs = [orjson.loads(line) for line in batch.payload.split(b"\n")]

        # Verify doc_count matches the actual number of docs in the payload
        assert len(batch_docs) == batch.doc_count, (
            f"Batch {i} reports {batch.doc_count} docs but contains {len(batch_docs)}"
        )

        # If this is a single document batch that's too large for max_batch_size, we allow it
        if len(batch_docs) == 1 and len(orjson.dumps(batch_docs[0])) > max_batch_size:
            continue

        # Check that the batch size is within limits
        assert len(batch.payload) <= max_batch_size, (
            f"Batch {i} exceeds max size: {len(batch.payload)} > {max_batch_size}"
        )

    # Verify the total doc count matches expected
    assert total_doc_count == expected_doc_count

    # Verify skipped doc count matches expected
    assert len(serialized.over_limit) == len(docs) - expected_doc_count

    # Verify all documents were serialized correctly
    all_deserialized = []
    for batch in serialized.batches:
        batch_docs = [orjson.loads(line) for line in batch.payload.split(b"\n")]
        all_deserialized.extend(batch_docs)

    assert len(all_deserialized) == expected_doc_count
    expected_serialized = [
        run for i, run in enumerate(docs) if i not in serialized.over_limit
    ]
    for original, deserialized in zip(expected_serialized, all_deserialized):
        assert original == deserialized, "Document content changed after serialization"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "docs,max_batch_size,expected_batch_count,expected_total_size",
    [
        # Empty input
        ([], 1000, 0, 0),
        # Single doc larger than max_batch_size
        (
            [{"id": 1, "text": "x" * 1000}],
            500,
            1,
            None,
        ),  # None means calculate dynamically
        # Mixed docs with one larger than max_batch_size
        (
            [
                {"id": 1, "text": "a" * 200},  # Small doc
                {"id": 2, "text": "b" * 800},  # Large doc (> max_size)
                {"id": 3, "text": "c" * 300},  # Small doc
            ],
            500,
            3,
            None,
        ),
    ],
)
async def test_serialize_quickwit_docs_edge_cases(
    docs, max_batch_size, expected_batch_count, expected_total_size
):
    # Calculate expected total size if not provided
    if expected_total_size is None:
        expected_total_size = sum(len(orjson.dumps(doc)) for doc in docs)

    # Run the serialization
    serialized = await _serialize_quickwit_docs(
        docs=docs, max_batch_size=max_batch_size
    )

    # Check the correct number of batches was created
    assert len(serialized.batches) == expected_batch_count, (
        f"Expected {expected_batch_count} batches, got {len(serialized.batches)}"
    )

    # Check the reported total size
    assert serialized.total_size == expected_total_size, (
        f"Expected total size {expected_total_size}, got {serialized.total_size}"
    )

    # Verify all documents were serialized (if any)
    if docs:
        all_deserialized = []
        total_doc_count = 0

        for batch in serialized.batches:
            # Add to the total document count
            total_doc_count += batch.doc_count

            # Deserialize the batch payload
            batch_docs = [orjson.loads(line) for line in batch.payload.split(b"\n")]
            all_deserialized.extend(batch_docs)

            # Verify doc_count matches the actual number of docs in the payload
            assert len(batch_docs) == batch.doc_count

        # Verify the total doc count matches the input
        assert total_doc_count == len(docs), (
            f"Expected {len(docs)} docs, counted {total_doc_count}"
        )

        # Verify content is preserved
        assert len(all_deserialized) == len(docs), (
            "Number of documents changed after serialization"
        )
        for original, deserialized in zip(docs, all_deserialized):
            assert original == deserialized, (
                "Document content changed after serialization"
            )

"""
Repository versioning for cache invalidation.

This module implements a version counter approach for invalidating repository-level
caches. Instead of scanning and deleting cache keys, we increment a version counter
for each repository when commits are created, causing natural cache misses.

Key benefits:
- Single Redis INCR operation (O(1))
- No Redis SCAN operations (production safe)
- Automatic invalidation of all related cache entries
- Natural cache miss strategy (no proactive deletion)
"""

import logging
from typing import Optional

from lc_database.redis import aredis_caching_pool

logger = logging.getLogger(__name__)


def _get_repo_version_key(owner: str, repo: str) -> str:
    """Get the Redis key for a repository's cache version counter."""
    return f"smith:repo_version:{owner}:{repo}"


async def get_repo_cache_version(owner: Optional[str], repo: str) -> int:
    """
    Get the current cache version for a repository.

    If no version exists, automatically initialize it based on the current timestamp.
    This provides a unique starting point without requiring manual setup.

    Args:
        owner: Repository owner
        repo: Repository name

    Returns:
        Current version number (integer)
    """
    if owner is None:
        logger.warning("get_repo_cache_version called with None owner")
        return 1

    key = _get_repo_version_key(owner, repo)

    try:
        async with aredis_caching_pool() as redis:
            version = await redis.get(key)

            if version is not None:
                return int(version)

            # Auto-initialize version counter if it doesn't exist
            # Use current timestamp as starting version to ensure uniqueness
            import time

            initial_version = int(time.time())

            # Use SET NX to avoid race conditions during initialization
            was_set = await redis.set(key, initial_version, nx=True)

            if was_set:
                logger.debug(
                    f"Initialized cache version for {owner}/{repo} to {initial_version}"
                )
                return initial_version
            else:
                # Another process initialized it, get the current value
                version = await redis.get(key)
                return int(version) if version is not None else initial_version

    except Exception as e:
        logger.warning(f"Failed to get repo cache version for {owner}/{repo}: {e}")
        # Return a default version to avoid breaking cache functionality
        return 1


async def increment_repo_cache_version(owner: Optional[str], repo: str) -> int:
    """
    Increment the cache version counter for a repository.

    This invalidates all cache entries for the repository by causing cache misses
    when the version in cache keys no longer matches the current version.

    Args:
        owner: Repository owner
        repo: Repository name

    Returns:
        New version number after increment
    """
    if owner is None:
        logger.warning("increment_repo_cache_version called with None owner")
        return 1

    key = _get_repo_version_key(owner, repo)

    try:
        async with aredis_caching_pool() as redis:
            new_version = await redis.incr(key)
            logger.debug(
                f"Incremented cache version for {owner}/{repo} to {new_version}"
            )
            return new_version

    except Exception as e:
        logger.error(f"Failed to increment repo cache version for {owner}/{repo}: {e}")
        # Don't raise exception to avoid breaking commit creation
        # Return a default value
        return 1

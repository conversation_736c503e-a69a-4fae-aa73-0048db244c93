# ruff: noqa: E402

import asyncio
from functools import wraps

import structlog
import truststore
from ddtrace import tracer
from ddtrace.constants import SPAN_KIND
from ddtrace.ext import SpanKind, SpanTypes
from lc_config.settings import shared_settings
from lc_database.redis import aredis_pool
from opentelemetry import trace as otel_trace
from opentelemetry.trace import SpanKind as OtelSpanKind
from opentelemetry.trace import Status, StatusCode
from saq import Job
from saq.types import Context

from app.api.auth.schemas import AuthInfo
from app.models.runs.tokens import OPENAI_MODELS, get_tiktoken_encoding
from app.utils import arun_in_executor

logger = structlog.getLogger(__name__)

EXCLUDE_TRACING_ARGS = {
    "auth_dict",
    "root_password",
    "run_ids",
}
INCLUDE_AUTH_DICT_KEYS = {
    "flags",
    "identity_id",
    "identity_permissions",
    "tenant_id",
    "organization_id",
    "organization_is_personal",
    "tenant_config",
    "tenant_id",
}

# Keep track of startup tasks for asyncio
startup_task = None


def clear_startup_task(result):
    global startup_task
    result.exception()
    startup_task = None


async def startup_callback(ctx: Context):
    """
    Startup callback for worker. Log the worker startup.
    """
    if shared_settings.SYSTEM_CERTS_ENABLED:
        truststore.inject_into_ssl()
    try:
        async with aredis_pool() as redis:
            # ping redis to test connection before starting
            await redis.ping()
    except Exception as e:
        raise RuntimeError(
            f"Error pinging Redis. Please make sure the Redis cache is running, and that the Redis server is reachable from this service with any provided connection parameters. Error: {e}"
        )

    # Prime tokens for caching
    if not shared_settings.LANGCHAIN_ENV or (
        "local_dev" not in shared_settings.LANGCHAIN_ENV
        and "test" not in shared_settings.LANGCHAIN_ENV
    ):
        global startup_task
        startup_task = asyncio.create_task(prime_token_encodings())
        startup_task.add_done_callback(clear_startup_task)


async def prime_token_encodings():
    """
    Prime the token cache.
    """
    for model in OPENAI_MODELS:
        try:
            logger.info("Priming cache {}".format(model))
            await arun_in_executor(get_tiktoken_encoding, model, "openai")
        except Exception as e:
            logger.error(f"Failed to prime token encodings. {str(e)}")


async def shutdown_callback(ctx: Context):
    """
    Shutdown callback for worker. Give some time for requests to finish before exiting.
    """
    logger.info("Gracefully shutting down worker {}".format(ctx))
    await asyncio.sleep(shared_settings.ASYNC_GRACEFUL_SHUTDOWN_SECS)


def _clean_kwargs_dict(kwargs_dict: dict) -> dict:
    """Return dict without keys in EXCLUDE_TRACING_ARGS."""
    clean_dict = {}
    for k, v in kwargs_dict.items():
        if k not in EXCLUDE_TRACING_ARGS:
            clean_dict[k] = v
    return clean_dict


def set_auth_metadata(auth: AuthInfo):
    """Set the auth metadata for the current span."""
    metadata = {
        "tenant_id": str(auth.tenant_id),
        "organization_id": str(auth.organization_id),
        "user_id": str(auth.user_id),
        "ls_user_id": str(auth.ls_user_id),
    }
    job_vars = structlog.contextvars.get_contextvars()
    if job_vars and job_vars.get("metadata"):
        job_vars.get("metadata").update(metadata)

    structlog.contextvars.bind_contextvars(metadata=metadata)

    current_span = tracer.current_span()
    if current_span is not None:
        for key, value in metadata.items():
            if value is not None:
                current_span.set_tag(f"metadata.{key}", value)


def trace_job(func):
    @wraps(func)
    async def wrapper(*args, **kwargs) -> None:
        ctx = args[0]
        job: Job = ctx["job"]

        if kwargs.get("run_ids"):
            run_ids = kwargs["run_ids"]
        elif kwargs.get("run_id"):
            run_ids = [kwargs["run_id"]]
        else:
            run_ids = None

        # when logging traces and run_ids [[trace_id, run_id], [trace_id, run_id], ...] use run_ids
        if (
            run_ids
            and isinstance(run_ids, list)
            and all(isinstance(item, list) for item in run_ids)
        ):
            run_ids = [run_id for _, run_id in run_ids]

        structlog_metadata = {
            "auth_id": kwargs.get("auth_id"),
            "tenant_id": (kwargs.get("auth_dict") or {}).get("tenant_id"),
            "run_ids": run_ids,
            "organization_id": (kwargs.get("auth_dict") or {}).get("organization_id"),
            "user_id": (kwargs.get("auth_dict") or {}).get("user_id"),
            "ls_user_id": (kwargs.get("auth_dict") or {}).get("ls_user_id"),
        }
        if rules_dict := kwargs.get("rule_dict"):
            structlog_metadata.update(
                {
                    "rule_id": rules_dict.get("id"),
                    "tenant_id": rules_dict.get("tenant_id"),
                    "session_id": rules_dict.get("session_id"),
                }
            )

        # Setup contextvars for logging
        structlog.contextvars.clear_contextvars()
        structlog.contextvars.bind_contextvars(
            job_id=job.id,
            job_attempts=job.attempts,
            job_func_name=job.function,
            lb_trace_context=kwargs.get("lb_trace_context"),
            api_key_short=kwargs.get("api_key_short"),
            rule_id=kwargs.get("rule_id"),
            metadata=structlog_metadata,
        )

        if (
            not shared_settings.DATADOG_ENABLED
            and not shared_settings.OTEL_TRACING_ENABLED
        ):
            return await func(*args, **kwargs)

        if shared_settings.DATADOG_ENABLED:
            with tracer.trace(
                f"async_worker.{job.function}",
                span_type=SpanTypes.WORKER,
                resource=job.function,
            ) as span:
                span.set_tag_str(SPAN_KIND, SpanKind.CONSUMER)
                span.set_tag_str("job.id", job.id)
                span.set_tag_str("job.attempts", str(job.attempts))
                span.set_tag_str("job.func_name", job.function)

                # exclude job args and nested dict keys from tracing metadata
                for k, val in kwargs.items():
                    if k in EXCLUDE_TRACING_ARGS:
                        continue
                    if isinstance(val, dict):
                        val = _clean_kwargs_dict(val)
                    span.set_tag(f"metadata.{k}", val)

                if run_ids:
                    span.set_tag("metadata.run_ids", run_ids)
                if auth_dict := kwargs.get("auth_dict"):
                    for k, val in auth_dict.items():
                        if k in INCLUDE_AUTH_DICT_KEYS:
                            span.set_tag(f"metadata.auth_dict.{k}", val)

                if rules_dict := kwargs.get("rule_dict"):
                    span.set_tag("rule_id", rules_dict.get("id"))
                    span.set_tag("tenant_id", rules_dict.get("tenant_id"))
                    span.set_tag("session_id", rules_dict.get("session_id"))

                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    span.error = 1
                    raise e
        elif shared_settings.OTEL_TRACING_ENABLED:
            with otel_trace.get_tracer(__name__).start_as_current_span(
                f"async_worker.{job.function}",
                kind=OtelSpanKind.CONSUMER,
            ) as span:
                span.set_attribute("job.id", job.id)
                span.set_attribute("job.attempts", str(job.attempts))
                span.set_attribute("job.func_name", job.function)

                for k, val in kwargs.items():
                    if k in EXCLUDE_TRACING_ARGS:
                        continue
                    if isinstance(val, dict):
                        val = _clean_kwargs_dict(val)
                    span.set_attribute(f"metadata.{k}", str(val))

                if run_ids:
                    span.set_attribute("metadata.run_ids", str(run_ids))
                if auth_dict := kwargs.get("auth_dict"):
                    for k, val in auth_dict.items():
                        if k in INCLUDE_AUTH_DICT_KEYS:
                            span.set_attribute(f"metadata.auth_dict.{k}", str(val))

                if rules_dict := kwargs.get("rule_dict"):
                    span.set_attribute("rule_id", str(rules_dict.get("id")))
                    span.set_attribute("tenant_id", str(rules_dict.get("tenant_id")))
                    span.set_attribute("session_id", str(rules_dict.get("session_id")))

                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    span.record_exception(e)
                    span.set_status(Status(StatusCode.ERROR))
                    raise e

    return wrapper

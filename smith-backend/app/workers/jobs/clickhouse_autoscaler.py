from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Any, Literal

import structlog
from httpx import AsyncClient
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import (
    ClickhouseClient,
    clickhouse_client,
)

from app.workers.utils import trace_job

logger = structlog.getLogger(__name__)

ScalingDecision = Literal["skip", "scale-up", "scale-down"]

FETCH_AUTOSCALING_METRICS_QUERY = f"""
select
    metric,
    value
from
    (
        select
            'lag60_memory_utilization_pct' as metric,
            (
                sum(mem_usage) / (
                    select
                        sum(toUInt64(getSetting('max_memory_usage')))
                    from
                        clusterAllReplicas('default', 'system.one')
                )
            ) * 100 as value
        from
            (
                select
                    hostname(),
                    avg(CurrentMetric_MemoryTracking) as mem_usage
                from
                    clusterAllReplicas('default', 'system.metric_log')
                where
                    event_date >= toDate(now() - INTERVAL 10 MINUTE - INTERVAL 60 SECOND)
                    AND event_time BETWEEN (now() - INTERVAL 10 MINUTE - INTERVAL 60 SECOND)
                    AND now()
                group by
                    hostname()
            )
        union all
        select
            'lag60_cpu_utilization_pct' as metric,
            sum(avgs) * 100 as value
        from
            (
                select
                    avg(value) as avgs
                from
                    clusterAllReplicas('default', 'system.asynchronous_metric_log')
                where
                    event_date >= toDate(now() - INTERVAL 10 MINUTE - INTERVAL 60 SECOND)
                    AND event_time BETWEEN (now() - INTERVAL 10 MINUTE - INTERVAL 60 SECOND)
                    AND now()
                    and metric in (
                        'OSGuestNiceTimeNormalized',
                        'OSGuestTimeNormalized',
                        'OSIOWaitTimeNormalized',
                        'OSIdleTimeNormalized',
                        'OSIrqTimeNormalized',
                        'OSNiceTimeNormalized',
                        'OSSoftIrqTimeNormalized',
                        'OSStealTimeNormalized',
                        'OSSystemTimeNormalized',
                        'OSUserTimeNormalized'
                    )
                group by
                    metric
            )
        union all
        select
            'lag60_total_allocated_memory' as metric,
            sum(toUInt64(getSetting('max_memory_usage'))) / 1024 / 1024 / 1024 as value
        from
            clusterAllReplicas('default', 'system.one')
    ) settings skip_unavailable_shards = 1,
        max_execution_time={settings.CH_UPGRADE_MAX_EXECUTION_TIME}
"""


@trace_job
async def autoscale_clickhouse_nodes(ctx: dict):
    for cluster_name, cfg in settings.CLICKHOUSE_CLUSTERS.items():
        if not cfg.get("enabled"):
            await logger.ainfo("Skipping disabled cluster", cluster=cluster_name)
            continue
        await autoscale_clickhouse_cluster(ctx, cluster_name, cfg)


@trace_job
async def autoscale_clickhouse_cluster(
    ctx: dict, cluster_name: str, cfg: dict[str, Any]
) -> None:
    key_id = settings.CLICKHOUSE_API_KEY_ID
    key_secret = settings.CLICKHOUSE_API_KEY_SECRET
    client_str = cfg["client"]

    # TODO Remove this once deployed
    client_map = {
        "DEFAULT": "INGESTION",
        "IN_APP_ANALYTICS": "USER_QUERIES",
        "IN_APP_STATS": "USER_ANALYTICS",
    }
    mapped_client = client_map.get(client_str, client_str)
    client_enum = ClickhouseClient[mapped_client]
    org_id = cfg["org_id"]
    service_id = cfg["service_id"]
    auth = (key_id, key_secret)
    replica_limits = cfg.get("replica_limits", {})
    scaling_cfg = cfg.get("scaling_config", {})
    cpu_up = scaling_cfg.get("cpu_up", 25)
    mem_up = scaling_cfg.get("mem_up", 80)
    cpu_down = scaling_cfg.get("cpu_down", 20)
    mem_down = scaling_cfg.get("mem_down", 75)
    dry_run = scaling_cfg.get("dry_run", True)
    force = scaling_cfg.get("force", False)
    stabilization_minutes = scaling_cfg.get("stabilization_minutes", 5)

    api_base = settings.CLICKHOUSE_CLOUD_API_BASE_URL
    service_url = f"{api_base}/v1/organizations/{org_id}/services/{service_id}"
    scaling_url = f"{service_url}/scaling"

    # validating the replica limits are defined in config per cluster
    if "min" not in replica_limits or "max" not in replica_limits:
        raise ValueError(
            f"Missing replica_limits['min'] or ['max'] for cluster {cluster_name}"
        )

    lock_key = f"clickhouse_autoscale_lock:{service_id}"
    lock_ttl_sec = 300  # 5 minutes
    async with redis.aredis_pool() as aredis:
        lock = aredis.lock(lock_key, timeout=lock_ttl_sec, blocking=False)
        if not await lock.acquire():
            await logger.awarning(
                "Autoscaler lock already held — skipping this run",
                lock_key=lock_key,
                service_id=service_id,
            )
            return

    try:
        await logger.ainfo("Starting ClickHouse autoscaler job")

        async with AsyncClient(auth=auth) as client:
            await logger.ainfo("Fetching service info", url=service_url)
            svc_resp = await client.get(service_url)
            svc_resp.raise_for_status()
            service = svc_resp.json()["result"]
            await logger.ainfo(
                "Service data parsed",
                service_id=service.get("id"),
                num_replicas=service.get("numReplicas"),
            )

        current_replicas = service["numReplicas"]
        max_mem_per_replica = service["maxReplicaMemoryGb"]
        memory_limit = current_replicas * max_mem_per_replica

        # Retrieve per-cluster replica limits
        min_replicas = replica_limits.get("min")
        max_replicas = replica_limits.get("max")

        await logger.ainfo(
            "Fetched service config",
            service_name=service.get("name"),
            num_replicas=current_replicas,
            max_replica_memory_gb=max_mem_per_replica,
            total_memory_limit_gb=memory_limit,
        )

        async with clickhouse_client(client_enum) as ch:
            rows = await ch.fetch(
                "fetch_autoscaling_metrics", FETCH_AUTOSCALING_METRICS_QUERY
            )

        if not rows:
            await logger.awarning("No metrics returned from ClickHouse")
            raise RuntimeError("ClickHouse autoscaler: no metrics returned")

        metrics = {row["metric"]: row["value"] for row in rows}
        cpu_util = metrics.get("lag60_cpu_utilization_pct", 0)
        mem_util = metrics.get("lag60_memory_utilization_pct", 0)
        allocated_mem = metrics.get("lag60_total_allocated_memory", 0)
        clickhouse_metrics_log = {
            "cpu_util_pct": round(cpu_util, 2),
            "mem_util_pct": round(mem_util, 2),
            "allocated_memory_gb": round(allocated_mem, 2),
        }

        await logger.ainfo("Fetched ClickHouse metrics", **clickhouse_metrics_log)

        if any(v == 0 for v in [cpu_util, mem_util, allocated_mem]):
            raise RuntimeError(
                "ClickHouse autoscaler: One or more metrics unexpectedly returned 0 — query may be malformed",
                {
                    "cpu_util": cpu_util,
                    "mem_util": mem_util,
                    "allocated_mem": allocated_mem,
                },
            )
        mem_limit_pct = (allocated_mem / memory_limit) * 100 if memory_limit else 0

        now = datetime.now()
        last_key = f"clickhouse_last_scale_ts_{service_id}"

        async with redis.aredis_pool() as aredis:
            last_scale_ts_str = await aredis.get(last_key)
            last_scale_ts = (
                datetime.fromisoformat(last_scale_ts_str.decode("utf-8"))
                if last_scale_ts_str
                else None
            )

        async def log_decision(
            decision: ScalingDecision,
            new_replicas: int,
            reason: str,
            failed: bool = False,
        ) -> None:
            # This log is used for monitoring.
            status = "failed" if failed else "successful"
            log_tag = f"autoscale_clickhouse_cluster_{status}_decision"
            if dry_run:
                log_tag = "[DRY-RUN] " + log_tag

            await logger.ainfo(
                log_tag,
                service_id=service_id,
                service_name=service.get("name"),
                current_replicas=current_replicas,
                max_replicas=max_replicas,
                min_replicas=min_replicas,
                cpu_up_threshold_pct=cpu_up,
                mem_up_threshold_pct=mem_up,
                cpu_down_threshold_pct=cpu_down,
                mem_down_threshold_pct=mem_down,
                memory_limit_gb=round(memory_limit, 2),
                memory_util_vs_limit_pct=round(mem_limit_pct, 2),
                decision=decision,
                prev_replicas=current_replicas,
                new_replicas=new_replicas,
                reason=reason,
                last_scale_ts=last_scale_ts.isoformat() if last_scale_ts else None,
                **clickhouse_metrics_log,
            )

        if (
            last_scale_ts
            and now < last_scale_ts + timedelta(minutes=stabilization_minutes)
            and not force
        ):
            await log_decision(
                decision="skip",
                new_replicas=current_replicas,
                reason="within stabilization window",
            )
            return
        if force:
            await logger.awarning(
                "Force flag enabled — skipping stabilization window check"
            )

        # Determine scaling action needed
        new_replicas = current_replicas
        scaling_reason = None

        # Check if scale up is needed based on resource utilization
        if cpu_util > cpu_up or mem_util > mem_up:
            new_replicas = min(current_replicas + 1, max_replicas)
            scaling_reason = (
                f"high CPU ({cpu_util:.2f}%) or memory ({mem_util:.2f}%) utilization"
            )
        # Check if scale down is needed based on resource utilization
        elif cpu_util < cpu_down and mem_util < mem_down:
            new_replicas = max(current_replicas - 1, min_replicas)
            scaling_reason = (
                f"low CPU ({cpu_util:.2f}%) and memory ({mem_util:.2f}%) utilization"
            )

        # Determine actual decision based on replica count change
        decision: ScalingDecision
        if new_replicas > current_replicas:
            decision = "scale-up"
            reason = scaling_reason or f"enforcing min replicas ({min_replicas})"
        elif new_replicas < current_replicas:
            decision = "scale-down"
            reason = scaling_reason or "reducing replicas"
        else:
            # No scaling needed
            if scaling_reason:
                # Wanted to scale but already at limit
                if cpu_util > cpu_up or mem_util > mem_up:
                    reason = f"already at max replicas ({max_replicas})"
                else:
                    reason = f"already at min replicas ({min_replicas})"
            else:
                reason = "no scaling action required"

            await log_decision(
                decision="skip",
                new_replicas=new_replicas,
                reason=reason,
            )
            return

        # Execute scaling if not dry run
        if not dry_run:
            payload = {"numReplicas": new_replicas}
            async with AsyncClient(auth=auth) as patch_client:
                patch_resp = await patch_client.patch(scaling_url, json=payload)
                if not patch_resp.is_success:
                    await log_decision(
                        decision=decision,
                        new_replicas=new_replicas,
                        reason=f"clickhouse_response_error: {patch_resp.text}",
                        failed=True,
                    )
                    patch_resp.raise_for_status()

        # Log successful scaling decision
        await log_decision(
            decision=decision,
            new_replicas=new_replicas,
            reason=reason,
        )

        # Update timestamp
        async with redis.aredis_pool() as aredis:
            await aredis.set(last_key, now.isoformat(), ex=86400)

        return

    finally:
        await lock.release()
        await logger.ainfo(
            "Released autoscaler Redis lock",
            lock_key=lock_key,
            service_id=service_id,
        )

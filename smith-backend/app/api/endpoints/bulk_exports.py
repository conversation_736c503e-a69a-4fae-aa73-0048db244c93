"""Endpoints for API Keys."""

from typing import List
from uuid import UUID

import structlog
from fastapi import Depends
from lc_database import api_router
from lc_logging.audit_logs import audit_operation_name

from app import schemas
from app.api import deps
from app.api.auth.schemas import Permissions
from app.models.bulk_exports import crud as exports_crud

router = api_router.TrailingSlashRouter()

logger = structlog.get_logger(__name__)

# TODO: Add specific permissions for bulk exports


@router.get("", response_model=List[schemas.BulkExport])
async def get_bulk_exports(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> List[schemas.BulkExport]:
    """Get the current workspace's bulk exports"""
    return await exports_crud.list_bulk_exports(auth)


@router.get("/destinations", response_model=List[schemas.BulkExportDestination])
async def get_bulk_export_destinations(
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> List[schemas.BulkExportDestination]:
    """Get the current workspace's bulk export destinations"""
    return await exports_crud.list_bulk_export_destinations(auth)


@router.get("/{bulk_export_id}", response_model=schemas.BulkExport)
async def get_bulk_export(
    bulk_export_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> schemas.BulkExport:
    """Get a single bulk export by ID"""
    return await exports_crud.get_bulk_export(auth, bulk_export_id)


@router.post("", response_model=schemas.BulkExport)
@audit_operation_name("create_bulk_export")
async def create_bulk_export(
    payload: schemas.BulkExportCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_MANAGE)),
) -> schemas.BulkExport:
    """Create a new bulk export"""
    payload_internal = schemas.BulkExportCreateInternal(
        **payload.model_dump(), source_bulk_export_id=None
    )
    return await exports_crud.create_bulk_export(auth, payload_internal)


@router.patch("/{bulk_export_id}")
@audit_operation_name("cancel_bulk_export")
async def cancel_bulk_export(
    bulk_export_id: UUID,
    payload: schemas.BulkExportUpdate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_MANAGE)),
) -> schemas.BulkExport:
    """Cancel a bulk export by ID"""
    return await exports_crud.cancel_bulk_export(auth, bulk_export_id, payload)


@router.get(
    "/destinations/{destination_id}", response_model=schemas.BulkExportDestination
)
@audit_operation_name("read_bulk_export_destination")
async def get_bulk_export_destination(
    destination_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> schemas.BulkExportDestination:
    """Get a single bulk export destination by ID"""
    return await exports_crud.get_bulk_export_destination(auth, destination_id)


@router.post("/destinations", response_model=schemas.BulkExportDestination)
@audit_operation_name("create_bulk_export_destination")
async def create_bulk_export_destination(
    payload: schemas.BulkExportDestinationCreate,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_MANAGE)),
) -> schemas.BulkExportDestination:
    """Create a new bulk export destination"""
    return await exports_crud.create_bulk_export_destination(auth, payload)


@router.get("/{bulk_export_id}/runs", response_model=List[schemas.BulkExportRun])
async def get_bulk_export_runs(
    bulk_export_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> List[schemas.BulkExportRun]:
    """Get a bulk export's runs"""
    return await exports_crud.list_bulk_export_runs(auth, bulk_export_id)


@router.get("/{bulk_export_id}/runs/{run_id}", response_model=schemas.BulkExportRun)
async def get_bulk_export_run(
    bulk_export_id: UUID,
    run_id: UUID,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.WORKSPACES_READ)),
) -> schemas.BulkExportRun:
    """Get a single bulk export's run by ID"""
    return await exports_crud.get_bulk_export_run(auth, bulk_export_id, run_id)

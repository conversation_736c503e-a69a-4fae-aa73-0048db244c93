"""Endpoints accessible via the retool admin panel."""

from datetime import datetime
from uuid import uuid4

import or<PERSON><PERSON>
import structlog
from cryptography.fernet import Fernet
from fastapi import Depends, HTTPException, Response
from lc_database import api_router

from app import models, schemas
from app.api import deps
from app.api.auth.schemas import (
    AuthInfo,
    OrgAuthInfo,
    Permissions,
    ServiceIdentity,
)
from app.models.alerts.models import AlertEntity
from app.models.alerts.visitor import AlertEntityVisitor
from app.models.query_lang.parse import parse_as_filter_directive

logger = structlog.get_logger(__name__)

SERVICE_ONLY_DEPS = Depends(
    deps.Authorize(
        permission=None,
        allowed_services=[ServiceIdentity.UNSPECIFIED],
    )
)

SERVICE_ONLY_ORG_DEPS = Depends(
    deps.OrgAuthorize(
        permission=None,
        allowed_services=[ServiceIdentity.UNSPECIFIED],
    )
)

router = api_router.TrailingSlashRouter()


### ENDPOINTS THAT WE DO NOT WANT TO EXPOSE TO THE PUBLIC. All routes here are not possible to access via the LB ###
@router.get("/tenants/auth", response_model=AuthInfo)
async def get_tenant_config(
    auth: deps.AuthInfo = SERVICE_ONLY_DEPS,
) -> AuthInfo:
    """Get the tenant config."""
    return auth


@router.get("/orgs/auth", response_model=OrgAuthInfo)
async def get_org_config(
    auth: deps.OrgAuthInfo = SERVICE_ONLY_ORG_DEPS,
) -> OrgAuthInfo:
    """Get the org config."""
    return auth


@router.post(
    "/workspaces/current/encrypted-secrets",
    response_model=schemas.InternalSecretsResponse,
)
async def list_current_workspace_encrypted_secrets(
    internal_secret_request: schemas.InternalSecretsRequest,
    auth: deps.AuthInfo = Depends(
        deps.Authorize(
            Permissions.WORKSPACES_READ,
            allow_disabled=False,
            allowed_services=[ServiceIdentity.UNSPECIFIED],
        ),
    ),
) -> schemas.InternalSecretsResponse:
    secrets = await models.tenants.secrets.list_secrets(auth)
    secret_dicts = [secret.dict() for secret in secrets]
    # 2) Use Fernet with the received key
    fernet = Fernet(internal_secret_request.secret_key)
    # Turn list into json string and encrypt
    encrypted_secrets = fernet.encrypt(orjson.dumps(secret_dicts)).decode()
    return schemas.InternalSecretsResponse(encrypted_secrets=encrypted_secrets)


@router.post("/alert-filters/validate")
async def validate_filter(
    body: schemas.FilterValidationRequest,
    auth: deps.AuthInfo = Depends(deps.Authorize(Permissions.RUNS_READ)),
) -> None:
    """Validate a query filter."""
    try:
        # parse the filter
        filter_directive = parse_as_filter_directive(body.filter)

        # create a dummy alert entity
        alert_entity = AlertEntity(
            session_id=uuid4(),
            is_root=False,
            start_time=datetime.now(),
            end_time=datetime.now(),
            latency=1.1,
            status="error",
            name="test",
            run_type="test",
            input_key={"test": "test"},
            output_key={"test": "test"},
            metadata_key={"test": "test"},
            tag=["test"],
            error="test",
            inputs="test",
            outputs="test",
            # feedback entries
            modified_at=datetime.now(),
            feedback_key="test",
            feedback_score=1.2,
        )

        # check if the alert entity matches the filter directive
        _ = filter_directive.accept(AlertEntityVisitor(alert_entity=alert_entity))

        return Response(status_code=200)
    except Exception as e:
        logger.warning("Filter validation failed", filter=body.filter, error=e)
        raise HTTPException(status_code=400, detail=str(e))

from app.models.query_lang.translate_qw import QuickwitAttributeInfo, QuickwitType


def get_quickwit_attributes() -> list[QuickwitAttributeInfo]:
    return [
        QuickwitAttributeInfo(
            name="tenant_id",
            quickwit_field="tenant_id",
            type=QuickwitType.UUID,
        ),
        QuickwitAttributeInfo(
            name="session_id",
            quickwit_field="session_id",
            type=QuickwitType.UUID,
        ),
        QuickwitAttributeInfo(
            name="inputs_text",
            quickwit_field="inputs_flat",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="outputs_text",
            quickwit_field="outputs_flat",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="error",
            quickwit_field="error",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="inputs_json",
            quickwit_field="inputs",
            type=QuickwitType.ANY,
        ),
        QuickwitAttributeInfo(
            name="outputs_json",
            quickwit_field="outputs",
            type=QuickwitType.ANY,
        ),
        QuickwitAttributeInfo(
            name="extra",
            quickwit_field="extra",
            type=QuickwitType.ANY,
        ),
        QuickwitAttributeInfo(
            name="is_root",
            quickwit_field="is_root",
            type=QuickwitType.BOOL,
        ),
        QuickwitAttributeInfo(
            name="name",
            quickwit_field="name",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="run_type",
            quickwit_field="run_type",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="parent_run_id",
            quickwit_field="parent_run_id",
            type=QuickwitType.UUID,
        ),
        QuickwitAttributeInfo(
            name="tags",
            quickwit_field="tags",
            type=QuickwitType.ARRAY_TEXT,
        ),
        QuickwitAttributeInfo(
            name="status",
            quickwit_field="status",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="trace_id",
            quickwit_field="trace_id",
            type=QuickwitType.UUID,
        ),
        QuickwitAttributeInfo(
            name="thread_id",
            quickwit_field="thread_id",
            type=QuickwitType.STRING,
        ),
        QuickwitAttributeInfo(
            name="start_time",
            quickwit_field="start_time_micros",
            type=QuickwitType.DATETIME,
        ),
        QuickwitAttributeInfo(
            name="cursor",
            quickwit_field=None,  # will be handled in the visitor
            type=QuickwitType.CURSOR,
        ),
        QuickwitAttributeInfo(
            name="input_key",
            quickwit_field=None,  # pseudo-attribute, no direct field
            type=QuickwitType.JSON_KEY,
        ),
        QuickwitAttributeInfo(
            name="input_value",
            quickwit_field=None,
            type=QuickwitType.JSON_VALUE,
        ),
        QuickwitAttributeInfo(
            name="output_key",
            quickwit_field=None,
            type=QuickwitType.JSON_KEY,
        ),
        QuickwitAttributeInfo(
            name="output_value",
            quickwit_field=None,
            type=QuickwitType.JSON_VALUE,
        ),
        QuickwitAttributeInfo(
            name="extra_key",
            quickwit_field=None,
            type=QuickwitType.JSON_KEY,
        ),
        QuickwitAttributeInfo(
            name="extra_value",
            quickwit_field=None,
            type=QuickwitType.JSON_VALUE,
        ),
    ]

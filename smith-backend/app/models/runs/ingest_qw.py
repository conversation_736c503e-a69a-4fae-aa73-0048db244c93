import asyncio
import traceback
from datetime import datetime
from typing import (
    NamedTuple,
)
from uuid import UUID

import orjson
import structlog
import xxhash
from ddtrace import tracer  # noqa: E402
from ddtrace.constants import SPAN_KIND  # noqa: E402
from ddtrace.ext import SpanKind  # noqa: E402
from lc_config.logging_settings import logging_settings
from lc_config.settings import shared_settings as env_settings
from lc_database.kafka import kafka_producer_client
from lc_database.quickwit import quickwit_client
from lc_logging.trace import TraceSpanKind, wrap
from opentelemetry import trace as otel_tracer

from app.api.auth.schemas import BaseAuthInfo
from app.config import settings
from app.models.runs.schema import RunInsert
from app.models.runs.utils import get_run_status, get_run_thread_id
from app.schemas import TraceTier

logger = structlog.stdlib.get_logger(__name__)


class QuickwitBatch(NamedTuple):
    payload: bytes
    doc_count: int


class QuickwitBatchList(NamedTuple):
    batches: list[QuickwitBatch]
    total_size: int
    over_limit: list[int]
    over_limit_sizes: list[int]


async def _serialize_quickwit_docs(
    docs: list[dict],
    max_batch_size: int,
    skip_docs_over_limit=False,
) -> QuickwitBatchList:
    """
    Offload orjson serialization to a thread to avoid blocking the event loop.
    Returns a list of byte arrays, each respecting the max_batch_size limit.
    """

    def _serialize() -> QuickwitBatchList:
        batches: list[QuickwitBatch] = []
        current_batch: list[bytes] = []
        current_size = 0
        total_size = 0
        current_doc_count = 0
        over_limit: list[int] = []
        over_limit_sizes: list[int] = []

        for i, doc in enumerate(docs):
            serialized_doc = orjson.dumps(doc)
            doc_size = len(serialized_doc)

            if skip_docs_over_limit and doc_size > max_batch_size:
                over_limit.append(i)
                over_limit_sizes.append(doc_size)
                continue

            total_size += doc_size

            # note that not -1 - accounting for incoming doc
            new_line_count = len(current_batch)

            # If adding would exceed the limit and already have documents in the current batch
            if (
                current_batch
                and current_size + new_line_count + doc_size > max_batch_size
            ):
                # Close and append the batch
                batches.append(
                    QuickwitBatch(
                        payload=b"\n".join(current_batch), doc_count=current_doc_count
                    )
                )
                # Start a new batch with this document
                current_batch = [serialized_doc]
                current_size = doc_size
                current_doc_count = 1
            else:
                # Add to the current batch
                current_batch.append(serialized_doc)
                current_size += doc_size
                current_doc_count += 1

        # Serialize last batch
        if current_batch:
            batches.append(
                QuickwitBatch(
                    payload=b"\n".join(current_batch), doc_count=current_doc_count
                )
            )

        return QuickwitBatchList(batches, total_size, over_limit, over_limit_sizes)

    return await asyncio.to_thread(_serialize)


def _calculate_id_sort(uuid_str: str) -> int:
    u = UUID(uuid_str)
    h = xxhash.xxh64()
    h.update(u.bytes)
    return h.intdigest()


@wrap(
    name="quickwit.ingest",
    service="quickwit",
    resource="quickwit_ingest_bulk:http",
)
async def add_runs_to_search_ingest_bulk(
    auth: BaseAuthInfo,
    filtered_docs: dict[str, list[dict]],
) -> None:
    if not filtered_docs:
        return

    tasks = []
    for tier, docs in filtered_docs.items():
        index_id = (
            settings.QUICKWIT_RUNS_INDEX_LONG_TTL
            if tier == TraceTier.longlived
            else settings.QUICKWIT_RUNS_INDEX
        )

        task = asyncio.create_task(
            _add_runs_to_search_ingest_tier(auth, docs, index_id=index_id)
        )
        tasks.append(task)

    await asyncio.gather(*tasks)


@wrap(
    name="quickwit.ingest",
    service="quickwit",
    resource="quickwit_ingest_tier:http",
    kind=TraceSpanKind.CLIENT,
)
async def _add_runs_to_search_ingest_tier(
    auth: BaseAuthInfo,
    filtered_docs: list[dict],
    index_id: str,
) -> None:
    serialized = await _serialize_quickwit_docs(
        docs=filtered_docs,
        max_batch_size=env_settings.QUICKWIT_INDEXING_MAX_PAYLOAD_SIZE,
        skip_docs_over_limit=True,
    )

    if len(serialized.over_limit) != 0:
        logger.info(
            "Batch ingest skipped docs over limit",
            tenant_id=str(auth.tenant_id),
            over_limit_count=len(serialized.over_limit),
            over_limit_runs=[filtered_docs[i].get("id") for i in serialized.over_limit],
            over_limit_sizes=serialized.over_limit_sizes,
        )

    if logging_settings.DATADOG_ENABLED and (span := tracer.current_span()):
        span.set_tag_str("tenant_id", str(auth.tenant_id))
        span.set_tag_str(SPAN_KIND, SpanKind.CLIENT)
        span.set_tag("index_id", index_id)
        span.set_tag("run_count", len(filtered_docs))
        span.set_tag("batch_count", len(serialized.batches))
        span.set_tag("total_size", serialized.total_size)
        span.set_tag("over_limit_count", len(serialized.over_limit))
        span.set_tag("over_limit_size", sum(serialized.over_limit_sizes))
    elif logging_settings.OTEL_TRACING_ENABLED and (
        span := otel_tracer.get_current_span()
    ):
        span.set_attribute("tenant_id", str(auth.tenant_id))
        span.set_attribute("index_id", index_id)
        span.set_attribute("run_count", len(filtered_docs))
        span.set_attribute("batch_count", len(serialized.batches))
        span.set_attribute("total_size", serialized.total_size)
        span.set_attribute("over_limit_count", len(serialized.over_limit))
        span.set_attribute("over_limit_size", sum(serialized.over_limit_sizes))

    tasks = []
    for batch in serialized.batches:
        task = asyncio.create_task(_add_to_search_ingest(auth, batch, index_id))
        tasks.append(task)

    results = await asyncio.gather(*tasks, return_exceptions=True)
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            # Note: outer span will be successfull here
            logger.error(
                "Batch ingest to Quickwit failed",
                tenant_id=str(auth.tenant_id),
                payload_size=len(serialized.batches[i].payload),
                run_count=serialized.batches[i].doc_count,
                error=str(result),
                traceback=f"{''.join(traceback.format_tb(result.__traceback__))}",
            )


@wrap(
    name="quickwit.ingest",
    service="quickwit",
    resource="quickwit_ingest:http",
)
async def _add_to_search_ingest(
    auth: BaseAuthInfo,
    batch: QuickwitBatch,
    index_id: str = settings.QUICKWIT_RUNS_INDEX,
) -> None:
    if not batch:
        return

    if logging_settings.DATADOG_ENABLED and (span := tracer.current_span()):
        span.set_tag("payload_size", len(batch.payload))
        span.set_tag("run_count", batch.doc_count)
    elif logging_settings.OTEL_TRACING_ENABLED and (
        span := otel_tracer.get_current_span()
    ):
        span.set_attribute("payload_size", len(batch.payload))
        span.set_attribute("run_count", batch.doc_count)

    async with quickwit_client() as session:
        # Create HTTP request with NDJSON format
        url = f"{settings.QUICKWIT_INDEXING_URL}/api/v1/{index_id}/ingest"
        headers = {"Content-Type": "application/x-ndjson"}
        params = {}
        if env_settings.QUICKWIT_INDEXING_FORCE_COMMIT:
            params["commit"] = "force"
        if env_settings.QUICKWIT_DETAILED_RESPONSE:
            params["detailed_response"] = "true"

        response = await session.post(
            url, headers=headers, content=batch.payload, params=params
        )

        response.raise_for_status()  # This will raise an HTTPStatusError if the status is 4xx/5xx

        response_data = response.json()
        if response_data.get("num_ingested_docs") != batch.doc_count:
            logger.error(
                "Error ingesting runs into Quickwit",
                tenant_id=str(auth.tenant_id),
                payload_size=len(batch.payload),
                run_count=batch.doc_count,
                num_ingested_docs=response_data.get("num_ingested_docs", 0),
                num_rejected_docs=response_data.get("num_rejected_docs", 0),
                response=response_data,
            )
            raise Exception(f"Quickwit ingest failed: {response_data}")


async def add_runs_to_search_via_kafka(
    auth: BaseAuthInfo,
    filtered_docs: dict[TraceTier | None, list[dict]],
) -> None:
    if not filtered_docs:
        return

    try:
        async with kafka_producer_client() as producer:
            send_tasks = []

            # TODO: Needs to route to correct Kafka source for tier
            for _, docs in filtered_docs.items():
                for doc in docs:
                    send_tasks.append(
                        producer.publish(
                            message=orjson.dumps(doc),
                            topic=settings.QUICKWIT_KAFKA_RUNS_SEARCH_TOPIC,
                            key=doc["trace_id"].encode(
                                "utf-8"
                            ),  # trace_id as the key (debatably the best option to distribute load as tenant_id will create hot spots)
                        )
                    )

        # wait for messages to be sent
        await asyncio.gather(*send_tasks)

    except Exception as e:
        logger.error("Failed to upsert runs into Quickwit", error=str(e))


def _sanitize_timestamp(timestamp: str) -> str:
    # E.g. "2023-05-05T05:13:24.571809"
    return datetime.fromisoformat(timestamp).strftime("%Y-%m-%d %H:%M:%S.%f")


def _timestamp_to_micros(timestamp: str) -> int:
    # E.g. "2023-05-05T05:13:24.571809"
    return int(datetime.fromisoformat(timestamp).timestamp() * 1_000_000)


def prepare_search_ingest_docs(
    auth: BaseAuthInfo,
    run_inserts: list[RunInsert],
    input_previews: dict[str, str],
    output_previews: dict[str, str],
    session_ids_by_trace_id: dict[str, UUID],
    trace_tier_by_session_id: dict[UUID, TraceTier | None],
) -> dict[TraceTier | None, list[dict]]:
    # Organize by tiers so we can route to the correct index
    filtered_docs: dict[TraceTier | None, list[dict]] = {}

    for run in run_inserts:
        if run.payload.get("start_time") is None:
            # Consider this document invalid
            continue

        start_time = str(run.payload.get("start_time"))

        if run.payload.get("end_time") is None:
            # Only ingest runs that have ended
            continue

        session_id = session_ids_by_trace_id.get(run.trace_id)
        if session_id is None:
            logger.error(
                "Missing session_id for run",
                tenant_id=auth.tenant_id,
                trace_id=run.trace_id,
                run_id=run.payload.get("id"),
            )
            continue

        end_time = str(run.payload.get("end_time"))

        run_id = str(run.payload.get("id"))
        doc = {
            "id": run_id,
            "id_sort": _calculate_id_sort(str(run.payload.get("id"))),
            "tenant_id": str(auth.tenant_id),
            "session_id": str(session_ids_by_trace_id[run.trace_id]),
            "is_root": run.payload.get("parent_run_id") is None,
            "name": run.payload.get("name"),
            "start_time": _sanitize_timestamp(start_time),
            "start_time_micros": _timestamp_to_micros(start_time),
            "end_time": _sanitize_timestamp(end_time),
            "extra": run.payload.get("extra"),
            "error": run.payload.get("error"),
            "run_type": run.payload.get("run_type"),
            "inputs": run.payload.get("inputs"),
            "inputs_preview": input_previews.get(run_id),
            "outputs": run.payload.get("outputs"),
            "outputs_preview": output_previews.get(run_id),
            "parent_run_id": run.payload.get("parent_run_id"),
            "tags": run.payload.get("tags") or [],
            "status": get_run_status(run.payload),
            "trace_id": run.trace_id,
            "dotted_order": run.dotted_order,
            "thread_id": get_run_thread_id(run.payload),
            "reference_example_id": run.payload.get("reference_example_id"),
        }

        if settings.FF_TRACE_TIERS_ENABLED:
            trace_tier = (
                run.payload.get("upgrade_trace_tier")
                or trace_tier_by_session_id[session_id]
            )

        filtered_docs.setdefault(trace_tier, []).append(doc)
    return filtered_docs


def is_search_ingest_enabled(auth: BaseAuthInfo) -> bool:
    return (
        str(auth.tenant_id) in settings.QUICKWIT_INDEXING_ENABLED_TENANTS
        or settings.QUICKWIT_INDEXING_ENABLED_ALL
    )


def is_search_ingest_use_kafka(auth: BaseAuthInfo) -> bool:
    return settings.QUICKWIT_INDEXING_KAFKA_ENABLED

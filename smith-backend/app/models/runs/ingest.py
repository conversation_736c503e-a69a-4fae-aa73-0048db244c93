import asyncio
from collections import defaultdict
from collections.abc import Sequence
from dataclasses import dataclass
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from functools import partial
from itertools import groupby
from random import random
from typing import (
    Any,
    Callable,
    Literal,
    NamedTuple,
    NotRequired,
    Optional,
    TypedDict,
    cast,
)
from uuid import UUID

import orjson
import regex as re
import structlog
from aiochclient.records import Record
from fastapi import HTTPException
from lc_config.settings import shared_settings as env_settings
from lc_config.tracing import trace_async_function_call
from lc_database import clickhouse, database, redis
from lc_database.clickhouse import ClickhouseClient
from lc_database.kafka import kafka_producer_client
from lc_database.s3_client import (
    copy_object,
    get_run_data_and_size,
    strip_ttl_prefix,
    upload_run_data,
)
from lc_logging.conditional_logger import ContextConditionalDropper
from lc_logging.trace import wrap
from prometheus_client import Counter

from app import schemas
from app.api.auth.schemas import AuthIn<PERSON>, BaseAuthInfo
from app.config import settings
from app.memoize import redis_cache
from app.models.alerts.match import check_entities_and_add_metrics
from app.models.alerts.models import AlertEntity
from app.models.constants import CH_INSERT_TIME
from app.models.feedback.ingest import (
    FeedbackInsert,
    is_kafka_only_ingest_skip_ch_enabled,
    is_kafka_produce_enabled,
    upsert_feedback,
)
from app.models.feedback.ingest import ch_col_names as feedback_ch_cols_list
from app.models.model_price_map.fetch import fetch_model_price_map
from app.models.query_lang.translate import convert_datetime
from app.models.runs.compression import (
    decompress_and_parse,
    decompress_and_parse_many,
    decompress_many,
)
from app.models.runs.convert_runs import get_run_tokens, process_mm_run_io
from app.models.runs.ingest_qw import (
    add_runs_to_search_ingest_bulk,
    add_runs_to_search_via_kafka,
    is_search_ingest_enabled,
    is_search_ingest_use_kafka,
    prepare_search_ingest_docs,
)
from app.models.runs.preview import preview_inputs, preview_outputs
from app.models.runs.run_kv import break_out_nested_kv_pairs
from app.models.runs.schema import RunInfo, RunIngestDict, RunInsert
from app.models.runs.tokenize import tokenize_object
from app.models.runs.tokens import calculate_token_count
from app.models.runs.utils import get_run_status, get_run_thread_id
from app.models.runs.validate import convert_timestamp_to_isostring
from app.models.tracer_sessions.start import start_or_fetch_tracer_session
from app.models.usage_limits import user_defined_limits
from app.retry import retry_asyncpg, retry_clickhouse
from app.utils import (
    arun_in_executor,
    completed_future,
    create_latency_histogram,
    gated_coro,
)

logger = structlog.stdlib.get_logger(__name__)

# This logger only logs if the workspace is configured to do so in the environment variable.
detailed_logger = structlog.wrap_logger(
    logger,
    processors=[
        ContextConditionalDropper(
            context_key="tenant_id",
            allowed_values=settings.DETAILED_LOGGING_RUN_INGEST_WORKSPACE_IDS,
        )
    ],
)

DOTTED_ORDER_TIME = "%Y%m%dT%H%M%S%f"
ROOT_S3_KEY = "ROOT"
# This stores ingestion errors on deserializing inputs/outputs
ROOT_ERROR_S3_KEY = "ROOT_ERROR"
ERROR_S3_KEY = "error"
EVENTS_S3_KEY = "events"
# This stores ingestion errors on deserializing events
EVENTS_ERROR_S3_KEY = "events_error"
EXTRA_S3_KEY = "extra"
# This stores ingestion errors on deserializing extra
EXTRA_ERROR_S3_KEY = "extra_error"
ATTACHMENT_S3_PREFIX = "attachment."
SERIALIZED_S3_KEY = "serialized"
# This stores ingestion errors on deserializing manifests
SERIALIZED_ERROR_S3_KEY = "serialized_error"
ALLOWED_OUT_OF_BAND_KEYS = {
    "inputs",
    "outputs",
    "events",
    "error",
    "extra",
    "serialized",
}

KNOWN_S3_KEYS = {
    ERROR_S3_KEY: "error",
    EVENTS_S3_KEY: "events",
    SERIALIZED_S3_KEY: "serialized",
    # We don't include extras here as it's popped on ingestion
}

UPSERTED_RUNS = Counter(
    "upserted_runs",
    "Total count of upserted runs.",
    [],
    namespace="langsmith",
)

UPSERTED_FEEDBACKS = Counter(
    "upserted_feedbacks",
    "Total count of upserted feedback.",
    [],
    namespace="langsmith",
)

KAFKA_RUNS_LATENCY = create_latency_histogram(
    "kafka_runs_producer_latency_seconds",
    "Latency of sending runs to Kafka in seconds.",
)

KAFKA_FEEDBACKS_LATENCY = create_latency_histogram(
    "kafka_feedbacks_producer_latency_seconds",
    "Latency of sending feedbacks to Kafka in seconds.",
)

UPLOADED_MANIFESTS_EXPIRATION = 60 * 60 * 24 + 300
INCLUDED_MANIFEST_RUN_TYPES = {"llm", "prompt"}

IO_TYPE_MAPPING: dict[str, str] = {
    "error": "errors",
    "input": "inputs",
    "output": "outputs",
    "event": "events",
    "attachment": "attachments",
    "extra": "extras",
}

fetch_single_run_semaphore = asyncio.Semaphore(settings.FETCH_SINGLE_RUN_SEMAPHORE)


# TypedDict as we need to mutate the values
class TokenTracker(TypedDict):
    prompt_tokens: int | None
    completion_tokens: int | None
    total_tokens: int | None
    first_token_time: Optional[str]
    prompt_token_details: NotRequired[dict[str, int] | None]
    completion_token_details: NotRequired[dict[str, int] | None]
    prompt_cost: float | None
    completion_cost: float | None
    total_cost: float | None
    prompt_cost_details: NotRequired[dict[str, float] | None]
    completion_cost_details: NotRequired[dict[str, float] | None]


async def persist_run_and_children_ch(
    auth: AuthInfo,
    run_id: str,
    semaphore: asyncio.Semaphore,
) -> None:
    """Move a pending run and its children from Redis queue to ClickHouse."""

    # create empty collections
    inserts: dict[str, RunInsert | FeedbackInsert] = {}
    tokens: defaultdict[str, TokenTracker] = defaultdict(
        lambda: TokenTracker(
            prompt_tokens=None,
            completion_tokens=None,
            total_tokens=None,
            first_token_time=None,
            prompt_token_details=None,
            completion_token_details=None,
            prompt_cost=None,
            completion_cost=None,
            total_cost=None,
            prompt_cost_details=None,
            completion_cost_details=None,
        )
    )

    # collect this run and its children
    (run_info,) = await fetch_redis_run_info(
        auth.tenant_id,
        [
            [
                run_id,
                run_id,
            ]
        ],
    )
    # this may raise an exception, which we let bubble up
    await collect_run_and_children(
        auth, run_info, inserts, tokens, "", semaphore, False
    )
    await insert_collected_runs_and_feedback(auth, tokens, semaphore, inserts)


async def persist_batched_runs_ch(
    auth: AuthInfo,
    run_ids: list[list[str]] | list[str],
    semaphore: asyncio.Semaphore,
) -> None:
    """Move a pending run and its children from Redis queue to ClickHouse."""
    # create empty collections
    inserts: dict[str, RunInsert | FeedbackInsert] = {}
    tokens: defaultdict[str, TokenTracker] = defaultdict(
        lambda: TokenTracker(
            prompt_tokens=None,
            completion_tokens=None,
            total_tokens=None,
            first_token_time=None,
            prompt_token_details=None,
            completion_token_details=None,
            prompt_cost=None,
            completion_cost=None,
            total_cost=None,
            prompt_cost_details=None,
            completion_cost_details=None,
        )
    )

    # collect this run and its children
    run_infos = await fetch_redis_run_info(auth.tenant_id, run_ids)

    # this may raise an exception, which we let bubble up
    await asyncio.gather(
        *(
            collect_run_and_children(
                auth,
                run_info,
                inserts,
                tokens,
                "",
                semaphore,
                True,
            )
            for run_info in run_infos
        )
    )
    await insert_collected_runs_and_feedback(auth, tokens, semaphore, inserts)


async def insert_collected_runs_and_feedback(
    auth: AuthInfo,
    tokens: defaultdict[str, TokenTracker],
    semaphore: asyncio.Semaphore,
    inserts: dict[str, RunInsert | FeedbackInsert],
    wait_for_async_insert: bool | None = None,
    metrics: bool = True,
):
    wait_for_async_insert = (
        random() < settings.CLICKHOUSE_ASYNC_INSERT_WAIT_PCT_FLOAT
        if wait_for_async_insert is None
        else wait_for_async_insert
    )

    alert_entities = []
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )

    if inserts:
        try:
            run_inserts_by_trace_id = defaultdict[str, list[RunInsert]](list)
            for id, insert in inserts.items():
                if isinstance(insert, RunInsert):
                    run_inserts_by_trace_id[insert.trace_id or id].append(insert)
                    if not insert.trace_id:
                        # TODO: Monitor and cleanup warn and id above if not reached
                        await logger.awarning("Trace ID missing for run", run_id=id)

            run_inserts = [
                run_insert
                for _, run_insert in inserts.items()
                if isinstance(run_insert, RunInsert) and run_insert.should_insert
            ]

            tracer_sessions = await asyncio.gather(
                *(
                    _prepare_tracer_session(
                        run_inserts,
                        auth,
                        semaphore,
                    )
                    for _, run_inserts in run_inserts_by_trace_id.items()
                ),
                return_exceptions=True,
            )
            tracer_sessions_by_id = {
                tracer_session.id: tracer_session
                for tracer_session in tracer_sessions
                if tracer_session and not isinstance(tracer_session, BaseException)
            }

            prepared: list[
                tuple[UUID | None, list[list[Any]]] | BaseException
            ] = await asyncio.gather(
                *(
                    prepare_upsert_runs(
                        run_inserts,
                        tracer_session,
                        tokens,
                        auth,
                        semaphore,
                    )
                    if not isinstance(tracer_session, BaseException)
                    else completed_future(tracer_session)
                    for (_, run_inserts), tracer_session in zip(
                        run_inserts_by_trace_id.items(), tracer_sessions
                    )
                ),
                return_exceptions=True,
            )
            prepared_runs: list[list[Any]] = []
            prepare_exceptions: list[Any] = []
            session_ids_by_trace_id = {}
            for prepared_run, tracer_session, (trace_id, _) in zip(
                prepared, tracer_sessions, run_inserts_by_trace_id.items()
            ):
                if isinstance(prepared_run, BaseException):
                    prepare_exceptions.append(prepared_run)
                else:
                    session_id, data = prepared_run
                    prepared_runs.extend(data)
                    if session_id:
                        session_ids_by_trace_id[trace_id] = session_id

            structlog.contextvars.bind_contextvars(
                session_ids=[
                    str(session_id) for session_id in tracer_sessions_by_id.keys()
                ],
            )
            if is_kafka_produce_enabled(auth):
                await _add_runs_to_kafka(auth, prepared_runs)

            if str(auth.tenant_id) in env_settings.LOGGED_ALERT_TENANTS:
                logger.info(f"Ingesting batch of runs for tenant {str(auth.tenant_id)}")

            if is_kafka_only_ingest_skip_ch_enabled(auth):
                # TODO: Remove this after rollout
                logger.info(
                    "Skipping clickhouse write for runs",
                    skipped_run_count=len(prepared_runs),
                    skipped_run_ids=[d[0] for d in prepared_runs],
                )
            else:
                await upsert_prepared_runs(
                    prepared_runs,
                    str(auth.tenant_id),
                    sync_insert=False,
                    wait_for_async_insert=wait_for_async_insert,
                )

            feedback_inserts = []
            for insert in inserts.values():
                if isinstance(insert, FeedbackInsert):
                    if insert.session_id:
                        feedback_inserts.append(insert)
                    elif str(insert.trace_id) in session_ids_by_trace_id:
                        feedback_inserts.append(
                            _add_session_id(
                                insert, session_ids_by_trace_id[str(insert.trace_id)]
                            )
                        )

            if (
                settings.LANGCHAIN_ENV in ["local_test"]
                or (
                    auth.tenant_config
                    and auth.tenant_config.organization_config.langsmith_alerts_poc_enabled
                )
                or str(auth.tenant_id) in env_settings.TENANTS_ALERTS_FEATURE_ENABLED
            ):
                for run in prepared_runs:
                    if run[ch_cols_list.index("trace_upgrade")]:
                        continue

                    # only process completed runs
                    if run[ch_cols_list.index("end_time")] is not None:
                        # process kv, extra for alert entity
                        inputs_kv = {
                            k: v for k, v in run[ch_cols_list.index("inputs_kv")]
                        }
                        outputs_kv = {
                            k: v for k, v in run[ch_cols_list.index("outputs_kv")]
                        }
                        metadata_kv = orjson.loads(
                            run[ch_cols_list.index("extra")]
                        ).get("metadata", {})

                        latency_seconds = (
                            datetime.fromisoformat(run[ch_cols_list.index("end_time")])
                            - datetime.fromisoformat(
                                run[ch_cols_list.index("start_time")]
                            )
                        ).total_seconds()

                        # add alert entity to alert_entities
                        alert_entities.append(
                            AlertEntity(
                                session_id=run[ch_cols_list.index("session_id")],
                                is_root=run[ch_cols_list.index("is_root")],
                                status=run[ch_cols_list.index("status")],
                                start_time=run[ch_cols_list.index("start_time")],
                                end_time=run[ch_cols_list.index("end_time")],
                                latency=latency_seconds,
                                name=run[ch_cols_list.index("name")],
                                run_type=run[ch_cols_list.index("run_type")],
                                input_key=inputs_kv,
                                output_key=outputs_kv,
                                metadata_key=metadata_kv,
                                tag=run[ch_cols_list.index("tags")],
                                error=run[ch_cols_list.index("error_tokens")],
                                inputs=run[ch_cols_list.index("input_tokens")],
                                outputs=run[ch_cols_list.index("output_tokens")],
                            )
                        )

            if feedback_inserts:
                job_vars = structlog.contextvars.get_contextvars()
                skip_trace_upgrades = False
                if job_vars and job_vars.get("job_func_name") == "upgrade_batched_runs":
                    logger.info("Skipping upgrades for feedbacks on upgrade path")
                    skip_trace_upgrades = True
                await upsert_feedback(
                    feedback_inserts,
                    auth,
                    throw_on_invalid=False,
                    skip_trace_upgrade=skip_trace_upgrades,
                    skip_ch_write=is_kafka_only_ingest_skip_ch_enabled(auth),
                )

                # upsert_feedbacks never returned data, can uncomment if we decide to ever use kafka for fb
                # if is_kafka_produce_enabled(auth):
                #     await _add_feedbacks_to_kafka(auth, prepared_feedbacks)

            # TODO: Handle specific error handling and retries for each error type (alert, search, feedback, runs)
            if is_search_ingest_enabled(auth):
                trace_tier_by_session_id = {
                    session_id: tracer_session.trace_tier
                    for session_id, tracer_session in tracer_sessions_by_id.items()
                }

                try:
                    (
                        input_previews,
                        output_previews,
                    ) = await _prepare_search_ingest_previews(
                        auth,
                        run_inserts,
                        prepared_runs,
                        semaphore,
                    )

                    filtered_docs = prepare_search_ingest_docs(
                        auth,
                        run_inserts,
                        input_previews,
                        output_previews,
                        session_ids_by_trace_id,
                        trace_tier_by_session_id,
                    )

                    if is_search_ingest_use_kafka(auth):
                        await add_runs_to_search_via_kafka(auth, filtered_docs)
                    else:
                        await add_runs_to_search_ingest_bulk(auth, filtered_docs)
                except Exception as e:
                    # TODO: this is caught as a backstop but update this once rolling out
                    logger.error("Failed to upsert runs into Quickwit", error=str(e))

            try:
                await trace_async_function_call(
                    name="check_entities_and_add_metrics",
                    func=check_entities_and_add_metrics,
                    args=(auth, alert_entities),
                    kwargs={},
                )
            except Exception as e:
                logger.error(
                    "Failed to check entities and add alert metrics", error=str(e)
                )

            if prepare_exceptions:
                logger.error(
                    f"Exception with prepared queries {prepare_exceptions}.",
                    inserted_run_ids=[ins[0] for ins in prepared_runs],
                )
                # reraise these so they can be retried if not expected
                if unexpected_excs := [
                    e for e in prepare_exceptions if not isinstance(e, HTTPException)
                ]:
                    if len(unexpected_excs) == 1:
                        raise unexpected_excs[0]
                    elif all(isinstance(exc, Exception) for exc in unexpected_excs):
                        raise ExceptionGroup(
                            "Exception during processing batch inserts", unexpected_excs
                        )
                    else:
                        # cannot pass BaseException to ExceptionGroup
                        raise unexpected_excs[0]

            # track metrics
            if metrics:
                UPSERTED_RUNS.inc(len(run_inserts))
                UPSERTED_FEEDBACKS.inc(len(feedback_inserts))
        except HTTPException as e:
            await logger.awarning(
                f"Rejected inserts for tenant_id {auth.tenant_id} and run_id {inserts.keys()} due to {e.detail}"
            )

        # mark as done in redis if inserted or rejected
        # if unhandled exception, this is skipped and we'll try again later
        async with (
            redis.aredis_routed_pool(
                str(auth.tenant_id), redis.RedisOperation.WRITE
            ) as aredis,
            aredis.pipeline(transaction=use_redis_transaction) as pipe,
        ):
            # dual-write: write
            for insert in inserts.values():
                if isinstance(insert, RunInsert):
                    (
                        payload,
                        trace_id,
                        dotted_order,
                        _,
                        modified_at,
                        hash_key,
                        should_insert,
                        done,
                        _,
                    ) = insert
                    # as we're payload, save to Redis.
                    pipe.hsetnx(hash_key, "trace_id", orjson.dumps(trace_id))
                    pipe.hsetnx(
                        hash_key, "start_time", orjson.dumps(payload["start_time"])
                    )
                    pipe.hsetnx(hash_key, "dotted_order", orjson.dumps(dotted_order))
                    if session_id := session_ids_by_trace_id.get(trace_id):
                        pipe.hsetnx(hash_key, "session_id", orjson.dumps(session_id))

                    flag = 2 if wait_for_async_insert else 1
                    flag_w_time = orjson.dumps([flag, modified_at])

                    tracer_session = (
                        tracer_sessions_by_id[session_id] if session_id else None
                    )
                    if settings.FF_TRACE_TIERS_ENABLED:
                        trace_tier = (
                            insert.payload.get("upgrade_trace_tier")
                            or tracer_session.trace_tier
                            if tracer_session
                            else None
                        )
                        if trace_tier:
                            pipe.hset(hash_key, "trace_tier", orjson.dumps(trace_tier))
                    else:
                        trace_tier = None

                    if should_insert:
                        pipe.hsetnx(hash_key, "post_done_ch", flag_w_time)
                        if done:
                            pipe.hsetnx(hash_key, "patch_done_ch", flag_w_time)
                            # Remove payload to offload space. Only do this for longlived, as shortlived need to handle upgrades.
                            if settings.FF_BLOB_STORAGE_ENABLED and (
                                trace_tier
                                and not trace_tier == schemas.TraceTier.shortlived
                                or not settings.FF_TRACE_TIERS_ENABLED
                            ):
                                pipe.hdel(hash_key, "post", "patch")
                elif isinstance(insert, FeedbackInsert) and insert.redis:
                    key, value = insert.redis
                    pipe.srem(key, value)

            await redis.execute_write_pipeline(
                str(auth.tenant_id), pipe, redis.RedisOperationType.INGESTION
            )


async def _prepare_search_ingest_previews(
    auth: BaseAuthInfo,
    run_inserts: list[RunInsert],
    prepared_runs: list[list[Any]],
    semaphore: asyncio.Semaphore,
) -> tuple[dict[str, str], dict[str, str]]:
    if settings.FF_CH_SEARCH_ENABLED:
        # Use prepared previews if ClickHouse search is enabled
        input_previews = {
            run[ch_cols_list.index("id")]: run[ch_cols_list.index("inputs_preview")]
            for run in prepared_runs
        }
        output_previews = {
            run[ch_cols_list.index("id")]: run[ch_cols_list.index("outputs_preview")]
            for run in prepared_runs
        }
    else:
        # Extract previews if ClickHouse search is disabled
        in_previews, out_previews = await asyncio.gather(
            _extract_run_io_preview(auth, run_inserts, "inputs", semaphore),
            _extract_run_io_preview(auth, run_inserts, "outputs", semaphore),
        )
        input_previews = {
            run.payload["id"]: in_preview
            for run, in_preview in zip(run_inserts, in_previews)
        }
        output_previews = {
            run.payload["id"]: out_preview
            for run, out_preview in zip(run_inserts, out_previews)
        }

    return input_previews, output_previews


async def _add_feedbacks_to_kafka(
    auth: BaseAuthInfo,
    prepared_feedbacks: list[list[Any]],
) -> None:
    if not prepared_feedbacks:
        return

    try:
        with KAFKA_FEEDBACKS_LATENCY.time():
            async with kafka_producer_client() as producer:
                send_tasks = []
                for feedback in prepared_feedbacks:
                    insert_row = {}
                    for col_name, insert_value in zip(feedback_ch_cols_list, feedback):
                        insert_row[col_name] = _normalize_json_value(
                            insert_value, field_name=col_name
                        )

                    send_tasks.append(
                        producer.publish(
                            message=orjson.dumps(insert_row),
                            topic=settings.KAFKA_FEEDBACKS_TOPIC,
                        )
                    )

                # wait for messages to be sent
                await asyncio.gather(*send_tasks)
    except Exception as e:
        logger.error("Failed to upsert feedbacks into Kafka", error=str(e))
        if is_kafka_only_ingest_skip_ch_enabled(auth):
            raise


async def _add_runs_to_kafka(
    auth: BaseAuthInfo,
    prepared_runs: list[list[Any]],
) -> None:
    if not prepared_runs:
        return

    try:
        with KAFKA_RUNS_LATENCY.time():
            async with kafka_producer_client() as producer:
                send_tasks = []
                for run in prepared_runs:
                    insert_row = {}
                    for col_name, insert_value in zip(ch_cols_list, run):
                        insert_row[col_name] = _normalize_json_value(insert_value)
                    insert_row["manifest_id"] = None

                    send_tasks.append(
                        producer.publish(
                            message=orjson.dumps(insert_row),
                            topic=settings.KAFKA_RUNS_TOPIC,
                        )
                    )

                # wait for messages to be sent
                await asyncio.gather(*send_tasks)
                detailed_logger.info(
                    "Upserted runs into kafka",
                    run_count=len(prepared_runs),
                    run_ids=[d[0] for d in prepared_runs],
                )
    except Exception as e:
        logger.error("Failed to upsert runs into Kafka", error=str(e))
        if is_kafka_only_ingest_skip_ch_enabled(auth):
            raise


def _normalize_json_value(value: Any, field_name: str | None = None) -> Any:
    if field_name == "score" and isinstance(value, bool):
        # Convert boolean to integer since ClickHouse's Kafka connector doesn't automatically
        # convert booleans like the Python client does
        return 1 if value else 0
    if isinstance(value, UUID):
        return str(value)
    elif isinstance(value, Decimal):
        return str(value)
    elif isinstance(value, datetime):
        return value.isoformat()
    return value


async def _prepare_tracer_session(
    inserts: list[RunInsert],
    auth: BaseAuthInfo,
    semaphore: asyncio.Semaphore,
) -> schemas.TracerSessionWithoutVirtualFields | None:
    if not inserts:
        return None
    direct_inserts = [ins for ins in inserts if ins.should_insert]
    first_insert = direct_inserts[0] if direct_inserts else inserts[0]
    async with semaphore:
        tracer_session = await start_or_fetch_tracer_session(
            auth,
            session_id=first_insert.payload["session_id"],
            session_name=first_insert.payload["session_name"],
            start_time_str=first_insert.payload["start_time"],
        )
    return tracer_session


def _strip_suffixes(key: bytes, suffixes: Sequence[bytes]) -> bytes:
    for suffix in suffixes:
        if key.endswith(suffix):
            return key[: -len(suffix)]
    return key


def _assemble_extra(
    extra: dict[bytes, bytes],
) -> dict[str, str | tuple[bytes | None, bytes | None, bytes]]:
    return {
        key.decode(): extra[key + b"_s3_url"].decode()
        if any(k.endswith(b"_s3_url") for k in group)
        else (
            extra.get(key + b"_content_type"),
            extra.get(key + b"_compression_method"),
            extra[key],
        )
        for key, group in groupby(
            sorted(extra.keys()),
            partial(
                _strip_suffixes,
                suffixes=(b"_content_type", b"_s3_url", b"_compression_method"),
            ),
        )
    }


async def fetch_redis_run_info(
    tenant_id: UUID, trace_and_run_ids: list[list[str]] | list[str]
) -> list[RunInfo]:
    def _calculate_modified_at(now_time: tuple[int, int]) -> datetime:
        return datetime.fromtimestamp(now_time[0], timezone.utc) + timedelta(
            microseconds=now_time[1]
        )

    # TOOD: Done for backwards compatiblity, cleanup instance checks and unify types after rollout
    if isinstance(trace_and_run_ids[0], str):
        run_ids = cast(list[str], trace_and_run_ids)
    else:
        run_ids = [run_id for _, run_id in cast(list[list[str]], trace_and_run_ids)]

    # TODO: Cleanup instance check after rollout. This is handling backwards compatibility of payloads
    trace_tier_enabled_for_payload = settings.FF_TRACE_TIERS_ENABLED and not isinstance(
        trace_and_run_ids[0], str
    )
    redis_cluster_ingestion_enabled = redis.is_redis_cluster_ingestion_enabled(
        str(tenant_id)
    )
    use_redis_transaction = not redis_cluster_ingestion_enabled

    # We want to use time from Redis to set as modified_at
    # This is so we have a globally consistent version to use to merge runs in ch
    # Use a single Redis instance for this so that time is globally consistent.
    # Splitting out from pipeline when redis cluster case from pipeline below since time is blocked.
    modified_at = None
    if redis_cluster_ingestion_enabled:
        async with redis.aredis_routed_pool(
            str(tenant_id), redis.RedisOperation.TIME
        ) as aredis_single:
            modified_at = _calculate_modified_at(await aredis_single.time())

    async with (
        redis.aredis_routed_pool(str(tenant_id), redis.RedisOperation.READ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        # dual-write: write - only write is "srem" which we are not dual writing
        # We want to use time from Redis to set as modified_at
        # This is so we have a globally consistent version to use to merge runs in ch
        if not redis_cluster_ingestion_enabled:
            pipe.time()

        # remove from queued runs right before getting them from Redis
        pipe.srem(queued_runs_key(str(tenant_id)), *run_ids)

        if trace_tier_enabled_for_payload:
            trace_ids = list(
                set(
                    trace_id for trace_id, _ in cast(list[list[str]], trace_and_run_ids)
                )
            )
            # get parent trace information
            for trace_id in trace_ids:
                pipe.hmget(
                    f"smith:runs:pending:{tenant_id}:{trace_id}",
                    "trace_first_received_at",
                    "upgrade_trace_tier",
                )
        else:
            trace_ids = []

        # run information
        for run_id in run_ids:
            namespace = tenant_id
            pipe.hmget(
                f"smith:runs:pending:{namespace}:{run_id}",
                "parent",
                "start_time",
                "trace_id",
                "dotted_order",
                "session_id",
                "post",
                "post_received_at",
                "post_done_ch",
                "post_compression_method",
                "post_content_type",
                "patch",
                "patch_received_at",
                "patch_done_ch",
                "patch_compression_method",
                "patch_content_type",
                "trace_tier",
            )
            pipe.smembers(f"smith:runs:children:{namespace}:{run_id}")
            pipe.smembers(f"smith:runs:feedback:{namespace}:{run_id}")
            pipe.hgetall(f"smith:runs:pending:{namespace}:{run_id}:extra")

        results = await pipe.execute()

    if not redis_cluster_ingestion_enabled:
        modified_at = _calculate_modified_at(results[0])
        results = results[2:]
    else:
        results = results[1:]

    if trace_tier_enabled_for_payload:
        run_to_trace_id = {
            run_id: trace_id
            for trace_id, run_id in cast(list[list[str]], trace_and_run_ids)
        }
        trace_info = {
            trace_id: (
                orjson.loads(trace_first_received_at)
                if trace_first_received_at
                else None,
                orjson.loads(upgrade_trace_tier) if upgrade_trace_tier else None,
            )
            for trace_id, (
                trace_first_received_at,
                upgrade_trace_tier,
            ) in zip(
                trace_ids,
                results[: len(trace_ids)],
            )
        }
        results = results[len(trace_ids) :]
    else:
        trace_info = {}

    return [
        RunInfo(
            run_id,
            parent,
            start_time,
            trace_id,
            dotted_order,
            session_id,
            patch_received_at or post_received_at,
            post,
            post_received_at,
            post_done_ch,
            post_compression_method,
            post_content_type,
            patch,
            patch_received_at,
            patch_done_ch,
            patch_compression_method,
            patch_content_type,
            trace_tier,
            children,
            feedback,
            cast(datetime, modified_at),
            trace_info[run_to_trace_id[run_id]][0]
            if trace_tier_enabled_for_payload
            else None,
            trace_info[run_to_trace_id[run_id]][1]
            if trace_tier_enabled_for_payload
            else None,
            f"smith:runs:pending:{tenant_id}:{run_id}",
            f"smith:runs:feedback:{tenant_id}:{run_id}",
            _assemble_extra(extra),
        )
        for run_id, (
            parent,
            start_time,
            trace_id,
            dotted_order,
            session_id,
            post,
            post_received_at,
            post_done_ch,
            post_compression_method,
            post_content_type,
            patch,
            patch_received_at,
            patch_done_ch,
            patch_compression_method,
            patch_content_type,
            trace_tier,
        ), children, feedback, extra in zip(
            run_ids,
            results[::4],
            results[1::4],
            results[2::4],
            results[3::4],
        )
    ]


async def _run_io_bytes(
    inserts: list[RunInsert],
    key: Literal["inputs", "outputs", "error", "events", "extra", "serialized"],
    semaphore: asyncio.Semaphore,
    filter_func: Callable[[RunInsert], bool] = lambda x: True,
) -> list[bytes | None]:
    """Return json dumps for run io bytes"""

    return await asyncio.gather(
        *(
            gated_coro(
                arun_in_executor(
                    orjson.dumps,
                    ins.payload[key],
                ),
                semaphore,
            )
            if ins.payload.get(key) and filter_func(ins)
            else completed_future(None)
            for ins in inserts
        )
    )


def _should_include_serialized(ins: RunInsert) -> bool:
    should_include = (
        ins.payload.get("serialized") is not None
        and isinstance(ins.payload["serialized"], dict)
        and ins.payload["serialized"].get("lc") is not None
        and ins.payload["serialized"]["type"] != "not_implemented"
        and ins.payload["run_type"] in INCLUDED_MANIFEST_RUN_TYPES
    )
    # remove graph
    if should_include and ins.payload["serialized"].get("graph"):
        ins.payload["serialized"].pop("graph")
    return should_include


@dataclass
class ExtraBytes:
    full_bytes: bytes
    clickhouse_bytes: bytes


def _pull_metadata_and_runtime(extra_dict: dict) -> dict:
    """
    Extract metadata and runtime from extra dict with size limitations.
    Only includes primitive values and limits string length and total keys.
    """
    metadata_only = {}
    for k in ["metadata", "runtime"]:
        if k in extra_dict and isinstance(extra_dict[k], dict):
            # Only include non-dict values or strings < MAX_EXTRA_KEY_LENGTH chars
            metadata_only[k] = {
                key: value
                for key, value in extra_dict[k].items()
                # only allow primitives
                if isinstance(value, (str, int, float, bool, type(None)))
                # and not keys longer than MAX_EXTRA_KEY_LENGTH
                and not len(key) > settings.MAX_EXTRA_KEY_LENGTH
                # and not strings longer than MAX_EXTRA_KEY_LENGTH
                and not (
                    isinstance(value, str)
                    and len(value) >= settings.MAX_EXTRA_KEY_LENGTH
                )
            }
    # Truncate to max MAX_EXTRA_KEYS keys
    for k, v in list(metadata_only.items()):
        if isinstance(v, dict) and len(v) > settings.MAX_EXTRA_KEYS:
            metadata_only[k] = dict(list(v.items())[: settings.MAX_EXTRA_KEYS])
    return metadata_only


async def _extra_or_metadata_bytes(
    extra: dict | None,
    example_metadata: dict | None,
    semaphore: asyncio.Semaphore,
    run_depth: int = 0,
    s3_urls: dict | None = None,
) -> ExtraBytes | None:
    if not extra and not example_metadata:
        return None
    extra_dict = extra or {}  # nosemgrep
    if example_metadata is not None:
        existing_metadata = extra_dict.get("metadata", {})
        for key, value in example_metadata.items():
            existing_metadata[f"ls_example_{key}"] = value
        extra_dict["metadata"] = existing_metadata

    if isinstance(extra_dict, dict):
        if "metadata" not in extra_dict or extra_dict["metadata"] is None:
            extra_dict["metadata"] = {}
        extra_dict["metadata"]["ls_run_depth"] = run_depth

    extra_payload = await gated_coro(
        arun_in_executor(
            orjson.dumps,
            extra_dict,
        ),
        semaphore,
    )

    if (
        settings.FF_BLOB_STORAGE_ENABLED
        and len(extra_payload) >= settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
    ):
        metadata_only = _pull_metadata_and_runtime(extra_dict)
        metadata_bytes = await gated_coro(
            arun_in_executor(
                orjson.dumps,
                metadata_only,
            ),
            semaphore,
        )
        return ExtraBytes(extra_payload, metadata_bytes)
    else:
        if s3_urls:
            s3_urls.pop("extra", None)
        return ExtraBytes(extra_payload, extra_payload)


async def _process_extra_or_metadata_bytes(
    inserts: list[RunInsert],
    example_info: dict[str, Any],
    semaphore: asyncio.Semaphore,
) -> list[ExtraBytes | None]:
    """
    Process bytes for s3 upload and clickhouse storage.
    Returns:
        list[ExtraBytes | None]
    Return both bytes for clickhouse and s3 storage.
    """

    key = "extra"
    return await asyncio.gather(
        *(
            _extra_or_metadata_bytes(
                ins.payload.get(key),
                example_info.get(
                    cast(str, ins.payload.get("reference_example_id")), {}
                ).get("metadata")
                if ins.payload.get("reference_example_id")
                else None,
                semaphore,
                ins.dotted_order.count("."),
                ins.payload.get("s3_urls"),
            )
            for ins in inserts
        )
    )


async def _process_run_io_upload(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    session_id: UUID,
    session_trace_tier: schemas.TraceTier | None,
    payloads: list[bytes | None],
    key: str,
    semaphore: asyncio.Semaphore,
) -> list[str | None]:
    """Process inputs/outputs for run."""
    if not settings.FF_BLOB_STORAGE_ENABLED:
        return [None] * len(inserts)

    return await asyncio.gather(
        *(
            gated_coro(
                upload_run_data(
                    payload,
                    key,
                    auth.tenant_id,
                    session_id,
                    ins.payload["id"],
                    (ins.payload.get("upgrade_trace_tier") or session_trace_tier)
                    if settings.FF_TRACE_TIERS_ENABLED
                    else None,
                    settings.S3_SINGLE_REGION_BUCKET_NAME,
                ),
                semaphore,
            )
            if payload
            and len(payload) >= settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
            and not ins.payload.get("s3_urls", {}).get(key)
            else completed_future(None)
            for ins, payload in zip(inserts, payloads)
        )
    )


async def _copy_s3_url_blob_to_extend(run: RunInsert, key: str) -> None:
    s3_url = run.payload["s3_urls"][key]
    bucket, without_ttl_path = strip_ttl_prefix(s3_url)

    bucket_prefix = bucket + "/" if bucket else ""

    updated_path = f"{bucket_prefix}{settings.S3_TRACE_TIER_PREFIX_MAP[schemas.TraceTier.longlived.value]}/{without_ttl_path}"
    logger.info(
        "Upgrading S3 TTL to longlived",
        s3_url=s3_url,
        upgraded_s3_url=updated_path,
        run_id=run.payload["id"],
    )
    await copy_object(s3_url, updated_path)
    run.payload["s3_urls"][key] = updated_path


async def _download_and_re_upload_blob(
    auth: BaseAuthInfo, run: RunInsert, key: str
) -> None:
    """Download data from S3 and re-upload to longlived path."""
    s3_url = run.payload["s3_urls"][key]
    data_and_size = await get_run_data_and_size(s3_url)
    if not data_and_size:
        logger.warning(
            "Failed to download data for re-upload",
            s3_url=s3_url,
            run_id=run.payload["id"],
        )
        return

    data, _ = data_and_size
    data_bytes = orjson.dumps(data)

    # use mapped io type
    io_type = IO_TYPE_MAPPING.get(key, key)

    updated_path = await upload_run_data(
        data_bytes,
        io_type,
        auth.tenant_id,
        run.payload["session_id"],
        run.payload["id"],
        schemas.TraceTier.longlived.value,
        settings.S3_SINGLE_REGION_BUCKET_NAME,
    )
    logger.info(
        "Upgrading S3 TTL to longlived via download/re-upload",
        s3_url=s3_url,
        upgraded_s3_url=updated_path,
        run_id=run.payload["id"],
    )

    run.payload["s3_urls"][key] = updated_path


async def _process_run_io_upgrades(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    session_trace_tier: schemas.TraceTier | None,
    semaphore: asyncio.Semaphore,
) -> None:
    """COPY s3 urls to longlived path"""
    if not settings.FF_BLOB_STORAGE_ENABLED:
        return

    tasks = []

    for run in inserts:
        target_tier = run.payload.get("upgrade_trace_tier") or session_trace_tier
        if target_tier == schemas.TraceTier.longlived.value:
            s3_urls = run.payload.get("s3_urls", {})
            for key, val in s3_urls.items():
                if (
                    settings.S3_TRACE_TIER_PREFIX_MAP[schemas.TraceTier.longlived.value]
                    not in val
                ):
                    if "#" in val:
                        tasks.append(
                            gated_coro(
                                _download_and_re_upload_blob(auth, run, key),
                                semaphore,
                            )
                        )
                    else:
                        tasks.append(
                            gated_coro(
                                _copy_s3_url_blob_to_extend(run, key),
                                semaphore,
                            )
                        )

    await asyncio.gather(*tasks)


async def _process_run_attachment_upload(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    session_id: UUID,
    session_trace_tier: schemas.TraceTier | None,
    semaphore: asyncio.Semaphore,
) -> list[dict[str, str]]:
    """Process inputs/outputs for run."""
    if not settings.FF_BLOB_STORAGE_ENABLED:
        return [{}] * len(inserts)

    attachments = [
        (ins, key, content_type, payload)
        for ins in inserts
        for key, (content_type, payload) in ins.attachments.items()
    ]

    urls = await asyncio.gather(
        *(
            gated_coro(
                upload_run_data(
                    payload if isinstance(payload, bytes) else payload.tobytes(),
                    "attachments",
                    auth.tenant_id,
                    session_id,
                    ins.payload["id"],
                    (ins.payload.get("upgrade_trace_tier") or session_trace_tier)
                    if settings.FF_TRACE_TIERS_ENABLED
                    else None,
                    settings.S3_SINGLE_REGION_BUCKET_NAME,
                    content_type=content_type,
                ),
                semaphore,
            )
            for ins, _, content_type, payload in attachments
        )
    )

    grouped = {
        run_id: {key: url for (_, key, _, _), url in group}
        for run_id, group in groupby(
            sorted(zip(attachments, urls), key=lambda x: x[0][0].payload["id"]),
            lambda x: x[0][0].payload["id"],
        )
    }

    return [grouped.get(ins.payload["id"], {}) for ins in inserts]


async def _process_run_io_tokens(
    inserts: list[RunInsert],
    key: Literal["inputs", "outputs", "error"],
    semaphore: asyncio.Semaphore,
) -> list[str]:
    """Process inputs/outputs for run."""
    if not settings.FF_BLOB_STORAGE_ENABLED or not settings.FF_CH_SEARCH_ENABLED:
        return [""] * len(inserts)

    return await asyncio.gather(
        *(
            gated_coro(
                arun_in_executor(
                    _tokenize_run_data,
                    ins.payload[key],
                ),
                semaphore,
            )
            if ins.payload.get(key)
            else completed_future("")
            for ins in inserts
        )
    )


async def _process_run_io_preview(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    key: Literal["inputs", "outputs"],
    semaphore: asyncio.Semaphore,
) -> list[str | None]:
    """Process inputs/outputs preview for run"""

    if not settings.FF_CH_SEARCH_ENABLED:
        return [None] * len(inserts)

    return await _extract_run_io_preview(
        auth,
        inserts,
        key,
        semaphore,
    )


async def _extract_run_io_preview(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    key: Literal["inputs", "outputs"],
    semaphore: asyncio.Semaphore,
) -> list[str | None]:
    """Process inputs/outputs preview for run"""

    if key == "inputs":
        return await asyncio.gather(
            *(
                gated_coro(
                    arun_in_executor(
                        preview_inputs,
                        ins.payload["inputs"],
                        settings.RUN_PREVIEW_IO_MAX_CHARS,
                    ),
                    semaphore,
                )
                if ins.payload.get("inputs")
                else completed_future(None)
                for ins in inserts
            )
        )

    elif key == "outputs":
        return await asyncio.gather(
            *(
                gated_coro(
                    arun_in_executor(
                        preview_outputs,
                        ins.payload["outputs"],
                        ins.payload.get("inputs", None),
                        settings.RUN_PREVIEW_IO_MAX_CHARS,
                    ),
                    semaphore,
                )
                if ins.payload.get("outputs")
                else completed_future(None)
                for ins in inserts
            )
        )


async def _process_run_io_kv(
    inserts: list[RunInsert],
    key: Literal["inputs", "outputs"],
    semaphore: asyncio.Semaphore,
) -> list[dict[Any, Any]]:
    """Process inputs/outputs for run and break out nested key-value pairs."""
    if not settings.FF_CH_SEARCH_ENABLED:
        return [{}] * len(inserts)

    return await asyncio.gather(
        *(
            gated_coro(
                arun_in_executor(
                    break_out_nested_kv_pairs,
                    ins.payload[key],
                ),
                semaphore,
            )
            if ins.payload.get(key)
            else completed_future("")
            for ins in inserts
        )
    )


@wrap(name="tokenize_runs", service="tokenize_object")
def _tokenize_run_data(
    run_io: dict | list | str,
) -> str:
    tokens = tokenize_object(run_io)
    return " ".join(tokens)


@redis_cache(ttl=120, cache_empty=False)
async def fetch_single_run_cached(
    auth: BaseAuthInfo,
    run_id: UUID,
    select: list[schemas.RunSelect] | None = None,
    include_feedback: bool = True,
) -> dict[str, Any] | None:
    from app.models.runs.fetch_ch import fetch_single_run

    async with fetch_single_run_semaphore:
        return await fetch_single_run(
            auth=auth,
            run_id=run_id,
            select=select,
            include_feedback=include_feedback,
            query_name_postfix="_feedback_ingest",
            clickhouse_client_type=ClickhouseClient.USER_HEAVY_WORKLOADS_SLOW,
        )


async def collect_run_and_children(
    auth: BaseAuthInfo,
    run_info: RunInfo,
    inserts: dict[str, RunInsert | FeedbackInsert],
    tokens: defaultdict[str, TokenTracker],
    dotted_order_prefix: str,
    semaphore: asyncio.Semaphore,
    process_inline: bool,
) -> None:
    if run_info.run_id in inserts:
        # we've seen this id, stop recursion down this branch
        return None

    if run_info.post_received_at is None:
        if run_info.feedback and settings.ENABLE_EVALUATE_EXPIRED_EXPERIMENTS:
            first_fb_raw = next(iter(run_info.feedback))
            first_fb = orjson.loads(first_fb_raw)
            trace_id = (
                orjson.loads(run_info.trace_id)
                if run_info.trace_id
                else first_fb.get("trace_id")
            )
            session_id = (
                orjson.loads(run_info.session_id)
                if run_info.session_id
                else first_fb.get("session_id")
            )
            if not session_id:
                run = await fetch_single_run_cached(
                    auth=auth,
                    run_id=run_info.run_id,
                    select=[schemas.RunSelect.session_id, schemas.RunSelect.start_time],
                    include_feedback=False,
                )
                if not run or not run.get("session_id"):
                    await logger.ainfo(
                        f"Skipping feedback {first_fb['id']} as session_id is not present."
                    )
                    return None

            if run_info.start_time:
                start_ts = orjson.loads(run_info.start_time)
            elif run.get("start_time"):
                start_ts = run["start_time"]
            else:
                start_ts = first_fb.get("created_at")

            if isinstance(start_ts, (int, float)):
                start_str = convert_timestamp_to_isostring(start_ts)
            else:
                start_str = str(start_ts).replace("Z", "+00:00")

            if start_str is None:
                await logger.ainfo(
                    f"Skipping feedback {first_fb['id']} as start_time is not present."
                )
                return None
            start_time_dt = datetime.fromisoformat(start_str)

            for fb_raw in run_info.feedback:
                fb = orjson.loads(fb_raw)
                inserts[fb["id"]] = FeedbackInsert(
                    payload=fb,
                    trace_id=UUID(trace_id),
                    session_id=run["session_id"],
                    start_time=start_time_dt,
                    redis=(run_info.feedback_key, fb_raw),
                )

        # we still skip the run itself
        if run_info.patch_received_at is not None:
            await detailed_logger.ainfo(
                f"Skipping run {run_info.run_id} until post payload arrives."
            )
        else:
            await logger.ainfo(f"Skipping run {run_info.run_id} as it has expired.")
        return None

    payload = None
    if run_info.post:
        # decode and combine post and patch payloads
        post = await decompress_and_parse(
            run_info.post,
            orjson.loads(run_info.post_compression_method)
            if run_info.post_compression_method
            else None,
            run_info.post_content_type.decode()
            if run_info.post_content_type is not None
            else None,
        )
        payload = cast(
            RunIngestDict,
            {**post, "id": run_info.run_id},
        )
        start_time = convert_timestamp_to_isostring(
            payload.get("start_time")
        ) or orjson.loads(run_info.post_received_at)
        trace_id = cast(str | None, payload.get("trace_id"))
        session_id = cast(str | None, payload.get("session_id"))
        dotted_order = cast(str | None, payload.get("dotted_order"))
    else:
        trace_id = orjson.loads(run_info.trace_id) if run_info.trace_id else None
        session_id = orjson.loads(run_info.session_id) if run_info.session_id else None
        dotted_order = (
            orjson.loads(run_info.dotted_order) if run_info.dotted_order else None
        )
        start_time = orjson.loads(run_info.start_time) if run_info.start_time else None

    # update trace_id and dotted_order
    if not run_info.parent:
        start_time_dt = datetime.fromisoformat(start_time)
        trace_id = run_info.run_id
        dotted_order_prefix = cast(
            str,
            dotted_order
            or f"{start_time_dt.strftime(DOTTED_ORDER_TIME)}Z{run_info.run_id}",
        )
    elif process_inline:
        # handle if was originally nonbatch and data is stored on run_info
        if trace_id is None and run_info.trace_id:
            trace_id = orjson.loads(run_info.trace_id)
        if dotted_order is None and run_info.dotted_order:
            dotted_order = orjson.loads(run_info.dotted_order)
        # for batch_processing, we should have dotted_order and don't calculate recursively,
        # so if we don't have dotted_order, we skip. it may come in non-batched separately
        trace_id = cast(str, trace_id)
        dotted_order_prefix = cast(str, dotted_order)
        if not dotted_order_prefix:
            await logger.ainfo(
                f"Skipping run {run_info.run_id} as dotted order is not present."
            )
            return None
    else:
        if not dotted_order_prefix:
            # we got here before root run is available, we'll try again later
            await logger.ainfo(
                f"Skipping run {run_info.run_id} as root run is not available yet."
            )
            return None

        start_time_dt = datetime.fromisoformat(start_time)
        trace_id = dotted_order_prefix.split(".")[0].split("Z")[1]
        dotted_order_prefix = f"{dotted_order_prefix}.{start_time_dt.strftime(DOTTED_ORDER_TIME)}Z{run_info.run_id}"

    # add to inserts after converting run inputs/outputs
    should_insert = (
        not run_info.post_done_ch
        or (run_info.patch is not None and not run_info.patch_done_ch)
        or (
            settings.FF_TRACE_TIERS_ENABLED
            and run_info.upgrade_trace_tier is not None
            and (
                not run_info.trace_tier
                or run_info.upgrade_trace_tier != orjson.loads(run_info.trace_tier)
            )
        )
    )

    if should_insert or (run_info.feedback and run_info.post):
        if payload is None:
            # this may happen on batch upgrades if they are called concurrently and payload is removed
            await logger.awarn(
                f"Skipping run {run_info.run_id} as payload has been removed."
            )
            return None
        payload = cast(RunIngestDict, payload)
        if payload.get("end_time"):
            end_time = convert_timestamp_to_isostring(
                payload["end_time"]
            ) or orjson.loads(run_info.post_received_at)
        else:
            end_time = None

        # merge post and patch info
        if run_info.patch is not None:
            patch = cast(
                RunIngestDict,
                await decompress_and_parse(
                    run_info.patch,
                    orjson.loads(run_info.patch_compression_method)
                    if run_info.patch_compression_method
                    else None,
                    run_info.patch_content_type.decode()
                    if run_info.patch_content_type is not None
                    else None,
                ),
            )
            for k, v in patch.items():
                if k == "end_time" and v is not None:
                    end_time = convert_timestamp_to_isostring(cast(str | int, v))
                elif v is not None:
                    payload[k] = v  # type: ignore[literal-required]
            if end_time is None and run_info.patch_received_at is not None:
                end_time = orjson.loads(run_info.patch_received_at)
        if run_info.extra:
            if oob_keys := {
                k: v
                for k, v in run_info.extra.items()
                if k in ALLOWED_OUT_OF_BAND_KEYS and isinstance(v, tuple)
            }:
                parsed = await decompress_and_parse_many(oob_keys)
                for k in ALLOWED_OUT_OF_BAND_KEYS:
                    if k not in parsed:
                        continue
                    v = parsed[k]
                    if isinstance(v, orjson.JSONDecodeError):
                        await logger.awarning(
                            f"Failed to decode json for run {run_info.run_id} key {k}, ingesting without."
                        )
                    elif isinstance(v, Exception):
                        raise v
                    else:
                        payload[k] = v  # type: ignore[literal-required]
                if payload.get("inputs") and not isinstance(payload["inputs"], dict):
                    await logger.awarning(
                        f"Received non-dict inputs for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("inputs")  # type: ignore
                if payload.get("outputs") and not isinstance(payload["outputs"], dict):
                    await logger.awarning(
                        f"Received non-dict outputs for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("outputs")  # type: ignore
                if payload.get("events") and not isinstance(payload["events"], list):
                    await logger.awarning(
                        f"Received non-list events for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("events")  # type: ignore
                if payload.get("extra") and not isinstance(payload["extra"], dict):
                    await logger.awarning(
                        f"Received non-dict extra for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("extra")  # type: ignore
                if payload.get("error") and not isinstance(payload["error"], str):
                    await logger.awarning(
                        f"Received non-str error for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("error")  # type: ignore
                if payload.get("serialized") and not isinstance(
                    payload["serialized"], dict
                ):
                    await logger.awarning(
                        f"Received non-str serialized for run {run_info.run_id}, ingesting without."
                    )
                    payload.pop("serialized")  # type: ignore
            if attachments_raw := {
                k: v
                for k, v in run_info.extra.items()
                if k.startswith(ATTACHMENT_S3_PREFIX) and isinstance(v, tuple)
            }:
                attachments = await decompress_many(attachments_raw)
            else:
                attachments = {}
            if s3_urls := {
                k: v for k, v in run_info.extra.items() if isinstance(v, str)
            }:
                # download the ones we need to process locally
                tasks: dict[asyncio.Task, str] = {}

                for key in ALLOWED_OUT_OF_BAND_KEYS:
                    if key not in s3_urls:
                        continue
                    tasks[asyncio.create_task(get_run_data_and_size(s3_urls[key]))] = (
                        key
                    )
                # assign the results to the payload
                if tasks:
                    await asyncio.wait(tasks)
                    for task, key in tasks.items():
                        try:
                            if res := task.result():
                                data, size = res
                                await _validate_and_assign_payload_data(
                                    payload,
                                    key,
                                    data,
                                    int(size),
                                    run_info.run_id,
                                    logger,
                                )
                        except orjson.JSONDecodeError:
                            await logger.awarning(
                                f"Failed to decode JSON for run {run_info.run_id} key {key} url {s3_urls[key]}, ingesting without."
                            )

        else:
            attachments = {}
            s3_urls = {}

        received_at = (
            orjson.loads(run_info.received_at) if run_info.received_at else None
        )
        run_tokens = get_run_tokens(
            payload.get("outputs"), (payload.get("extra") or {}).get("metadata") or {}
        )
        # we collect data on the entire run tree, but only insert if needed
        # add insert record
        insert_payload = {
            "prompt_tokens": run_tokens.prompt_tokens if run_tokens else None,
            "completion_tokens": run_tokens.completion_tokens if run_tokens else None,
            "total_tokens": run_tokens.total_tokens if run_tokens else None,
            "prompt_token_details": run_tokens.prompt_token_details
            if run_tokens
            else None,
            "completion_token_details": run_tokens.completion_token_details
            if run_tokens
            else None,
            "prompt_cost": run_tokens.prompt_cost if run_tokens else None,
            "completion_cost": run_tokens.completion_cost if run_tokens else None,
            "total_cost": run_tokens.total_cost if run_tokens else None,
            "prompt_cost_details": run_tokens.prompt_cost_details
            if run_tokens
            else None,
            "completion_cost_details": run_tokens.completion_cost_details
            if run_tokens
            else None,
            "s3_urls": s3_urls,
            "start_time": start_time,
            "end_time": end_time,
            "id": payload["id"],
            "inputs": payload.get("inputs"),
            "input_size": payload.get("inputs_size"),
            "outputs": payload.get("outputs"),
            "output_size": payload.get("outputs_size"),
            "extra": payload.get("extra") or {},
            "name": payload["name"][:128] if payload.get("name") else None,
            "run_type": payload.get("run_type"),
            "error": payload.get("error"),
            "execution_order": payload.get("execution_order"),
            "serialized": payload.get("serialized"),
            "parent_run_id": payload.get("parent_run_id"),
            "manifest_id": payload.get("manifest_id"),
            "manifest_s3_id": payload.get("manifest_s3_id"),
            "events": payload.get("events"),
            "tags": payload.get("tags"),
            "session_id": payload.get("session_id"),
            "session_name": payload.get("session_name"),
            "reference_example_id": payload.get("reference_example_id"),
            "input_attachments": payload.get("input_attachments"),
            "output_attachments": payload.get("output_attachments"),
            "trace_first_received_at": run_info.trace_first_received_at,
            "upgrade_trace_tier": run_info.upgrade_trace_tier,
            "ttl_seconds": settings.TRACE_TIER_TTL_DURATION_SEC_MAP[
                run_info.upgrade_trace_tier
            ]
            if run_info.upgrade_trace_tier
            else None,
            # trace_upgrade only counted if not doing initial post or patch
            "trace_upgrade": bool(
                should_insert
                and run_info.post_done_ch is not None
                and (run_info.patch is None or run_info.patch_done_ch is not None)
            ),
        }
        inserts[payload["id"]] = RunInsert(
            payload=insert_payload,
            trace_id=trace_id,
            dotted_order=dotted_order_prefix.replace(run_info.run_id, payload["id"]),
            received_at=received_at,
            modified_at=run_info.modified_at.isoformat(),
            hash_key=run_info.hash_key,
            should_insert=should_insert,
            done=end_time is not None,
            attachments=attachments,
        )
        # update token counts
        payload_size = max(
            len(run_info.post) if run_info.post else 0,
            len(run_info.patch) if run_info.patch else 0,
        )
        if payload["run_type"] == "llm":
            await update_token_counts(
                auth.tenant_id,
                tokens,
                insert_payload,
                payload_size,
            )

    if run_info.feedback:
        start_time = datetime.fromisoformat(start_time)
        for feedback in run_info.feedback:
            feedback_ = orjson.loads(feedback)
            inserts[feedback_["id"]] = FeedbackInsert(
                payload=feedback_,
                trace_id=UUID(trace_id),
                session_id=UUID(session_id) if session_id else None,
                start_time=start_time,
                redis=(run_info.feedback_key, feedback),
            )

    if process_inline or not run_info.children:
        # non recursive, or no children, so we're done
        return None

    async with semaphore:
        children_run_infos = await fetch_redis_run_info(
            auth.tenant_id,
            [
                [
                    trace_id,
                    orjson.loads(r),
                ]
                for r in run_info.children
            ],
        )

    # recurse to children
    await asyncio.gather(
        *(
            collect_run_and_children(
                auth,
                child_run_info,
                inserts,
                tokens,
                dotted_order_prefix,
                semaphore,
                process_inline,
            )
            for child_run_info in children_run_infos
        )
    )


async def update_token_counts(
    tenant_id: UUID,
    tokens: defaultdict[str, TokenTracker],
    runio: dict,
    payload_size: int,
) -> None:
    # Older versions of LangchainJS might send zero token counts
    runio_id = runio["id"]
    runio_prompt_tokens = runio["prompt_tokens"]
    runio_completion_tokens = runio["completion_tokens"]
    runio_total_tokens = runio["total_tokens"]
    runio_prompt_cost = runio["prompt_cost"]
    runio_completion_cost = runio["completion_cost"]
    runio_total_cost = runio["total_cost"]

    if (
        runio_prompt_cost is not None
        or runio_completion_cost is not None
        or runio_total_cost is not None
    ):
        # we have cost already, so use them
        tokens[runio_id]["prompt_cost"] = runio_prompt_cost
        tokens[runio_id]["completion_cost"] = runio_completion_cost
        tokens[runio_id]["total_cost"] = runio_total_cost
    if (
        runio_prompt_tokens is not None
        or runio_completion_tokens is not None
        or runio_total_tokens is not None
    ):
        # we have token counts already, so use them
        tokens[runio_id]["prompt_tokens"] = runio_prompt_tokens
        tokens[runio_id]["completion_tokens"] = runio_completion_tokens
        tokens[runio_id]["total_tokens"] = (
            runio_total_tokens
            if runio_total_tokens is not None
            else (runio_prompt_tokens or 0) + (runio_completion_tokens or 0)
        )
    # Don't calculate token counts if there's been an error.
    elif runio["error"]:
        pass
    elif payload_size <= settings.MAX_TOKEN_CALCULATION_KB_SIZE * 1024:
        # we don't have token counts, so calculate them
        try:
            extra = runio["extra"] or {}
            metadata = extra.get("metadata", {})
            invocation_params = extra.get("invocation_params", {})

            model_name = (
                metadata.get("ls_model_name")
                or invocation_params.get("model")
                or invocation_params.get("model_name")
            )

            calc = await asyncio.wait_for(
                arun_in_executor(
                    calculate_token_count,
                    tenant_id,
                    runio["inputs"],
                    runio["outputs"],
                    model_name,
                    extra.get("invocation_params"),
                ),
                settings.TOKEN_CALCULATION_TIMEOUT_SEC,
            )
            tokens[runio_id]["prompt_tokens"] = calc.prompt_tokens
            tokens[runio_id]["completion_tokens"] = calc.completion_tokens
            tokens[runio_id]["total_tokens"] = (
                calc.prompt_tokens + calc.completion_tokens
            )

        except asyncio.TimeoutError:
            await logger.awarning(
                f"Failed to calculate token due to time out {runio_id}, setting to 0.",
                exc_info=True,
            )
            return
        except Exception:
            await logger.aexception(
                f"Failed to calculate token counts for run {runio_id}, setting to 0.",
            )
            return
    else:
        logger.info(
            "Skipping token count calculation for large payload",
            run_id=runio["id"],
            payload_size=payload_size,
        )

    # update other fields

    tokens[runio_id]["prompt_token_details"] = runio.get("prompt_token_details")
    tokens[runio_id]["completion_token_details"] = runio.get("completion_token_details")
    tokens[runio_id]["prompt_cost_details"] = runio.get("prompt_cost_details")
    tokens[runio_id]["completion_cost_details"] = runio.get("completion_cost_details")
    if isinstance(runio["events"], list):
        tokens[runio_id]["first_token_time"] = convert_timestamp_to_isostring(
            next(
                (
                    e.get("time")
                    for e in runio["events"]
                    if e.get("name") == "new_token"
                ),
                None,
            )
        )


ch_cols_list = [
    "id",
    "tenant_id",
    "name",
    "start_time",
    "end_time",
    "extra",
    "error",
    "is_root",
    "run_type",
    "inputs",
    "inputs_s3_urls",
    "outputs",
    "outputs_s3_urls",
    "session_id",
    "parent_run_id",
    "reference_example_id",
    "reference_dataset_id",
    "events",
    "tags",
    "manifest_id" if not settings.FF_BLOB_STORAGE_ENABLED else "manifest_s3_id",
    "manifest",
    "status",
    "trace_id",
    "dotted_order",
    "prompt_tokens",
    "completion_tokens",
    "total_tokens",
    "prompt_token_details",
    "completion_token_details",
    "first_token_time",
    "received_at",
    "modified_at",
    "is_deleted",
    "prompt_cost",
    "completion_cost",
    "total_cost",
    "price_model_id",
    "prompt_cost_details",
    "completion_cost_details",
    "input_tokens",
    "output_tokens",
    "error_tokens",
    "input_size",
    "output_size",
    "trace_first_received_at",
    "trace_tier",
    "ttl_seconds",
    "trace_upgrade",
    "s3_urls",
    "inputs_preview",
    "outputs_preview",
    "inputs_kv",
    "outputs_kv",
    "thread_id",
]

ch_cols = ", ".join(ch_cols_list)


@retry_asyncpg
async def prepare_upsert_runs(
    inserts: list[RunInsert],
    tracer_session: schemas.TracerSessionWithoutVirtualFields | None,
    tokens: defaultdict[str, TokenTracker],
    auth: BaseAuthInfo,
    semaphore: asyncio.Semaphore,
) -> tuple[UUID | None, list[list[Any]]]:
    # we collect data on the entire run tree, but only insert if needed
    # (so that token counts can be propagated up the tree)
    if not inserts:
        return None, []

    tracer_session = cast(schemas.TracerSessionWithoutVirtualFields, tracer_session)

    # get example info including dataset ids and example metadata
    example_info = await _dataset_ids(auth, inserts)

    (
        input_bytes_list,
        output_bytes_list,
        error_bytes_list,
        event_bytes_list,
        serialized_bytes_list,
        extra_and_metadata_bytes_list,
    ) = await asyncio.gather(
        _run_io_bytes(inserts, "inputs", semaphore),
        _run_io_bytes(inserts, "outputs", semaphore),
        _run_io_bytes(inserts, "error", semaphore),
        _run_io_bytes(inserts, "events", semaphore),
        _run_io_bytes(
            inserts, "serialized", semaphore, filter_func=_should_include_serialized
        ),
        _process_extra_or_metadata_bytes(inserts, example_info, semaphore),
    )

    (
        image_s3_urls,
        attachment_urls,
        input_urls,
        output_urls,
        error_urls,
        event_urls,
        extra_urls,
        serialized_urls,
        _,
        input_tokens,
        output_tokens,
        error_tokens,
        inputs_kv,
        outputs_kv,
        inputs_preview,
        outputs_preview,
        model_price_map,
    ) = await asyncio.gather(
        _process_image_uploads(auth, inserts, tracer_session.id, semaphore),
        _process_run_attachment_upload(
            auth, inserts, tracer_session.id, tracer_session.trace_tier, semaphore
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            input_bytes_list,
            "inputs",
            semaphore,
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            output_bytes_list,
            "outputs",
            semaphore,
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            error_bytes_list,
            "errors",
            semaphore,
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            event_bytes_list,
            "events",
            semaphore,
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            [
                bytes_tuple.full_bytes if bytes_tuple else None
                for bytes_tuple in extra_and_metadata_bytes_list
            ],
            "extras",
            semaphore,
        ),
        _process_run_io_upload(
            auth,
            inserts,
            tracer_session.id,
            tracer_session.trace_tier,
            serialized_bytes_list,
            "serialized",
            semaphore,
        ),
        _process_run_io_upgrades(
            auth,
            inserts,
            tracer_session.trace_tier,
            semaphore,
        ),
        _process_run_io_tokens(inserts, "inputs", semaphore),
        _process_run_io_tokens(inserts, "outputs", semaphore),
        _process_run_io_tokens(inserts, "error", semaphore),
        _process_run_io_kv(inserts, "inputs", semaphore),
        _process_run_io_kv(inserts, "outputs", semaphore),
        _process_run_io_preview(auth, inserts, "inputs", semaphore),
        _process_run_io_preview(auth, inserts, "outputs", semaphore),
        gated_coro(_model_price_map(auth), semaphore),
    )

    # assemble data for clickhouse
    data = await arun_in_executor(
        _prepare_upsert_payload,
        auth,
        tracer_session,
        inserts,
        tokens,
        image_s3_urls,
        example_info,
        model_price_map,
        input_urls,
        output_urls,
        error_urls,
        event_urls,
        extra_urls,
        serialized_urls,
        input_tokens,
        output_tokens,
        error_tokens,
        inputs_kv,
        outputs_kv,
        inputs_preview,
        outputs_preview,
        input_bytes_list,
        output_bytes_list,
        error_bytes_list,
        event_bytes_list,
        [
            bytes_tuple.clickhouse_bytes if bytes_tuple else None
            for bytes_tuple in extra_and_metadata_bytes_list
        ],
        serialized_bytes_list,
        attachment_urls,
    )

    return UUID(tracer_session.id.hex), data


async def upsert_prepared_runs(
    data: list[list[Any]],
    auth_id: str,
    sync_insert: bool = False,
    wait_for_async_insert: bool = False,
) -> None:
    # TODO: consider moving usage limiting to ingest if we refactor trace tier to be
    #       determined on ingest. This would allow us to avoid queue delays affecting
    #       our usage limiting.

    if not data:
        # early exit if empty runs (if failures, runs already processed, etc)
        logger.info(
            "Skipping upserted runs into clickhouse due to 0 length",
            run_count=0,
            run_ids=[],
        )
        return

    structlog.contextvars.bind_contextvars(audit_operation_name="create_runs")
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(str(auth_id))

    longlived_traces_for_tenant: set[UUID] = set()
    for run in data:
        tier = cast(str | None, run[ch_cols_list.index("trace_tier")])
        # runs always are upserted in groups in the same tenant
        trace_id = cast(UUID, run[ch_cols_list.index("trace_id")])

        if tier == schemas.TraceTier.longlived.value:
            longlived_traces_for_tenant.add(trace_id)

    if len(longlived_traces_for_tenant) > 0:
        async with (
            redis.aredis_routed_pool(
                str(auth_id), redis.RedisOperation.WRITE
            ) as aredis,
            aredis.pipeline(transaction=use_redis_transaction) as pipe,
        ):
            # dual-write: write
            for (
                limit
            ) in user_defined_limits.UsageLimitUnit.LONGLIVED_TRACES.get_all_limits():
                limit.mark_seen_events_in_pipe(
                    longlived_traces_for_tenant, pipe, UUID(auth_id)
                )
            await redis.execute_write_pipeline(
                auth_id, pipe, redis.RedisOperationType.USAGE_LIMITS
            )

    run_ids = [str(d[0]) for d in data]
    structlog.contextvars.bind_contextvars(resource_ids=run_ids)

    # insert into clickhouse
    if sync_insert:
        request = clickhouse.ExecuteRequest(
            "upsert_runs_sync",
            f"""INSERT INTO runs ({ch_cols}) SETTINGS async_insert=0 VALUES""",
            data,
        )
    elif wait_for_async_insert:
        # https://clickhouse.com/docs/en/interfaces/http#http_response_codes_caveats
        request = clickhouse.ExecuteRequest(
            "upsert_runs_async_wait",
            f"""INSERT INTO runs ({ch_cols}) SETTINGS async_insert=1, wait_for_async_insert=1, http_wait_end_of_query=1, async_insert_use_adaptive_busy_timeout=0 VALUES""",
            data,
        )
    else:
        request = clickhouse.ExecuteRequest(
            "upsert_runs_async",
            f"""INSERT INTO runs ({ch_cols}) SETTINGS async_insert=1, async_insert_use_adaptive_busy_timeout=0 VALUES""",
            data,
        )

    await clickhouse.multi_execute_single(request, use_slow_client=True)
    detailed_logger.info(
        "Upserted runs into clickhouse",
        run_count=len(data),
        run_ids=run_ids,
    )


@retry_clickhouse
async def upsert_runs(
    inserts: list[RunInsert],
    tokens: defaultdict[str, TokenTracker],
    auth: BaseAuthInfo,
    semaphore: asyncio.Semaphore,
    wait_for_async_insert: bool = False,
) -> UUID | None:
    """
    Use above for upserts, this one to be used by scripts or tests.
    """
    tracer_session = await _prepare_tracer_session(
        inserts,
        auth,
        semaphore,
    )

    session_id, data = await prepare_upsert_runs(
        inserts, tracer_session, tokens, auth, semaphore
    )

    if not data:
        return session_id

    structlog.contextvars.bind_contextvars(
        session_id=str(session_id),
    )
    await upsert_prepared_runs(
        data,
        str(auth.tenant_id),
        sync_insert=inserts[0].hash_key == "",  # if this is a sample run
        wait_for_async_insert=wait_for_async_insert,
    )

    return session_id


"""
Below are private functions that compute or fetch specific attributes of a run.
"""


def _prepare_upsert_payload(
    auth: BaseAuthInfo,
    tracer_session: schemas.TracerSessionWithoutVirtualFields,
    inserts: list[RunInsert],
    tokens: defaultdict[str, TokenTracker],
    image_s3_urls_list: list[dict[str, dict]],
    example_info: dict[str, dict],
    model_price_map: list[schemas.ModelPriceMapSchema],
    input_urls: list[str],
    output_urls: list[str],
    error_urls: list[str],
    event_urls: list[str],
    extra_urls: list[str],
    serialized_urls: list[str],
    input_token_list: list[str | None],
    output_token_list: list[str | None],
    error_token_list: list[str | None],
    inputs_kv_list: dict[Any, Any],
    outputs_kv_list: dict[Any, Any],
    inputs_preview_list: list[str | None],
    outputs_preview_list: list[str | None],
    input_bytes_list: list[bytes | None],
    output_bytes_list: list[bytes | None],
    error_bytes_list: list[bytes | None],
    event_bytes_list: list[bytes | None],
    extra_bytes_list: list[bytes | None],
    serialized_bytes_list: list[bytes | None],
    attachment_urls: list[dict[str, str]],
) -> list[list[Any]]:
    return [
        [
            # id
            run["id"],
            # tenant_id
            UUID(auth.tenant_id.hex),
            # name
            run["name"],
            # start_time
            datetime.fromisoformat(run["start_time"]).strftime(CH_INSERT_TIME),
            # end_time
            datetime.fromisoformat(run["end_time"]).strftime(CH_INSERT_TIME)
            if run["end_time"]
            else None,
            # extra
            extra_bytes.decode("utf-8") if extra_bytes is not None else "{}",
            # error
            run["error"]
            if run["error"]
            and error_bytes is not None
            and (
                not settings.FF_BLOB_STORAGE_ENABLED
                or len(error_bytes) < settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
            )
            else None,
            # is_root
            run["parent_run_id"] is None,
            # run_type
            run["run_type"],
            # inputs
            input_bytes.decode("utf-8")
            if run["inputs"]
            and input_bytes is not None
            and (
                not settings.FF_BLOB_STORAGE_ENABLED
                or len(input_bytes) < settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
            )
            else "{}",
            # inputs_s3_urls
            _merged_s3_attachments(
                image_s3_urls,
                "input_attachments",
                input_url or run.get("s3_urls", {}).pop("inputs", None),
                not run.get("inputs"),
            ),
            # outputs
            output_bytes.decode("utf-8")
            if run["outputs"]
            and output_bytes is not None
            and (
                not settings.FF_BLOB_STORAGE_ENABLED
                or len(output_bytes) < settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
            )
            else "{}",
            # outputs_s3_urls
            _merged_s3_attachments(
                image_s3_urls,
                "output_attachments",
                output_url or run.get("s3_urls", {}).pop("outputs", None),
                not run.get("outputs"),
            ),
            # session_id
            UUID(tracer_session.id.hex),
            # parent_run_id
            run["parent_run_id"],
            # reference_example_id
            run["reference_example_id"],
            # reference_dataset_id
            example_info.get(run["reference_example_id"], {}).get("dataset_id")
            if run["reference_example_id"]
            else None,
            # events
            event_bytes.decode("utf-8")
            if run["events"]
            and event_bytes is not None
            and (
                not settings.FF_BLOB_STORAGE_ENABLED
                or len(event_bytes) < settings.MIN_BLOB_STORAGE_SIZE_KB * 1024
            )
            else "[]",
            # tags
            run["tags"] or [],
            # manifest_id or manifest_s3_id
            None,
            # manifest
            "{}" if not serialized_bytes else serialized_bytes.decode("utf-8"),
            # status
            get_run_status(run),
            # trace_id
            trace_id,
            # dotted_order
            dotted_order,
            # prompt_tokens
            tokens[run["id"]]["prompt_tokens"],
            # completion_tokens
            tokens[run["id"]]["completion_tokens"],
            # total_tokens
            tokens[run["id"]]["total_tokens"],
            # prompt_token_details
            orjson.dumps(tokens[run["id"]].get("prompt_token_details")).decode("utf-8"),
            # completion_token_details
            orjson.dumps(tokens[run["id"]].get("completion_token_details")).decode(
                "utf-8"
            ),
            # first_token_time
            datetime.fromisoformat(
                tokens[run["id"]]["first_token_time"]  # type: ignore[arg-type]
            ).strftime(CH_INSERT_TIME)
            if tokens[run["id"]]["first_token_time"] is not None
            else None,
            # received_at
            datetime.fromisoformat(received_at).strftime(CH_INSERT_TIME)
            if received_at
            else None,
            # modified_at
            datetime.fromisoformat(modified_at).strftime(CH_INSERT_TIME),
            # is_deleted
            0,
            # prompt_cost, completion_cost, total_cost, price_model_id, prompt_cost_details, completion_cost_details
            *_get_cost_for_tokens(
                tokens=tokens[run["id"]],
                run=run,
                model_price_map=model_price_map,
            ),
            # input_tokens
            input_tokens,
            # output_tokens
            output_tokens,
            # error_tokens
            error_tokens,
            # input_size
            len(input_bytes) if input_bytes else run.get("input_size", 0),
            # output_size
            len(output_bytes) if output_bytes else run.get("output_size", 0),
            # trace_first_received_at
            datetime.fromisoformat(run["trace_first_received_at"]).strftime(
                CH_INSERT_TIME
            )
            if run.get("trace_first_received_at")
            else datetime.fromisoformat(
                received_at or modified_at or datetime.now(timezone.utc).isoformat()
            ).strftime(CH_INSERT_TIME),
            # trace_tier
            run.get("upgrade_trace_tier")
            or cast(schemas.TraceTier, tracer_session.trace_tier).value
            if settings.FF_TRACE_TIERS_ENABLED
            else None,
            # ttl_seconds
            run.get("ttl_seconds")
            or settings.TRACE_TIER_TTL_DURATION_SEC_MAP[tracer_session.trace_tier]
            if settings.FF_TRACE_TIERS_ENABLED
            else None,
            # trace_upgrade
            run.get("upgrade_trace_tier") is not None and run.get("trace_upgrade")
            if settings.FF_TRACE_TIERS_ENABLED
            else False,
            # s3_urls
            orjson.dumps(
                {
                    **(attachments or {}),
                    **(
                        _s3_url_or_error(
                            run.get("s3_urls", {}).get(EXTRA_S3_KEY, None),
                            EXTRA_S3_KEY,
                            EXTRA_ERROR_S3_KEY,
                            not run.get("extra"),
                        )
                    ),
                    **(
                        _s3_url_or_error(
                            run.get("s3_urls", {}).get(EVENTS_S3_KEY, None),
                            EVENTS_S3_KEY,
                            EVENTS_ERROR_S3_KEY,
                            not run.get("events"),
                        )
                    ),
                    **(
                        _s3_url_or_error(
                            run.get("s3_urls", {}).get(SERIALIZED_S3_KEY, None),
                            SERIALIZED_S3_KEY,
                            SERIALIZED_ERROR_S3_KEY,
                            not run.get("serialized"),
                        )
                    ),
                    **{
                        k: v
                        for k, v in run.get("s3_urls", {}).items()
                        if k not in (EVENTS_S3_KEY, SERIALIZED_S3_KEY, EXTRA_S3_KEY)
                    },
                    **({ERROR_S3_KEY: error_url} if error_url else {}),
                    **({EXTRA_S3_KEY: extra_url} if extra_url else {}),
                    **({EVENTS_S3_KEY: event_url} if event_url else {}),
                    **({SERIALIZED_S3_KEY: serialized_url} if serialized_url else {}),
                }
            ).decode("utf-8")
            if extra_url
            or error_url
            or event_url
            or serialized_url
            or attachments
            or run.get("s3_urls")
            else orjson.dumps({}).decode("utf-8"),
            # inputs_preview
            inputs_preview,
            # outputs_preview
            outputs_preview,
            # inputs_kv
            inputs_kv or [],
            # outputs_kv
            outputs_kv or [],
            # thread_id
            get_run_thread_id(run),
        ]
        for (
            run,
            trace_id,
            dotted_order,
            received_at,
            modified_at,
            _,
            _,
            _,
            _,
        ), image_s3_urls, input_url, output_url, error_url, event_url, extra_url, serialized_url, input_tokens, output_tokens, error_tokens, input_bytes, output_bytes, error_bytes, event_bytes, extra_bytes, serialized_bytes, inputs_preview, outputs_preview, inputs_kv, outputs_kv, attachments in zip(
            inserts,
            image_s3_urls_list,
            input_urls,
            output_urls,
            error_urls,
            event_urls,
            extra_urls,
            serialized_urls,
            input_token_list,
            output_token_list,
            error_token_list,
            input_bytes_list,
            output_bytes_list,
            error_bytes_list,
            event_bytes_list,
            extra_bytes_list,
            serialized_bytes_list,
            inputs_preview_list,
            outputs_preview_list,
            inputs_kv_list,
            outputs_kv_list,
            attachment_urls,
        )
    ]


def _merged_s3_attachments(
    s3_urls: dict[str, dict],
    attachment_key: str,
    io_payload_url: str | None,
    io_missing: bool,
) -> str:
    results = {}
    if s3_urls.get(attachment_key):
        results.update(s3_urls[attachment_key])
    if io_payload_url:
        results[ROOT_ERROR_S3_KEY if io_missing else ROOT_S3_KEY] = io_payload_url
    return orjson.dumps(results).decode("utf-8")


def _s3_url_or_error(
    io_payload_url: str | None,
    io_key: str,
    io_error_key: str,
    io_missing: bool,
) -> dict[str, str]:
    results = {}
    if io_payload_url:
        results[io_error_key if io_missing else io_key] = io_payload_url
    return results


@redis_cache(60)
async def _model_price_map(auth: BaseAuthInfo) -> list[schemas.ModelPriceMapSchema]:
    return await fetch_model_price_map(auth.tenant_id)


class TokenCosts(NamedTuple):
    prompt_cost: float | Decimal | None
    """Total cost from input."""
    completion_cost: float | Decimal | None
    """Total cost from generating output."""
    total_cost: float | Decimal | None
    """Total cost."""
    price_map_id: UUID | None
    prompt_cost_details: str | None
    """Breakdown of input cost by token type. Does not need to sum to prompt_cost."""
    completion_cost_details: str | None
    """Breakdown of output cost by token type.

    Does not need to sum to completion_cost."""


EMPTY_TOKEN_COSTS = TokenCosts(None, None, None, None, None, None)


def _get_cost_for_tokens(
    tokens: TokenTracker,
    run: dict,
    model_price_map: list[schemas.ModelPriceMapSchema],
) -> TokenCosts:
    """Returns (prompt_cost, completion_cost, total_cost, model_id)"""
    # require to be dict, even if explicitly set to None or other type
    if not isinstance(extra := run.get("extra", {}), dict):
        extra = {}
    if not isinstance(invocation_params := extra.get("invocation_params", {}), dict):
        invocation_params = {}
    if not isinstance(metadata := extra.get("metadata", {}), dict):
        metadata = {}

    run_start_time = datetime.fromisoformat(run["start_time"])

    if run_start_time.tzinfo is None:
        run_start_time = run_start_time.replace(tzinfo=timezone.utc)

    # currently only supporting LLM
    # TODO: add support for embedding models etc.
    if run["run_type"] != "llm":
        return EMPTY_TOKEN_COSTS
    if (
        tokens.get("completion_cost") is not None
        or tokens.get("prompt_cost") is not None
        or tokens.get("total_cost") is not None
        or tokens.get("completion_cost_details") is not None
        or tokens.get("prompt_cost_details") is not None
    ):
        return TokenCosts(
            tokens.get("prompt_cost")  # type: ignore[arg-type]
            if tokens.get("prompt_cost") is not None
            else None,
            tokens.get("completion_cost")  # type: ignore[arg-type]
            if tokens.get("completion_cost") is not None
            else None,
            tokens.get("total_cost")  # type: ignore[arg-type]
            if tokens.get("total_cost") is not None
            else None,
            None,
            orjson.dumps(tokens.get("prompt_cost_details")).decode("utf-8"),
            orjson.dumps(tokens.get("completion_cost_details")).decode("utf-8"),
        )

    metadata_provider = metadata.get("ls_provider")
    metadata_model = str(metadata.get("ls_model_name", ""))

    for price_map in model_price_map:
        # if we do have a provider set in price map and provider is present
        # in unified invocation params, compare and skip if not matching
        if price_map.provider and metadata_provider:
            if price_map.provider.lower() != metadata_provider.lower():
                continue

        # prefer the model name from metadata over the one from invocation params
        input_model: str = metadata_model

        if not input_model:
            for match_path in price_map.match_path:
                value = invocation_params.get(match_path)
                if isinstance(value, str) and len(value) > 0:
                    input_model = value
                    break

        if price_map.start_time and run_start_time < price_map.start_time:
            continue

        if not input_model or not price_map.match_pattern:
            continue

        if not isinstance(input_model, str):
            # Defensive check in case above logic changes
            logger.warning(f"Model name is not a string {input_model=}, skipping")
            continue

        try:
            if re.match(price_map.match_pattern, input_model):
                prompt_cost, prompt_cost_details = _detailed_cost(
                    total_tokens=tokens["prompt_tokens"] or 0,
                    token_breakdown=tokens.get("prompt_token_details") or {},
                    price_breakdown=price_map.prompt_cost_details or {},
                    default_price=price_map.prompt_cost,
                )
                completion_cost, completion_cost_details = _detailed_cost(
                    total_tokens=tokens["completion_tokens"] or 0,
                    token_breakdown=tokens.get("completion_token_details") or {},
                    price_breakdown=price_map.completion_cost_details or {},
                    default_price=price_map.completion_cost,
                )
                total_cost = (
                    (prompt_cost or Decimal("0.0"))
                    + (completion_cost or Decimal("0.0"))
                    if (prompt_cost or completion_cost) is not None
                    else None
                )
                return TokenCosts(
                    prompt_cost,
                    completion_cost,
                    total_cost,
                    UUID(price_map.id.hex),
                    orjson.dumps(
                        {k: str(v) for k, v in prompt_cost_details.items()}
                        if prompt_cost_details
                        else prompt_cost_details
                    ).decode("utf-8"),
                    orjson.dumps(
                        {k: str(v) for k, v in completion_cost_details.items()}
                        if completion_cost_details
                        else completion_cost_details
                    ).decode("utf-8"),
                )
        except Exception:
            logger.exception(
                f"Failed to match model price map {price_map.match_pattern=} with input model {input_model=}",
            )
            return EMPTY_TOKEN_COSTS

    return EMPTY_TOKEN_COSTS


def _detailed_cost(
    total_tokens: int,
    token_breakdown: dict[str, int],
    price_breakdown: dict[str, Decimal],
    default_price: Decimal,
) -> tuple[Decimal, dict[str, Decimal] | None]:
    cost_breakdown = {}
    accounted_tokens = 0
    for k, p in price_breakdown.items():
        if k in token_breakdown:
            cost_breakdown[k] = p * Decimal(token_breakdown[k])
            accounted_tokens += token_breakdown[k]
    total_cost = (
        sum(cost_breakdown.values()) + (total_tokens - accounted_tokens) * default_price
    )
    return total_cost, cost_breakdown or None


async def _get_dataset_ids_from_cache(
    auth: BaseAuthInfo, example_ids: list[str]
) -> dict[str, dict]:
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )
    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.READ
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        for example_id in example_ids:
            example_to_dataset_key = f"smith:runs:example_dataset_with_metadata:tenant:{str(auth.tenant_id)}:example:{str(example_id)}"
            pipe.get(example_to_dataset_key)
        example_data = await pipe.execute()
        parsed_data = [
            orjson.loads(data) if data is not None else None for data in example_data
        ]

    return {
        example_id: {
            "dataset_id": (data["dataset_id"] if data is not None else None),
            "metadata": (data["metadata"] if data is not None else None),
        }
        for example_id, data in zip(example_ids, parsed_data)
    }


async def _send_dataset_ids_to_cache(
    auth: BaseAuthInfo, example_ids: list[str], example_rows: list[dict]
) -> None:
    retreived_dataset_ids = [row["dataset_id"] for row in example_rows]
    retreived_metadata = [row["metadata"] for row in example_rows]
    use_redis_transaction = not redis.is_redis_cluster_ingestion_enabled(
        str(auth.tenant_id)
    )
    async with (
        redis.aredis_routed_pool(
            str(auth.tenant_id), redis.RedisOperation.WRITE
        ) as aredis,
        aredis.pipeline(transaction=use_redis_transaction) as pipe,
    ):
        for example_id, dataset_id, metadata in zip(
            example_ids, retreived_dataset_ids, retreived_metadata
        ):
            example_to_dataset_key = f"smith:runs:example_dataset_with_metadata:tenant:{str(auth.tenant_id)}:example:{str(example_id)}"
            pipe.set(
                example_to_dataset_key,
                orjson.dumps({"dataset_id": str(dataset_id), "metadata": metadata}),
                ex=settings.RUN_DATASET_ID_CACHE_TTL,
            )
        await pipe.execute()


async def _fetch_dataset_ids_from_pg(
    auth: BaseAuthInfo, example_ids_to_check: list[str]
) -> list[dict]:
    async with database.asyncpg_pool() as pg:
        if settings.LOG_DATASET_IDS_QUERY:
            logger.info(
                "Fetching dataset ids and metadata for examples",
                extra={
                    "example_ids": example_ids_to_check,
                    "tenant_id": auth.tenant_id,
                },
            )
        # TODO: fetch correct version of example based on session metadata dataset_version
        rows = await pg.fetch(
            """SELECT DISTINCT ON (examples_log.id) examples_log.id, examples_log.dataset_id, examples_log.metadata
                FROM examples_log
                INNER JOIN dataset ON examples_log.dataset_id = dataset.id
                WHERE examples_log.id = ANY($1)
                AND examples_log.inputs IS NOT NULL
                AND dataset.tenant_id = $2
                ORDER BY examples_log.id, examples_log.modified_at DESC""",
            example_ids_to_check,
            auth.tenant_id,
        )
        if len(rows) != len(example_ids_to_check):
            raise HTTPException(
                status_code=404,
                detail=f"Dataset not found for examples {set(example_ids_to_check) - {row['id'] for row in rows}}",
            )
    return rows


async def _dataset_ids(auth: BaseAuthInfo, inserts: list[RunInsert]) -> dict[str, dict]:
    example_ids: list[str] = list(
        set(filter(None, (ins.payload["reference_example_id"] for ins in inserts)))
    )
    if not example_ids:
        return {}

    # first check if we have the example's dataset id in cache
    example_to_dataset_ids = await _get_dataset_ids_from_cache(auth, example_ids)

    example_ids_to_check = [
        example_id
        for example_id in example_to_dataset_ids
        if example_to_dataset_ids[example_id].get("dataset_id") is None
    ]

    # if we don't have the example's dataset id in cache, fetch it from pg
    if len(example_ids_to_check) > 0:
        rows = await _fetch_dataset_ids_from_pg(auth, example_ids_to_check)
        await _send_dataset_ids_to_cache(auth, example_ids_to_check, rows)
        for row in rows:
            example_to_dataset_ids[str(row["id"])] = {
                "dataset_id": str(row["dataset_id"]),
                "metadata": row["metadata"],
            }
    return {
        k: v for k, v in example_to_dataset_ids.items() if v is not None
    }  # for mypy


async def _process_image_uploads(
    auth: BaseAuthInfo,
    inserts: list[RunInsert],
    session_id: UUID,
    semaphore: asyncio.Semaphore,
) -> list[dict[str, dict]]:
    return await asyncio.gather(
        *(
            gated_coro(
                process_mm_run_io(
                    auth,
                    session_id,
                    ins.payload["inputs"],
                    ins.payload["outputs"],
                ),
                semaphore,
            )
            for ins in inserts
        )
    )


def _add_session_id(insert: FeedbackInsert, session_id: UUID) -> FeedbackInsert:
    return FeedbackInsert(
        payload=insert.payload,
        trace_id=insert.trace_id,
        session_id=session_id,
        start_time=insert.start_time,
        redis=insert.redis,
        delete=insert.delete,
    )


def queued_runs_key(auth_id: str) -> str:
    current_day = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    return f"smith:runs:queued_runs:{auth_id}:{current_day}"


def uploaded_manifests_key(auth_id: str) -> str:
    current_day = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    return f"smith:manifests:uploaded:{auth_id}:{current_day}"


async def process_s3_run_io_ttl(
    run: list[Any],
    s3_column_name: str,
    target_tier: schemas.TraceTier = schemas.TraceTier.longlived,
) -> None:
    """
    Upgrade all S3 objects referenced in *s3_column_name* for the given ClickHouse
    *run* row so they live under the long‑lived prefix.

    • URLs containing "#" → **download & re‑upload**
    • All others          → **copy_object** to new path
    """
    s3_column_index = ch_cols_list.index(s3_column_name)

    run_id = run[ch_cols_list.index("id")]
    session_id = run[ch_cols_list.index("session_id")]
    tenant_id = run[ch_cols_list.index("tenant_id")]

    s3_urls: dict[str, str] = orjson.loads(run[s3_column_index])

    for key, url in s3_urls.items():
        # ─────────────────────── download / re‑upload branch ─────────────────
        if "#" in url:
            new_url = await _upgrade_s3_url_download_reupload(
                url,
                key=key,
                column_name=s3_column_name,
                tenant_id=tenant_id,
                session_id=session_id,
                run_id=run_id,
                target_tier=target_tier,
            )
            if new_url:
                s3_urls[key] = new_url
            continue

        # ─────────────────────────── copy‑object branch ─────────────────────
        src, dst = _build_longlived_path(url, target_tier)
        await copy_object(src, dst)
        logger.info(
            "Upgrading S3 TTL to longlived",
            s3_url=src,
            upgraded_s3_url=dst,
            run_id=run_id,
        )
        s3_urls[key] = dst

    if s3_urls:
        run[s3_column_index] = orjson.dumps(s3_urls).decode("utf-8")


async def _upgrade_s3_url_download_reupload(
    url: str,
    *,
    key: str,
    column_name: str,
    tenant_id: str,
    session_id: str,
    run_id: str,
    target_tier: schemas.TraceTier = schemas.TraceTier.longlived,
) -> str | None:
    """
    Download *url* and re‑upload it under the *target_tier* prefix.

    Returns the upgraded S3 URL or **None** if the download fails.
    """
    if key == ROOT_S3_KEY:
        root_map = {
            "inputs_s3_urls": "inputs",
            "outputs_s3_urls": "outputs",
        }
        io_type = root_map.get(column_name, key)
    else:
        io_type = IO_TYPE_MAPPING.get(key, key)

    data_and_size = await get_run_data_and_size(url)
    if not data_and_size:
        logger.warning(
            "Failed to download data for re‑upload",
            s3_url=url,
            run_id=run_id,
        )
        return None

    data, _ = data_and_size
    data_bytes = orjson.dumps(data)

    new_path = await upload_run_data(
        data_bytes,
        io_type,
        tenant_id,
        session_id,
        run_id,
        target_tier.value,
        settings.S3_SINGLE_REGION_BUCKET_NAME,
    )
    logger.info(
        "Upgrading S3 TTL to longlived via download/re‑upload",
        s3_url=url,
        upgraded_s3_url=new_path,
        run_id=run_id,
    )
    return new_path


def _build_longlived_path(url: str, target_tier: schemas.TraceTier) -> tuple[str, str]:
    """
    Return a tuple **(src, dst)** where:
    • *src* is the original *url* (unchanged),
    • *dst* is the correct long‑lived prefix for *url*.
    """
    bucket, without_ttl_path = strip_ttl_prefix(url)
    bucket_prefix = bucket + "/" if bucket else ""

    dst = f"{bucket_prefix}{settings.S3_TRACE_TIER_PREFIX_MAP[target_tier.value]}/{without_ttl_path}"
    return url, dst


async def upgrade_batched_ch_traces(
    auth_dict: dict[str, Any],
    lookback_period_minutes=settings.CH_UPGRADE_LOOKBACK_MINUTES,
    batch_limit=settings.CH_UPGRADE_BATCH_LIMIT,
    target_tier: schemas.TraceTier = schemas.TraceTier.longlived,
    lb_trace_context: Optional[str] = None,
    semaphore: asyncio.Semaphore = asyncio.Semaphore(settings.ASYNC_WORKER_SEMAPHORE),
) -> None:
    inflight_key = f"lock_upgrade_ch_traces:{auth_dict['tenant_id']}"
    async with redis.renewable_lock(
        inflight_key, settings.CH_UPGRADE_LOCK_TIMEOUT
    ) as lock:
        if not lock:
            async with redis.async_queue(settings.UPGRADES_QUEUE) as queue:
                logger.info("Lock not acquired, rescheduling upgrade_batched_ch_traces")
                # Need to reschedule in future via enqueue since the current job is deduped by key
                await queue.enqueue(
                    "schedule_upgrade_batched_ch_traces",
                    auth_dict=auth_dict,
                    lb_trace_context=lb_trace_context,
                    scheduled=(
                        datetime.now(timezone.utc)
                        + timedelta(seconds=settings.CH_UPGRADE_BATCH_DELAY_SEC)
                    ).timestamp(),
                    **redis.DEFAULT_JOB_KWARGS,
                )
            return
        await logger.ainfo("Upgrading CH Traces", tenant_id=auth_dict["tenant_id"])

        async with clickhouse.clickhouse_client(
            clickhouse.ClickhouseClient.USER_HEAVY_WORKLOADS_SLOW
        ) as ch:
            # Order runs so that when we limit, children are upgraded before their root run
            # Handled the case with a single trace spans multiple batches
            # So that on next iteration, the root run returns as not-yet-upgraded
            query = (
                """
                WITH target_traces as (
                    SELECT session_id, is_root, start_time, id FROM runs_trace_id FINAL
                    WHERE tenant_id = {tenant_id} AND trace_tier != {target_tier}
                    AND (session_id, trace_id) IN(
                        SELECT session_id, trace_id FROM trace_upgrades
                        WHERE tenant_id = {tenant_id}
                        AND ingestion_method = 'clickhouse'
                        AND inserted_at >= {upgrade_insert_cutoff}
                        ORDER BY inserted_at ASC
                    ) LIMIT {limit}
                ),
                target_runs as (
                """
                + f"SELECT {ch_cols}, inserted_at from runs "
                + """
                    WHERE tenant_id = {tenant_id}
                    AND (session_id, is_root, start_time, id) in target_traces
                    AND trace_tier != {target_tier}
                )
                """
                + f"SELECT {ch_cols} FROM runs "
                + """WHERE (tenant_id, session_id, id, inserted_at) IN (SELECT tenant_id, session_id, id, max(inserted_at) FROM target_runs GROUP BY tenant_id, session_id, id)
                ORDER BY runs.trace_first_received_at, runs.start_time DESC, runs.is_root
                """
                + f"SETTINGS max_memory_usage={settings.CH_UPGRADE_MAX_MEMORY_USAGE}, max_execution_time={settings.CH_UPGRADE_MAX_EXECUTION_TIME}"
                ""
            )
            results = await ch.fetch(
                "fetch_ch_ttl_batched_runs",
                query,
                params={
                    "tenant_id": auth_dict["tenant_id"],
                    "target_tier": target_tier.value,
                    "upgrade_insert_cutoff": convert_datetime(
                        datetime.now(timezone.utc)
                        - timedelta(minutes=lookback_period_minutes)
                    ),
                    "limit": batch_limit,
                },
            )
            # Note that the driver has an issue reading results with certain escape codes
            # In this case we want to avoid those rescords and continue
            valid_results = []
            for result in results:
                try:
                    # try to decode the result to ensure it is valid
                    result._decode()
                    valid_results.append(result)
                except Exception as e:
                    # If there is an issue parsing the results by the driver, we can't process this record.
                    await logger.aerror(
                        "Batch Clickhouse Upgrade: Error parsing results for TTL upgrade",
                        exc_info=e,
                    )

            logger.info(
                "Fetched batched runs for TTL upgrades",
                run_count=len(valid_results),
                run_ids=[str(run["id"]) for run in valid_results],
            )

            await process_insert_upgrades(
                auth_dict, valid_results, target_tier, semaphore
            )

            if len(valid_results) == batch_limit:
                await logger.ainfo(
                    "More results found, renqueue upgrade_batched_ch_traces",
                    tenant_id=auth_dict["tenant_id"],
                )
                async with redis.async_queue(settings.UPGRADES_QUEUE) as queue:
                    await queue.enqueue(
                        "schedule_upgrade_batched_ch_traces",
                        auth_dict=auth_dict,
                        lb_trace_context=lb_trace_context,
                        scheduled=(
                            datetime.now(timezone.utc)
                            + timedelta(seconds=settings.CH_UPGRADE_BATCH_DELAY_SEC)
                        ).timestamp(),
                        **redis.DEFAULT_JOB_KWARGS,
                    )


async def process_insert_upgrades(
    auth_dict: dict[str, Any],
    results: list[Record],
    target_tier: schemas.TraceTier,
    semaphore: asyncio.Semaphore,
) -> None:
    def _filter_valid_kv_pairs(kv_list):
        return [
            r
            for r in (kv_list or [])
            if isinstance(r, tuple)
            and len(r) == 2
            and all(isinstance(item, str) for item in r)
        ]

    inserts = []
    trace_tier_idx = ch_cols_list.index("trace_tier")
    ttl_seconds_idx = ch_cols_list.index("ttl_seconds")
    trace_upgrade_idx = ch_cols_list.index("trace_upgrade")
    modified_at_idx = ch_cols_list.index("modified_at")
    inputs_kv_idx = ch_cols_list.index("inputs_kv")
    outputs_kv_idx = ch_cols_list.index("outputs_kv")

    for row in results:
        run = list(row[:])
        run[trace_tier_idx] = target_tier.value
        run[ttl_seconds_idx] = settings.TRACE_TIER_TTL_DURATION_SEC_MAP[target_tier]
        if run[inputs_kv_idx]:
            run[inputs_kv_idx] = _filter_valid_kv_pairs(run[inputs_kv_idx])
        if run[outputs_kv_idx]:
            run[outputs_kv_idx] = _filter_valid_kv_pairs(run[outputs_kv_idx])
        run[trace_upgrade_idx] = True
        run[modified_at_idx] = datetime.now(timezone.utc).strftime(CH_INSERT_TIME)
        inserts.append(run)

    if not inserts:
        await logger.ainfo("No runs to upgrade on CH", tenant_id=auth_dict["tenant_id"])
        return

    tasks = []
    tasks.extend(
        [
            gated_coro(
                process_s3_run_io_ttl(run, "inputs_s3_urls"),
                semaphore,
            )
            for run in inserts
        ]
    )
    tasks.extend(
        [
            gated_coro(
                process_s3_run_io_ttl(run, "outputs_s3_urls"),
                semaphore,
            )
            for run in inserts
        ]
    )
    tasks.extend(
        [
            gated_coro(
                process_s3_run_io_ttl(run, "s3_urls"),
                semaphore,
            )
            for run in inserts
        ]
    )
    async with semaphore:
        results = await asyncio.gather(*tasks, return_exceptions=True)

    all_exceptions = []
    inserts_to_upsert = []
    # Currently this will raise error and be visible in monitoring.
    # Can consider exposing this in product if this becomes an issue though.
    for insert, exceptions in zip(inserts, results):
        if isinstance(exceptions, Exception):
            await logger.aerror(
                "Exception during asset processing", error=str(exceptions)
            )
            all_exceptions.append(exceptions)
        else:
            inserts_to_upsert.append(insert)

    await upsert_prepared_runs(
        inserts_to_upsert, str(auth_dict["tenant_id"]), wait_for_async_insert=True
    )

    # raise if exceptions happend during asset processing
    if all_exceptions:
        if all(isinstance(exc, Exception) for exc in all_exceptions):
            raise ExceptionGroup(
                "Exception during asset moves for CH trace upgrade",
                cast(list[Exception], all_exceptions),
            )
        else:
            # cannot pass BaseException to ExceptionGroup
            raise all_exceptions[0]


async def _validate_and_assign_payload_data(
    payload: RunIngestDict,
    key: str,
    data: Any,
    size: int,
    run_id: str,
    logger: Any,
) -> None:
    """Internal method to validate and assign payload data with proper type checking."""
    if (
        (key in ("inputs", "outputs") and isinstance(data, dict))
        or (key == "events" and isinstance(data, list))
        or (key == "error" and isinstance(data, str))
        or (key == "extra" and isinstance(data, dict))
        or (key == "serialized" and isinstance(data, dict))
    ):
        payload[key] = data  # type: ignore[literal-required]
        # Convert size to int if it's a string
        size_value = int(size) if isinstance(size, str) else size
        payload[key + "_size"] = size_value  # type: ignore[literal-required]
    else:
        await logger.awarning(
            f"Received invalid {key} type {type(data)} in extra s3 for run {run_id}, ingesting without."
        )

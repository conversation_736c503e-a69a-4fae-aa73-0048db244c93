import asyncio
import datetime
import time
import uuid

import structlog
from lc_config.settings import shared_settings
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import BaseAuthInfo
from app.models.charts import constants as chart_constants
from app.models.charts.create import create_custom_chart, create_custom_chart_section
from app.models.charts.fetch import (
    fetch_custom_charts_sections,
    fetch_prebuilt_dashboard_for_session,
    fetch_single_custom_chart_section,
)
from app.models.charts.update import update_custom_chart
from app.models.charts.utils import (
    get_prefetch_cache_ttl_sec,
    get_prefetch_enabled_start_end_time,
)
from app.models.shared.utils import (
    list_workspaces_for_auth,
)
from app.schemas import CustomChartsSectionRequest, RunStatsGroupBy, TimedeltaInput

logger = structlog.get_logger(__name__)


PREFETCH_INTERVAL_DAYS = [7, 14]
WINDOW_DAYS_TO_STRIDE_HOURS = {7: 8, 14: 12}
WINDOW_DAYS_TO_STRIDE_MINUTES = {
    w: s_hr * 60 for w, s_hr in WINDOW_DAYS_TO_STRIDE_HOURS.items()
}


async def process_org(organization_id: uuid.UUID):
    from app.api.auth.verify import internal_org_auth_request

    logger.info("Processing org charts", organization_id=organization_id)
    inflight_key = f"process_org_charts:{str(organization_id)}"
    async with redis.renewable_lock(
        inflight_key, shared_settings.ORG_CHARTS_JOB_TIMEOUT_SEC - 1
    ) as lock:
        if not lock:
            await logger.awarning(
                f"Failed to acquire lock {inflight_key}, already running, exiting",
                organization_id=organization_id,
            )
            return
        logger.info("Acquired lock for org charts", organization_id=organization_id)
        auth = await internal_org_auth_request(organization_id)
        logger.debug(
            "Retrieved org auth for org charts", organization_id=organization_id
        )
        access_scope = schemas.AccessScope.organization
        try:
            # Create usage section if needed
            org_usage_sections, _ = await fetch_custom_charts_sections(
                # Need to explicitly set Query fields to None here, otherwise they are present as Field objects
                schemas.CustomChartsSectionsRequest(
                    title_contains=chart_constants.ORG_USAGE_SECTION_TITLE,
                    limit=2,
                    offset=0,
                    sort_by="created_at",
                    sort_by_desc=False,
                    ids=None,
                    tag_value_id=None,
                ),
                auth,
                access_scope,
            )
            logger.debug(
                "retrieved custom chart sections", organization_id=organization_id
            )
            if org_usage_sections:
                section = org_usage_sections[0]
            else:
                section = await create_custom_chart_section(
                    auth=auth,
                    section=schemas.CustomChartsSectionCreateInternal(
                        title=chart_constants.ORG_USAGE_SECTION_TITLE,
                        index=0,
                        access_scope=access_scope,
                    ),
                )
                logger.debug(
                    "created custom chart section", organization_id=organization_id
                )

            workspaces = await list_workspaces_for_auth(auth)
            if not workspaces:
                logger.warning(
                    "Detected zero workspaces for organization, halting.",
                    organization_id=organization_id,
                )
                return
            workspaces_to_chart = workspaces[: shared_settings.CHARTS_MAX_SERIES_ORG]
            create_for_org_chart_req = schemas.CustomChartCreateInternal(
                title=chart_constants.ORG_USAGE_TITLE,
                description=chart_constants.ORG_USAGE_DESC,
                chart_type="line",
                section_id=section.id,
                access_scope=access_scope,
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=chart_constants.ORG_USAGE_SERIES_NAME,
                        filters=schemas.CustomChartSeriesFilters(
                            filter="eq(is_root, true)"
                        ),
                        metric=schemas.CustomChartMetric.run_count,
                    )
                ],
            )
            create_by_ws_chart_req = schemas.CustomChartCreateInternal(
                title=chart_constants.ORG_USAGE_BY_WS_TITLE,
                description=chart_constants.ORG_USAGE_DESC,
                chart_type="line",
                section_id=section.id,
                access_scope=access_scope,
                series=[
                    schemas.CustomChartSeriesCreate(
                        name=ws.display_name,
                        filters=schemas.CustomChartSeriesFilters(
                            filter="eq(is_root, true)"
                        ),
                        metric=schemas.CustomChartMetric.run_count,
                        workspace_id=ws.id,
                    )
                    for ws in workspaces_to_chart
                ],
            )
            if not section.chart_count:
                logger.debug("creating custom charts", organization_id=organization_id)
                await create_custom_chart(auth, create_for_org_chart_req)
                await create_custom_chart(auth, create_by_ws_chart_req)
            else:
                logger.debug(
                    "refreshing custom charts", organization_id=organization_id
                )
                # Refresh existing chart in case workspace names have changed or new workspaces have been created
                start_time = datetime.datetime.now(
                    datetime.timezone.utc
                ) - datetime.timedelta(hours=1)
                section_with_charts = await fetch_single_custom_chart_section(
                    section.id,
                    schemas.CustomChartsSectionRequest(
                        start_time=start_time,
                    ),
                    auth,
                    access_scope,
                    include_chart_data=False,
                )
                if not section_with_charts.charts:
                    logger.debug(
                        "creating custom charts for section",
                        organization_id=organization_id,
                    )
                    # Defensive check - there should be charts based on chart_count
                    await create_custom_chart(auth, create_for_org_chart_req)
                    await create_custom_chart(auth, create_by_ws_chart_req)
                else:
                    num_existing_charts = len(section_with_charts.charts)
                    logger.info(f"Found {num_existing_charts} existing charts")
                    charts_by_title = {c.title: c for c in section_with_charts.charts}

                    existing_chart_for_org = charts_by_title.get(
                        chart_constants.ORG_USAGE_TITLE
                    )
                    if existing_chart_for_org:
                        logger.debug(
                            "updating custom charts for section",
                            organization_id=organization_id,
                        )
                        base_req = create_for_org_chart_req.model_dump()
                        base_req["index"] = 0
                        await update_custom_chart(
                            auth,
                            existing_chart_for_org.id,
                            schemas.CustomChartUpdateInternal.model_validate(base_req),
                        )
                    if not existing_chart_for_org and num_existing_charts < len(
                        chart_constants.AUTO_CREATED_ORG_CHARTS
                    ):
                        logger.warning(
                            f"Could not find {chart_constants.ORG_USAGE_TITLE} chart in charts {section_with_charts.charts}, creating"
                        )
                        await create_custom_chart(auth, create_for_org_chart_req)
                    else:
                        logger.warning(
                            f"Found {num_existing_charts} charts, skipping {chart_constants.ORG_USAGE_TITLE}"
                        )

                    existing_chart_by_ws = charts_by_title.get(
                        chart_constants.ORG_USAGE_BY_WS_TITLE
                    )
                    if existing_chart_by_ws:
                        base_req = create_by_ws_chart_req.model_dump()
                        base_req["index"] = 1
                        logger.debug(
                            "updating by-workspace chart",
                            organization_id=organization_id,
                        )
                        await update_custom_chart(
                            auth,
                            existing_chart_by_ws.id,
                            schemas.CustomChartUpdateInternal.model_validate(base_req),
                        )
                    elif num_existing_charts < len(
                        chart_constants.AUTO_CREATED_ORG_CHARTS
                    ):
                        logger.warning(
                            f"Could not find {chart_constants.ORG_USAGE_BY_WS_TITLE} chart in charts {section_with_charts.charts}, creating"
                        )
                        await create_custom_chart(auth, create_by_ws_chart_req)
                        num_existing_charts += 1
                    else:
                        logger.warning(
                            f"Found {num_existing_charts} charts, skipping {chart_constants.ORG_USAGE_BY_WS_TITLE}"
                        )
        except Exception as e:
            logger.exception(
                "Error syncing org charts", organization_id=organization_id
            )
            raise e

        logger.info(
            "Successfully processed org charts", organization_id=organization_id
        )


async def sync_org_charts(filter_org_ids: list[uuid.UUID] | None = None):
    """
    For orgs with organization charts enabled
    1. Create the standard set of charts if they don't already exist
    2. Add series for new workspaces if they're not already part of the chart
    Optionally filter to specific IDs for testing
    """
    logger.info("Syncing org charts")
    async with asyncpg_conn() as db:
        # Get all organizations with org charts enabled
        orgs = await db.fetch(
            """
            SELECT id
            FROM organizations
            WHERE COALESCE((config->'enable_org_usage_charts')::bool, false)
            """
        )

        if not orgs:
            logger.info("No orgs with org charts enabled")
            return

        org_ids = [org["id"] for org in orgs]
        if filter_org_ids:
            org_ids = list(set(org_ids) & set(filter_org_ids))
        if not org_ids:
            logger.info(
                "No orgs to process after filtering", filter_org_ids=filter_org_ids
            )
            return

    logger.info("Processing orgs", org_ids=org_ids)
    coroutines = [process_org(org_id) for org_id in org_ids]
    await asyncio.gather(*coroutines)


async def _get_top_volume_sessions_for_tenant(tenant_id: uuid.UUID):
    """Get the high volume sessions for the tenant"""
    query = f"""
        SELECT 
            arrayJoin(topK({settings.PREFETCH_PREBUILT_MAX_SESSIONS_PER_TENANT})(session_id)) AS top_sessions 
        from runs_lite
        WHERE 
            tenant_id = {{tenant_id}}
            AND (start_time > date_sub(NOW(), interval {{interval}} minute))
        SETTINGS
            max_threads = 4
    """
    interval_min = 14 * 24 * 60  # 14 days
    async with clickhouse_client(ClickhouseClient.USER_ANALYTICS) as ch:
        raw_session_ids = await ch.fetch(
            "fetch_topk_sessions_for_prebuilt_dashboards",
            query,
            params={"tenant_id": tenant_id, "interval": interval_min},
        )
        session_ids = [sid["top_sessions"] for sid in raw_session_ids]
        return session_ids


async def prefetch_prebuilt_dashboards_job(current_time: datetime.datetime):
    """
    Prefetch pre-built dashboards to cache in redis for the top volume
    sessions for the tenants in the PREFETCH_PREBUILT_DASHBOARDS_TENANTS list.
    """
    from app.models.tracer_sessions.stats import get_tracer_session_metadata_top_k

    s = time.time()

    for (
        tenant_id,
        tenant_config,
    ) in settings.PREFETCH_PREBUILT_DASHBOARDS_TENANT_CONFIGS.items():
        auth = BaseAuthInfo(tenant_id=tenant_id)
        if "group_by_attributes" in tenant_config:
            group_by_attributes = tenant_config["group_by_attributes"]
        else:
            group_by_attributes = [None, "tag", "metadata"]
        session_ids = tenant_config.get("session_ids")

        # Get the group by fields
        group_by_params: list = []
        if None in group_by_attributes:
            group_by_params.append(None)
        if "tag" in group_by_attributes:
            group_by_params.append(RunStatsGroupBy(attribute="tag"))

        # Get the top session_ids for the tenant
        for sid in session_ids:
            for d in PREFETCH_INTERVAL_DAYS:
                stride_hrs = WINDOW_DAYS_TO_STRIDE_HOURS[d]
                start_time, end_time = get_prefetch_enabled_start_end_time(
                    current_time, d, stride_hrs, is_prefetch=True
                )

                cache_ttl = get_prefetch_cache_ttl_sec(
                    start_time, end_time, datetime.timedelta(hours=stride_hrs)
                )
                session_group_by_params = []
                if "metadata" in group_by_attributes:
                    # get the topk metadata for the session
                    topk_metadata = await get_tracer_session_metadata_top_k(
                        auth,
                        sid,
                        schemas.QueryParamsForTracerSessionMetadataSchema(
                            start_time=start_time,
                            k=5,
                            root_runs_only=True,
                        ),
                    )
                    session_group_by_params = [
                        schemas.RunStatsGroupBy(
                            attribute="metadata",
                            path=key,
                        )
                        for key in topk_metadata
                    ]

                # prefetch pre-built dashboards for the session
                for group_by in group_by_params + session_group_by_params:
                    rq = CustomChartsSectionRequest(
                        timezone="UTC",
                        start_time=start_time,
                        end_time=end_time,
                        stride=TimedeltaInput(hours=stride_hrs),
                        group_by=group_by,
                    )
                    # Get the pre-built dashboards for the session
                    await fetch_prebuilt_dashboard_for_session(
                        sid,
                        rq,
                        auth,
                        cache_ttl=cache_ttl,
                        is_prefetch=True,
                    )

        logger.info(
            "Pre-fetched pre-built dashboards for tenant",
            end_time=end_time,
            tenant_id=auth.tenant_id,
            session_ids=session_ids,
            time_taken=(time.time() - s),
        )

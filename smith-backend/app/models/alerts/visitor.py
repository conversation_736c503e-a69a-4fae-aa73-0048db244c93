from app.models.alerts.models import AlertEntity
from app.models.query_lang.parse import Comparator, Comparison, Operation, Operator
from app.models.query_lang.visitor import Visitor

KEY_ATTRIBUTES = [
    "input_key",
    "output_key",
    "metadata_key",
    "tag",
]

VALUE_KEY_ATTRIBUTES = {
    "input_value": "input_key",
    "output_value": "output_key",
    "metadata_value": "metadata_key",
}

SEARCH_ATTRIBUTES = [
    "error",
    "inputs",
    "outputs",
]


class AlertEntityVisitor(Visitor):
    """
    Visitor that determines if an alert entity matches the filter directive on the alert rule
    """

    alert_entity: AlertEntity

    def visit_comparison(self, comparison: Comparison) -> bool:
        """visit all non-key-value comparisons"""

        # do not support unary comparators
        if comparison.attribute is None:
            return False

        # clean any comparison strings
        if isinstance(comparison.value, str):
            if (
                len(comparison.value) >= 2
                and "%" == comparison.value[0]
                and "%" == comparison.value[-1]
            ) and comparison.comparator in [
                Comparator.LIKE,
                Comparator.NOTLIKE,
                Comparator.IN,
            ]:
                comparison.value = comparison.value.replace("%", "")

        # ignore case for search attributes
        if comparison.attribute in SEARCH_ATTRIBUTES:
            comparison.value = comparison.value.lower()

        # if key attributes, handle separately
        if comparison.attribute in KEY_ATTRIBUTES:
            return self.visit_key_comparison(comparison)

        # if value attributes, handle separately
        if comparison.attribute in VALUE_KEY_ATTRIBUTES:
            return self.visit_value_comparison(
                [
                    v
                    for k, v in getattr(
                        self.alert_entity, VALUE_KEY_ATTRIBUTES[comparison.attribute]
                    ).items()
                ],
                comparison.comparator,
                comparison.value,
            )

        # handle other comparisons
        if comparison.comparator == Comparator.EQ:
            return comparison.value == getattr(self.alert_entity, comparison.attribute)
        elif comparison.comparator == Comparator.NEQ:
            return comparison.value != getattr(self.alert_entity, comparison.attribute)
        elif comparison.comparator == Comparator.GT:
            return float(getattr(self.alert_entity, comparison.attribute)) > float(
                comparison.value
            )
        elif comparison.comparator == Comparator.GTE:
            return float(getattr(self.alert_entity, comparison.attribute)) >= float(
                comparison.value
            )
        elif comparison.comparator == Comparator.LT:
            return float(getattr(self.alert_entity, comparison.attribute)) < float(
                comparison.value
            )
        elif comparison.comparator == Comparator.LTE:
            return float(getattr(self.alert_entity, comparison.attribute)) <= float(
                comparison.value
            )
        elif comparison.comparator == Comparator.LIKE:
            return comparison.value in getattr(self.alert_entity, comparison.attribute)
        elif comparison.comparator == Comparator.NOTLIKE:
            return comparison.value not in getattr(
                self.alert_entity, comparison.attribute
            )
        elif comparison.comparator == Comparator.IN:
            return comparison.value in getattr(self.alert_entity, comparison.attribute)
        elif comparison.comparator == Comparator.EXISTS:
            return getattr(self.alert_entity, comparison.attribute) is not None
        else:
            raise ValueError(f"Unsupported comparator: {comparison.comparator}")

    def visit_key_comparison(self, comparison: Comparison) -> bool:
        """visit all key comparisons"""

        # do not support unary comparators
        if comparison.attribute is None:
            return False

        if (
            comparison.comparator == Comparator.EQ
            or comparison.comparator == Comparator.EXISTS
        ):
            return comparison.value in getattr(self.alert_entity, comparison.attribute)
        elif (
            comparison.comparator == Comparator.LIKE
            or comparison.comparator == Comparator.IN
        ):
            return (
                len(
                    [
                        k
                        for k in getattr(self.alert_entity, comparison.attribute)
                        if comparison.value in k
                    ]
                )
                > 0
            )
        elif comparison.comparator == Comparator.NEQ:
            return comparison.value not in getattr(
                self.alert_entity, comparison.attribute
            )
        elif comparison.comparator == Comparator.NOTLIKE:
            return (
                len(
                    [
                        k
                        for k in getattr(self.alert_entity, comparison.attribute)
                        if comparison.value in k
                    ]
                )
                == 0
            )
        else:
            raise ValueError(f"Unsupported comparator: {comparison.comparator}")

    def visit_value_comparison(self, values, comparator, op_value):
        """visit all value comparisons which may be associated with
        a key or not depending on the comparator"""
        if comparator == Comparator.EQ or comparator == Comparator.EXISTS:
            return op_value in values
        elif comparator == Comparator.NEQ:
            return op_value not in values
        elif comparator == Comparator.LIKE or comparator == Comparator.IN:
            return len([v for v in values if op_value in v]) > 0
        elif comparator == Comparator.NOTLIKE:
            return len([v for v in values if op_value in v]) == 0
        else:
            raise ValueError(f"Unsupported comparator: {comparator}")

    def visit_key_value_operation(self, operation: Operation) -> bool:
        """visit a key-value operations"""
        if (
            operation.arguments[0].comparator == Comparator.EQ
            or operation.arguments[0].comparator == Comparator.EXISTS
            or operation.arguments[0].comparator == Comparator.LIKE
        ) and self.visit_key_comparison(operation.arguments[0]):
            return self.visit_value_comparison(
                [
                    getattr(self.alert_entity, operation.arguments[0].attribute)[
                        operation.arguments[0].value
                    ]
                ],  # only value that belongs to the key is fair game
                operation.arguments[1].comparator,
                operation.arguments[1].value,
            )
        elif (
            operation.arguments[0].comparator == Comparator.NEQ
            or operation.arguments[0].comparator == Comparator.NOTLIKE
        ) and self.visit_key_comparison(operation.arguments[0]):
            return self.visit_value_comparison(
                set(
                    [
                        v
                        for _, v in getattr(
                            self.alert_entity, operation.arguments[0].attribute
                        ).items()
                    ]
                ),
                operation.arguments[1].comparator,
                operation.arguments[1].value,
            )
        return False

    def visit_operation(self, operation: Operation) -> bool:
        """visit an operation"""
        if operation.operator == Operator.AND:
            # handle key-value attributes
            if (
                len(operation.arguments) == 2
                and isinstance(operation.arguments[0], Comparison)
                and isinstance(operation.arguments[1], Comparison)
            ):
                if (
                    operation.arguments[0].attribute in KEY_ATTRIBUTES
                    and operation.arguments[1].attribute in VALUE_KEY_ATTRIBUTES
                ):
                    return self.visit_key_value_operation(operation)
                # handle case where the first argument is a value attribute and the second argument is a key attribute
                elif (
                    operation.arguments[0].attribute in VALUE_KEY_ATTRIBUTES
                    and operation.arguments[1].attribute in KEY_ATTRIBUTES
                ):
                    return self.visit_key_value_operation(
                        Operation(
                            operation.operator,
                            [operation.arguments[1], operation.arguments[0]],
                        )
                    )

            for arg in operation.arguments:
                res = arg.accept(self)
                if not res:
                    return False
            return True
        elif operation.operator == Operator.OR:
            # handle key-value attributes
            if (
                len(operation.arguments) == 2
                and isinstance(operation.arguments[0], Comparison)
                and isinstance(operation.arguments[1], Comparison)
                and operation.arguments[0].attribute in KEY_ATTRIBUTES
                and operation.arguments[1].attribute in VALUE_KEY_ATTRIBUTES
            ):
                return self.visit_key_value_operation(operation)

            for arg in operation.arguments:
                res = arg.accept(self)
                if res:
                    return True
            return False
        elif operation.operator == Operator.NOT:
            # handle key-value attributes
            if (
                len(operation.arguments) == 1
                and operation.arguments[0].attribute in KEY_ATTRIBUTES
                and operation.arguments[0].attribute in VALUE_KEY_ATTRIBUTES
            ):
                return not self.visit_key_value_operation(operation)

            return not operation.arguments[0].accept(self)
        else:
            raise ValueError(f"Unsupported operator: {operation.operator}")

import datetime

import or<PERSON><PERSON>
import structlog
from fastapi import HTTPException
from lc_config.service_communication_settings import ServiceName
from lc_database.curl import internal_platform_request
from lc_database.service_client import get_service_client

from app import config, schemas
from app.api.auth.schemas import OrgAuthInfo

logger = structlog.getLogger(__name__)


async def fetch_org_usage(
    starting_on: datetime.datetime,
    ending_before: datetime.datetime,
    on_current_plan: bool,
    auth: OrgAuthInfo,
) -> list[schemas.OrgUsage]:
    # if we are self-hosted, reach out to beacon to get usage
    if config.settings.IS_SELF_HOSTED:
        async with get_service_client(ServiceName.BEACON) as beacon_client:
            try:
                body = {
                    "license": config.settings.LANGSMITH_LICENSE_KEY,
                    "starting_on": starting_on.replace(
                        tzinfo=datetime.timezone.utc
                    ).isoformat(),
                    "ending_before": ending_before.replace(
                        tzinfo=datetime.timezone.utc
                    ).isoformat(),
                    "on_current_plan": on_current_plan,
                }
                response = await beacon_client.post(
                    "v1/beacon/usage",
                    json=body,
                )
                data = orjson.loads(response.content)
                result = [schemas.OrgUsage(**item) for item in data]
            except Exception as e:
                logger.error("Failed to get usage from beacon", error=e)
                raise HTTPException(502, "Failed to fetch usage")
            return result
    else:
        try:
            params = {
                "starting_on": starting_on.replace(
                    tzinfo=datetime.timezone.utc
                ).isoformat(),
                "ending_before": ending_before.replace(
                    tzinfo=datetime.timezone.utc
                ).isoformat(),
                "on_current_plan": str(on_current_plan).lower(),
            }
            response = await internal_platform_request(
                "GET",
                "/internal/org-usage",
                headers={"X-Organization-Id": str(auth.organization_id)},
                params=params,
                jwt_payload={"organization_id": str(auth.organization_id)},
                raise_error=True,
            )
            data = orjson.loads(response.body)
            result = [schemas.OrgUsage(**item) for item in data]
        except Exception as e:
            logger.error("Failed to get usage from platform-backend", error=e)
            raise HTTPException(502, "Failed to fetch usage")

        return result

from typing import Optional, Sequence
from uuid import UUID, uuid4

import asyncpg
from fastapi import HTTPException
from lc_database import database

from app import config, schemas
from app.api.auth import AuthInfo
from app.api.auth.api_keys import generate_key
from app.models.service_accounts.crud import (
    create_service_account_within_existing_transaction,
)
from app.models.service_accounts.schemas import (
    ServiceAccountCreateRequest,
)
from app.retry import retry_asyncpg
from app.schemas import AccessScope


def shorten_api_key(api_key: str) -> str:
    return api_key[:9] + "..." + api_key[-4:]


@retry_asyncpg
async def list_org_scoped_api_keys(auth: AuthInfo) -> Sequence[dict]:
    """List org-scoped API keys"""
    async with database.asyncpg_conn() as db:
        api_keys = await db.fetch(
            """
            SELECT a.*
            FROM api_keys a
            INNER JOIN identities i ON a.identity_id = i.id
            WHERE a.organization_id = $1 AND i.user_id IS NULL
            ORDER BY created_at DESC NULLS LAST
            """,
            auth.organization_id,
        )
    return [dict(key) for key in api_keys]


@retry_asyncpg
async def list_api_keys(auth: AuthInfo) -> Sequence[dict]:
    """List workspace-scoped API keys"""
    async with database.asyncpg_conn() as db:
        api_keys = await db.fetch(
            """
            SELECT a.*
            FROM api_keys a
            INNER JOIN identities i ON a.identity_id = i.id
            WHERE a.tenant_id = $1 AND i.user_id IS NULL
            ORDER BY created_at DESC NULLS LAST
            """,
            auth.tenant_id,
        )
    return [dict(key) for key in api_keys]


@retry_asyncpg
async def create_api_key_within_existing_transaction(
    auth: AuthInfo,
    payload: schemas.APIKeyCreateRequest,
    db: asyncpg.Connection,
) -> schemas.APIKeyCreateResponse:
    # Generate a new API key
    key, hashed_api_key = generate_key("lsv2_sk")

    # Create a service account for the key (Temporarily until we expose service accounts in the UI)
    service_account = await create_service_account_within_existing_transaction(
        auth,
        ServiceAccountCreateRequest(name=payload.description),
        db,
    )
    query = """
    INSERT INTO identities
    (id, tenant_id, organization_id, role_id, access_scope, service_account_id, parent_identity_id)
    VALUES ($1, $2, $3, (SELECT id FROM roles WHERE name = $4), $5, $6, $7)
    RETURNING id
    """

    identity = await db.fetchrow(
        query,
        uuid4(),
        auth.tenant_id,
        auth.organization_id,
        "WORKSPACE_VIEWER" if payload.read_only else "WORKSPACE_ADMIN",
        schemas.AccessScope.workspace,
        service_account.id,
        service_account.organization_identity_id,
    )

    query = """
    INSERT INTO api_keys
    (id, api_key, tenant_id, short_key, identity_id, description, service_account_id, organization_id, expires_at)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING id, short_key, created_at, description, expires_at
    """

    created_row = await db.fetchrow(
        query,
        uuid4(),
        hashed_api_key,
        auth.tenant_id,
        shorten_api_key(key),
        identity["id"],
        payload.description,
        service_account.id,
        auth.organization_id,
        payload.expires_at,
    )

    return schemas.APIKeyCreateResponse(**created_row, key=key)


@retry_asyncpg
async def create_org_scoped_api_key(
    auth: AuthInfo,
    payload: schemas.APIKeyCreateRequest,
    db: asyncpg.Connection,
) -> schemas.APIKeyCreateResponse:
    key, hashed_api_key = generate_key("lsv2_sk")
    # If workspace empty, treat as all workspaces
    ws_ids = payload.workspaces or []
    if not ws_ids:
        rows = await db.fetch(
            "SELECT id FROM tenants WHERE organization_id = $1",
            auth.organization_id,
        )
        ws_ids = [row["id"] for row in rows]
    # Create service acct
    service_account = await create_service_account_within_existing_transaction(
        auth,
        ServiceAccountCreateRequest(name=payload.description),
        db,
    )
    org_identity_id = service_account.organization_identity_id
    # Create workspace identities
    identity_ids = []
    for ws_id in ws_ids:
        identity_query = """
        INSERT INTO identities
        (id, tenant_id, organization_id, role_id, access_scope, service_account_id, parent_identity_id)
        VALUES ($1, $2, $3, (SELECT id FROM roles WHERE name = $4), $5, $6, $7)
        RETURNING id
        """
        identity = await db.fetchrow(
            identity_query,
            uuid4(),
            ws_id,
            auth.organization_id,
            "WORKSPACE_ADMIN",
            AccessScope.workspace,
            service_account.id,
            org_identity_id,
        )
        identity_ids.append(identity["id"])
    identity_id = identity_ids[0]
    query = """
    INSERT INTO api_keys
    (id, api_key, tenant_id, short_key, identity_id, description, service_account_id, organization_id, expires_at)
    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    RETURNING id, short_key, created_at, description, expires_at
    """
    created_row = await db.fetchrow(
        query,
        uuid4(),
        hashed_api_key,
        ws_ids[0],
        shorten_api_key(key),
        identity_id,
        payload.description,
        service_account.id,
        auth.organization_id,
        payload.expires_at,
    )
    return schemas.APIKeyCreateResponse(**created_row, key=key)


@retry_asyncpg
async def create_api_key(
    auth: AuthInfo, payload: schemas.APIKeyCreateRequest
) -> schemas.APIKeyCreateResponse:
    """Create an API key."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await create_api_key_within_existing_transaction(auth, payload, db)


@retry_asyncpg
async def delete_api_key_within_existing_transaction(
    auth: AuthInfo, api_key_id: UUID, db: asyncpg.Connection, throw_if_not_found: bool
) -> Optional[schemas.APIKeyGetResponse]:
    try:
        # Fetch the API key details before deletion
        row = await db.fetchrow(
            "SELECT id, short_key, description, created_at, last_used_at, expires_at, service_account_id FROM api_keys WHERE id = $1",
            api_key_id,
        )
        if not row:
            if throw_if_not_found:
                raise HTTPException(status_code=404, detail="API key not found")
            return None

        # Check if this is a PAT (no service_account_id) - PATs should only be deleted via PAT endpoint
        if row["service_account_id"] is None:
            if throw_if_not_found:
                raise HTTPException(status_code=404, detail="API key not found")
            return None

        service_account_id = row["service_account_id"]

        # Delete the service account (cascades to identities and api_keys)
        await db.execute(
            "DELETE FROM service_accounts WHERE id = $1", service_account_id
        )

        return schemas.APIKeyGetResponse(**row)
    except asyncpg.exceptions.ForeignKeyViolationError:
        raise HTTPException(
            status_code=409,
            detail="Cannot delete an API Key that is associated with a LangServe Deployment. Please delete the deployment first, then try again.",
        )


@retry_asyncpg
async def delete_api_key(auth: AuthInfo, api_key_id: UUID) -> schemas.APIKeyGetResponse:
    """Delete an API key."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await delete_api_key_within_existing_transaction(
            auth, api_key_id, db, True
        )


@retry_asyncpg
async def list_personal_access_tokens(auth: AuthInfo) -> Sequence[dict]:
    """List tenant Personal Access Tokens for current user."""
    async with database.asyncpg_conn() as db:
        api_keys = await db.fetch(
            """SELECT
                a.id,
                a.short_key,
                a.created_at,
                a.description,
                a.last_used_at,
                a.expires_at
              FROM api_keys a
              INNER JOIN identities i ON a.identity_id = i.id
              WHERE i.tenant_id = $1 AND i.ls_user_id = $2 ORDER BY a.created_at DESC NULLS LAST
             """,
            auth.tenant_id,
            auth.ls_user_id,
        )
    return [dict(key) for key in api_keys]


@retry_asyncpg
async def create_personal_access_token(
    auth: AuthInfo, payload: schemas.APIKeyCreateRequest
) -> schemas.APIKeyCreateResponse:
    """Create a Personal Access Token for the current user in the tenant."""
    async with database.asyncpg_conn() as db, db.transaction():
        assert config.settings.API_KEY_SALT

        # Generate a new signed API key
        token, hashed_token = generate_key("lsv2_pt")

        # Fetch user identity
        query = """
            SELECT id FROM identities WHERE tenant_id = $1 AND ls_user_id = $2
            """

        identity = await db.fetchval(
            query,
            auth.tenant_id,
            auth.ls_user_id,
        )
        if identity is None:
            raise HTTPException(
                status_code=404,
                detail="No identity in this tenant found for the current user",
            )

        query = """
            INSERT INTO api_keys
            (id, api_key, tenant_id, short_key, identity_id, description, user_id, organization_id, ls_user_id, expires_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
            RETURNING id, short_key, created_at, description, user_id, organization_id, expires_at
            """

        created_row = await db.fetchrow(
            query,
            uuid4(),
            hashed_token,
            auth.tenant_id,
            shorten_api_key(token),
            identity,
            payload.description,
            auth.user_id,
            auth.organization_id,
            auth.ls_user_id,
            payload.expires_at,
        )

        return schemas.APIKeyCreateResponse(**created_row, key=token)


@retry_asyncpg
async def delete_personal_access_token(
    auth: AuthInfo, api_key_id: UUID
) -> schemas.APIKeyGetResponse:
    """Delete a Personal Access Token. Only the user who created the PAT can delete it."""
    async with database.asyncpg_conn() as db, db.transaction():
        query = """
        DELETE FROM api_keys a
        WHERE a.id = $1 AND a.tenant_id = $2 AND a.identity_id IN (SELECT id FROM identities WHERE tenant_id = $2 AND ls_user_id = $3)
        RETURNING a.id, short_key, a.created_at, a.description
        """
        deleted_key_row = await db.fetchrow(
            query, api_key_id, auth.tenant_id, auth.ls_user_id
        )

        assert auth.exists(deleted_key_row)

        return schemas.APIKeyGetResponse(**deleted_key_row)

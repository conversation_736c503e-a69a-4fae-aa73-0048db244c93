"""
This module handles merging feedback data from feedbacks_rmt with run metadata
from runs_lite into the runs_feedbacks_rmt_wide table.
"""

import datetime

import structlog
from lc_config.settings import shared_settings as settings
from lc_database import redis
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

logger = structlog.getLogger(__name__)

# Processing state
FEEDBACK_MERGE_LOCK_KEY = "feedback_merge_cron_lock"
FEEDBACK_MERGE_LAST_PROCESSED_KEY = "feedback_merge_last_processed_time"

# Processing configuration
LOCK_TIMEOUT_SEC = 300  # 5 minutes lock timeout
PROCESSED_TIME_EXPIRE_SEC = 86400 * 7  # 7 days
CLICKHOUSE_CLIENT_TYPE = ClickhouseClient.INGESTION


async def merge_feedback_run_metadata() -> None:
    """Main function to merge feedback data with run metadata."""
    async with redis.aredis_pool() as aredis:
        lock = aredis.lock(
            FEEDBACK_MERGE_LOCK_KEY, timeout=LOCK_TIMEOUT_SEC, blocking=False
        )

        if not await lock.acquire():
            await logger.ainfo("Feedback merge cron already running, skipping")
            return

        try:
            await logger.ainfo("Starting feedback run metadata merge")

            last_processed_time = await _get_last_processed_time(aredis)
            current_time = datetime.datetime.now(datetime.timezone.utc)

            await _process_feedback_batch(aredis, last_processed_time, current_time)

            await _update_last_processed_time(aredis, current_time)

        except Exception as e:
            await logger.aerror(
                "Error in feedback merge cron", error=str(e), exc_info=True
            )
            raise
        finally:
            await lock.release()


async def _get_last_processed_time(aredis) -> datetime.datetime:
    """Get the last processed time from Redis."""
    try:
        last_processed_str = await aredis.get(FEEDBACK_MERGE_LAST_PROCESSED_KEY)

        if last_processed_str:
            try:
                return datetime.datetime.fromisoformat(last_processed_str.decode())
            except (ValueError, AttributeError):
                await logger.awarn(
                    "Invalid last processed time format in Redis",
                    last_processed_time=last_processed_str,
                )

        await logger.ainfo(
            "No last processed time found",
            last_processed_time=last_processed_str,
        )
        return datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            minutes=10
        )
    except Exception as e:
        await logger.aerror(
            "Error getting last processed time from Redis",
            error=str(e),
            exc_info=True,
        )
        return datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(
            minutes=10
        )


async def _update_last_processed_time(aredis, timestamp: datetime.datetime) -> None:
    """Update the last processed time in Redis."""
    try:
        await aredis.set(
            FEEDBACK_MERGE_LAST_PROCESSED_KEY,
            timestamp.isoformat(),
            ex=PROCESSED_TIME_EXPIRE_SEC,
        )
    except Exception as e:
        await logger.aerror(
            "Error updating last processed time in Redis",
            error=str(e),
            exc_info=True,
        )
        raise


async def _process_feedback_batch(
    aredis, last_processed_time: datetime.datetime, current_time: datetime.datetime
) -> None:
    """Process a batch of feedbacks."""

    # Apply delta to last_processed_time to handle potential duplicates
    adjusted_last_processed_time = last_processed_time - datetime.timedelta(
        minutes=settings.FEEDBACK_MERGE_PROCESSING_DELTA_MINUTES
    )

    # Merge and insert feedback data with run metadata
    query = """
    INSERT INTO runs_feedbacks_rmt_wide
    (
        id,
        run_id,
        session_id,
        tenant_id,
        is_root,
        start_time,
        created_at,
        modified_at,
        key,
        score,
        value,
        comment,
        correction,
        trace_id,
        feedback_source,
        is_deleted,
        comparative_experiment_id,
        feedback_group_id,
        extra,
        name,
        end_time,
        run_type,
        parent_run_id,
        reference_example_id,
        reference_dataset_id,
        manifest_id,
        status,
        dotted_order,
        prompt_tokens,
        completion_tokens,
        total_tokens,
        first_token_time,
        prompt_cost,
        completion_cost,
        total_cost,
        thread_id,
        tags,
        metadata_kv,
        inputs_kv,
        outputs_kv,
        expiration_time,
        inserted_at,
        trace_tier,
        trace_ttl_seconds,
        trace_first_received_at
    )
    WITH
      -- 1) Window of feedbacks to process
      f_window AS (
        SELECT
          id,
          run_id,
          tenant_id,
          session_id,
          start_time,
          is_root,
          created_at,
          modified_at,
          key,
          score,
          value,
          comment,
          correction,
          trace_id,
          feedback_source,
          is_deleted,
          comparative_experiment_id,
          feedback_group_id,
          extra
        FROM feedbacks_rmt
        WHERE
          modified_at > {last_processed_time}
          AND modified_at <= {current_time}
          AND is_deleted = 0
      ),

      -- 2) Pre‑aggregate each run's latest row
      runs_scoped AS (
        SELECT
          rl.id AS run_id,
          rl.tenant_id,
          rl.session_id,
          argMax(modified_at,          modified_at) AS run_modified_at,
          argMax(name,                 modified_at) AS name,
          argMax(end_time,             modified_at) AS end_time,
          argMax(run_type,             modified_at) AS run_type,
          argMax(parent_run_id,        modified_at) AS parent_run_id,
          argMax(reference_example_id, modified_at) AS reference_example_id,
          argMax(reference_dataset_id, modified_at) AS reference_dataset_id,
          argMax(manifest_id,          modified_at) AS manifest_id,
          argMax(status,               modified_at) AS status,
          argMax(dotted_order,         modified_at) AS dotted_order,
          argMax(prompt_tokens,        modified_at) AS prompt_tokens,
          argMax(completion_tokens,    modified_at) AS completion_tokens,
          argMax(total_tokens,         modified_at) AS total_tokens,
          argMax(first_token_time,     modified_at) AS first_token_time,
          argMax(prompt_cost,          modified_at) AS prompt_cost,
          argMax(completion_cost,      modified_at) AS completion_cost,
          argMax(total_cost,           modified_at) AS total_cost,
          argMax(thread_id,            modified_at) AS thread_id,
          argMax(tags,                 modified_at) AS tags,
          argMax(metadata_kv,          modified_at) AS metadata_kv,
          argMax(inputs_kv,            modified_at) AS inputs_kv,
          argMax(outputs_kv,           modified_at) AS outputs_kv,
          argMax(inserted_at,          modified_at) AS inserted_at,
          argMax(trace_tier,           modified_at) AS trace_tier,
          argMax(trace_ttl_seconds,    modified_at) AS trace_ttl_seconds,
          argMax(trace_first_received_at, modified_at) AS trace_first_received_at,
          argMax(expiration_time,      modified_at) AS expiration_time
        FROM runs_lite AS rl
        WHERE
          rl.is_deleted = 0
          AND (rl.tenant_id, rl.session_id, rl.start_time) IN (
            SELECT DISTINCT
              tenant_id,
              session_id,
              start_time
            FROM f_window
          )
        GROUP BY rl.id, rl.tenant_id, rl.session_id
      )
    SELECT
      f.id,
      f.run_id,
      f.session_id,
      f.tenant_id,
      f.is_root,
      f.start_time,
      f.created_at,
      r.run_modified_at,
      f.key,
      f.score,
      f.value,
      f.comment,
      f.correction,
      f.trace_id,
      f.feedback_source,
      f.is_deleted,
      f.comparative_experiment_id,
      f.feedback_group_id,
      f.extra,
      r.name,
      r.end_time,
      r.run_type,
      r.parent_run_id,
      r.reference_example_id,
      r.reference_dataset_id,
      r.manifest_id,
      r.status,
      r.dotted_order,
      r.prompt_tokens,
      r.completion_tokens,
      r.total_tokens,
      r.first_token_time,
      r.prompt_cost,
      r.completion_cost,
      r.total_cost,
      r.thread_id,
      r.tags,
      r.metadata_kv,
      r.inputs_kv,
      r.outputs_kv,
      r.expiration_time,
      r.inserted_at,
      r.trace_tier,
      r.trace_ttl_seconds,
      r.trace_first_received_at
    FROM f_window AS f
    ANY INNER JOIN runs_scoped AS r
      ON f.run_id = r.run_id
      AND f.tenant_id = r.tenant_id
      AND f.session_id = r.session_id
    """

    params = {
        "last_processed_time": adjusted_last_processed_time.strftime(
            "%Y-%m-%d %H:%M:%S.%f"
        ),
        "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S.%f"),
    }

    try:
        async with clickhouse_client(client_type=CLICKHOUSE_CLIENT_TYPE) as client:
            await client.execute(
                name="merge_feedback_run_metadata",
                query=query,
                params=params,
            )

    except Exception as e:
        await logger.aerror("Error in feedback merge cron", error=str(e), exc_info=True)
        raise

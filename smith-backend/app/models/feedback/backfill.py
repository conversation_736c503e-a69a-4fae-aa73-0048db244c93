import argparse
import asyncio
import datetime
import math

import structlog
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

# ────────────────────────────── CONFIG ────────────────────────────── #

CLIENT_TYPE = ClickhouseClient.INTERNAL_ANALYTICS_SLOW
WINDOW_HOURS = 4
LOG_EVERY = 10

logger = structlog.get_logger(__name__)

INSERT_TEMPLATE = """
INSERT INTO runs_feedbacks_rmt_wide
(
    id, run_id, session_id, tenant_id, is_root, start_time, created_at, modified_at,
    key, score, value, comment, correction, trace_id, feedback_source, is_deleted,
    comparative_experiment_id, feedback_group_id, extra, name, end_time, run_type,
    parent_run_id, reference_example_id, reference_dataset_id, manifest_id, status,
    dotted_order, prompt_tokens, completion_tokens, total_tokens, first_token_time,
    prompt_cost, completion_cost, total_cost, thread_id, tags, metadata_kv,
    inputs_kv, outputs_kv, expiration_time, inserted_at, trace_tier,
    trace_ttl_seconds, trace_first_received_at
)
SETTINGS
  parts_to_throw_insert = 10000,
  parts_to_delay_insert = 5000,
  max_insert_block_size = 1000000000,
  min_insert_block_size_rows = 2000000,
  min_insert_block_size_bytes = 1000000000,
  max_threads = 16,
  max_insert_threads = {insert_threads},
  send_progress_in_http_headers = 1
WITH
  f_window AS (
    SELECT
      id,
      run_id,
      tenant_id,
      session_id,
      start_time,
      is_root,
      created_at,
      modified_at,
      key,
      score,
      value,
      comment,
      correction,
      trace_id,
      feedback_source,
      is_deleted,
      comparative_experiment_id,
      feedback_group_id,
      extra
    FROM feedbacks_rmt
    WHERE
      modified_at >= {start}
      AND modified_at < {end}
      AND is_deleted = 0
  ),

  runs_scoped AS (
    SELECT
      rl.id AS run_id,
      rl.tenant_id,
      rl.session_id,
      argMax(modified_at,          modified_at) AS run_modified_at,
      argMax(name,                 modified_at) AS name,
      argMax(end_time,             modified_at) AS end_time,
      argMax(run_type,             modified_at) AS run_type,
      argMax(parent_run_id,        modified_at) AS parent_run_id,
      argMax(reference_example_id, modified_at) AS reference_example_id,
      argMax(reference_dataset_id, modified_at) AS reference_dataset_id,
      argMax(manifest_id,          modified_at) AS manifest_id,
      argMax(status,               modified_at) AS status,
      argMax(dotted_order,         modified_at) AS dotted_order,
      argMax(prompt_tokens,        modified_at) AS prompt_tokens,
      argMax(completion_tokens,    modified_at) AS completion_tokens,
      argMax(total_tokens,         modified_at) AS total_tokens,
      argMax(first_token_time,     modified_at) AS first_token_time,
      argMax(prompt_cost,          modified_at) AS prompt_cost,
      argMax(completion_cost,      modified_at) AS completion_cost,
      argMax(total_cost,           modified_at) AS total_cost,
      argMax(thread_id,            modified_at) AS thread_id,
      argMax(tags,                 modified_at) AS tags,
      argMax(metadata_kv,          modified_at) AS metadata_kv,
      argMax(inputs_kv,            modified_at) AS inputs_kv,
      argMax(outputs_kv,           modified_at) AS outputs_kv,
      argMax(inserted_at,          modified_at) AS inserted_at,
      argMax(trace_tier,           modified_at) AS trace_tier,
      argMax(trace_ttl_seconds,    modified_at) AS trace_ttl_seconds,
      argMax(trace_first_received_at, modified_at) AS trace_first_received_at,
      argMax(expiration_time,      modified_at) AS expiration_time
    FROM runs_lite AS rl
    WHERE
      rl.is_deleted = 0
      AND (rl.tenant_id, rl.session_id, rl.start_time) IN (
        SELECT
          tenant_id,
          session_id,
          start_time
        FROM f_window
      )
    GROUP BY rl.id, rl.tenant_id, rl.session_id
  )
SELECT
    f.id,
    f.run_id,
    f.session_id,
    f.tenant_id,
    f.is_root,
    f.start_time,
    f.created_at,
    r.run_modified_at,
    f.key,
    f.score,
    f.value,
    f.comment,
    f.correction,
    f.trace_id,
    f.feedback_source,
    f.is_deleted,
    f.comparative_experiment_id,
    f.feedback_group_id,
    f.extra,
    r.name,
    r.end_time,
    r.run_type,
    r.parent_run_id,
    r.reference_example_id,
    r.reference_dataset_id,
    r.manifest_id,
    r.status,
    r.dotted_order,
    r.prompt_tokens,
    r.completion_tokens,
    r.total_tokens,
    r.first_token_time,
    r.prompt_cost,
    r.completion_cost,
    r.total_cost,
    r.thread_id,
    r.tags,
    r.metadata_kv,
    r.inputs_kv,
    r.outputs_kv,
    r.expiration_time,
    r.inserted_at,
    r.trace_tier,
    r.trace_ttl_seconds,
    r.trace_first_received_at
FROM f_window AS f
INNER JOIN runs_scoped AS r
  ON f.run_id = r.run_id
  AND f.tenant_id = r.tenant_id
  AND f.session_id = r.session_id
"""


async def _min_max_modified_at() -> tuple[datetime.datetime, datetime.datetime]:
    async with clickhouse_client(client_type=CLIENT_TYPE) as ch:
        result = await ch.fetchrow(
            "get_min_max_modified_at",
            "SELECT min(modified_at) as min_modified_at, max(modified_at) as max_modified_at "
            "FROM feedbacks_rmt WHERE is_deleted = 0",
        )
        # Make datetimes timezone-aware by assuming UTC
        min_dt = result["min_modified_at"]
        max_dt = result["max_modified_at"]
        if min_dt.tzinfo is None:
            min_dt = min_dt.replace(tzinfo=datetime.timezone.utc)
        if max_dt.tzinfo is None:
            max_dt = max_dt.replace(tzinfo=datetime.timezone.utc)
        return min_dt, max_dt


async def _insert_window(
    start: datetime.datetime, end: datetime.datetime, insert_threads: int
) -> None:
    params = {
        "start": start.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "end": end.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "insert_threads": insert_threads,
    }
    async with clickhouse_client(client_type=CLIENT_TYPE) as ch:
        await ch.execute("insert_feedback_run_metadata", INSERT_TEMPLATE, params=params)


async def _process_batch(
    batch_info: tuple[int, datetime.datetime, datetime.datetime], insert_threads: int
) -> None:
    """Process a single batch of data."""
    batch_num, window_start, window_end = batch_info
    try:
        await _insert_window(window_start, window_end, insert_threads)
    except Exception as e:
        logger.error("batch failed", batch=batch_num, error=str(e))
        raise e
    else:
        if batch_num % LOG_EVERY == 0:
            logger.info(
                "backfill progress",
                batch=batch_num + 1,
                window_start=window_start.isoformat(),
                window_end=window_end.isoformat(),
            )


async def main() -> None:
    parser = argparse.ArgumentParser(
        description="Backfill feedback data with run metadata"
    )
    parser.add_argument(
        "--insert-threads",
        type=int,
        default=4,
        help="Number of insert threads for ClickHouse (default: 4)",
    )
    args = parser.parse_args()

    # Get the actual end time from the database
    _, end_dt = await _min_max_modified_at()

    start_dt = datetime.datetime.fromisoformat("2025-06-30T00:00:00+00:00")

    total_hours = math.ceil((end_dt - start_dt).total_seconds() / 3600)
    num_batches = math.ceil(total_hours / WINDOW_HOURS)

    logger.info(
        "backfill start",
        start=start_dt,
        end=end_dt,
        window_hours=WINDOW_HOURS,
        batches=num_batches,
        insert_threads=args.insert_threads,
    )

    batch_tasks = []
    window_start = start_dt

    for i in range(num_batches):
        window_end = min(window_start + datetime.timedelta(hours=WINDOW_HOURS), end_dt)
        batch_tasks.append((i, window_start, window_end))
        window_start = window_end

    semaphore = asyncio.Semaphore(8)

    async def process_batch_with_semaphore(batch_info):
        async with semaphore:
            return await _process_batch(batch_info, args.insert_threads)

    await asyncio.gather(
        *[process_batch_with_semaphore(batch) for batch in batch_tasks]
    )

    logger.info("backfill complete")


if __name__ == "__main__":
    asyncio.run(main())

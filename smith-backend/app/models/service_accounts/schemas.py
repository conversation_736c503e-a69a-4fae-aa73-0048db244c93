import datetime
from uuid import UUI<PERSON>

from app.schemas import BaseModel


class ServiceAccount(BaseModel):
    id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    name: str
    organization_id: UUID
    default_workspace_id: UUID | None


class ServiceAccountWorkspaceAssignment(BaseModel):
    workspace_id: UUID
    role_id: UUID | None = None


class ServiceAccountCreateRequest(BaseModel):
    name: str
    workspaces: list[ServiceAccountWorkspaceAssignment] = []


class ServiceAccountCreateResponse(ServiceAccount):
    organization_identity_id: UUID


class ServiceAccountDeleteRequest(BaseModel):
    id: UUID


class ServiceAccountDeleteResponse(ServiceAccount):
    pass

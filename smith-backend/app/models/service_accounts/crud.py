from typing import List
from uuid import UUID, uuid4

import asyncpg
from fastapi import HTT<PERSON>Exception
from lc_database import database

from app.api.auth import AuthInfo, OrgAuthInfo
from app.api.auth import schemas as auth_schema
from app.models.service_accounts import schemas
from app.retry import retry_asyncpg
from app.schemas import AccessScope


@retry_asyncpg
async def list_service_accounts(
    auth: OrgAuthInfo,
) -> List[schemas.ServiceAccount]:
    """List Service Accounts."""
    async with database.asyncpg_conn() as db:
        service_accounts = await db.fetch(
            """
            SELECT *
            FROM service_accounts s
            WHERE organization_id = $1
            ORDER BY created_at DESC
            """,
            auth.organization_id,
        )
    return [
        schemas.ServiceAccount(**service_account)
        for service_account in service_accounts
    ]


async def create_service_account_within_existing_transaction(
    auth: AuthInfo,
    payload: schemas.ServiceAccountCreateRequest,
    db: asyncpg.Connection,
) -> schemas.ServiceAccountCreateResponse:
    query = """
    INSERT INTO service_accounts
    (id, name, organization_id)
    VALUES ($1, $2, $3)
    RETURNING *
    """

    service_account = await db.fetchrow(
        query,
        uuid4(),
        payload.name,
        auth.organization_id,
    )

    org_identity_query = """
    INSERT INTO identities
    (id, organization_id, role_id, access_scope, service_account_id)
    VALUES ($1, $2, (SELECT id FROM roles WHERE name = $3), $4, $5)
    RETURNING id
    """
    org_identity = await db.fetchrow(
        org_identity_query,
        uuid4(),
        auth.organization_id,
        auth_schema.OrganizationRoles.USER.value,
        AccessScope.organization,
        service_account["id"],
    )

    return schemas.ServiceAccountCreateResponse(
        **service_account, organization_identity_id=org_identity["id"]
    )


@retry_asyncpg
async def create_service_account(
    auth: AuthInfo, payload: schemas.ServiceAccountCreateRequest
) -> schemas.ServiceAccountCreateResponse:
    """Create a Service Account."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await create_service_account_within_existing_transaction(
            auth, payload, db
        )


async def delete_service_account_within_existing_transaction(
    auth: AuthInfo,
    service_account_id: UUID,
    db: asyncpg.Connection,
) -> schemas.ServiceAccountDeleteResponse:
    """Delete a service account within an existing transaction."""
    query = """
    DELETE FROM service_accounts 
    WHERE id = $1 AND organization_id = $2
    RETURNING *
    """
    deleted_service_account = await db.fetchrow(
        query, service_account_id, auth.organization_id
    )
    if deleted_service_account is None:
        raise HTTPException(status_code=404, detail="Service Account not found")

    return schemas.ServiceAccountDeleteResponse(**deleted_service_account)


@retry_asyncpg
async def delete_service_account(
    auth: OrgAuthInfo, service_account_id: UUID
) -> schemas.ServiceAccountDeleteResponse:
    """Delete a service account."""
    async with database.asyncpg_conn() as db, db.transaction():
        return await delete_service_account_within_existing_transaction(
            auth, service_account_id, db
        )

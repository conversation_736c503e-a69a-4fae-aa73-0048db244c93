from datetime import datetime, timezone
from enum import Enum
from typing import Any, List, TypedDict
from uuid import UUID

from pydantic import BaseModel

from app.models.query_lang.parse import Comparator, Comparison, Operation, Operator
from app.models.query_lang.translate import Visitor


class QuickwitType(Enum):
    STRING = "string"
    BOOL = "bool"
    UUID = "uuid"
    DATETIME = "datetime"
    ARRAY_TEXT = "array[text]"
    ANY = "any"
    CURSOR = "cursor"
    JSON_KEY = "json_key"  # used for input_key, output_key, extra_key
    JSON_VALUE = "json_value"  # used for input_value, output_value, extra_value


TYPE_TO_ALLOWED_COMPARATORS = {
    QuickwitType.STRING: {
        Comparator.EQ,
        Comparator.LT,
        Comparator.LTE,
        Comparator.GT,
        Comparator.GTE,
        Comparator.SEARCH,
    },
    QuickwitType.BOOL: {Comparator.EQ},
    QuickwitType.UUID: {Comparator.EQ},
    QuickwitType.DATETIME: {
        Comparator.EQ,
        Comparator.LT,
        Comparator.LTE,
        Comparator.GT,
        Comparator.GTE,
    },
    QuickwitType.ARRAY_TEXT: {Comparator.HAS},
    QuickwitType.ANY: {
        Comparator.EQ,
        Comparator.LT,
        Comparator.LTE,
        Comparator.GT,
        Comparator.GTE,
        Comparator.SEARCH,
        Comparator.HAS,
    },
    QuickwitType.CURSOR: {
        Comparator.LT,
        Comparator.GT,
    },
    QuickwitType.JSON_KEY: {
        Comparator.EQ,
    },
    QuickwitType.JSON_VALUE: {
        Comparator.EQ,
    },
}


class QuickwitResult(TypedDict):
    query_str: str | None


def round_timestamp(timestamp: int, up: bool = False) -> str:
    rounded = timestamp / 1000000
    if up:
        rounded += 1
    return datetime.fromtimestamp(rounded, tz=timezone.utc).strftime(
        "%Y-%m-%dT%H:%M:%SZ"
    )


# TODO: correctly handle cursor
def quickwit_comparison_query(
    comparison: Comparison,
    field_expr: str | None,
    value_str: str,
) -> str:
    comp = comparison.comparator

    # Note: we are using start_time for timestamp-based pruning as a performance technique,
    # It does however have second-level resolution which requires rounding it to closest value up or down
    # depending on the comparator
    if field_expr == "start_time_micros":
        if comp == Comparator.LT:
            rounded_up = round_timestamp(timestamp=int(value_str), up=True)
            return f"{field_expr}:<{value_str} AND start_time:<{rounded_up}"
        elif comp == Comparator.LTE:
            rounded_up = round_timestamp(timestamp=int(value_str), up=True)
            return f"{field_expr}:<={value_str} AND start_time:<{rounded_up}"
        elif comp == Comparator.GT:
            rounded_down = round_timestamp(timestamp=int(value_str))
            return f"{field_expr}:>{value_str} AND start_time:>{rounded_down}"
        elif comp == Comparator.GTE:
            rounded_down = round_timestamp(timestamp=int(value_str))
            return f"{field_expr}:>={value_str} AND start_time:>={rounded_down}"

    if comp == Comparator.EQ:
        return f"{field_expr}:{value_str}"
    elif comp == Comparator.LT:
        return f"{field_expr}:<{value_str}"
    elif comp == Comparator.LTE:
        return f"{field_expr}:<={value_str}"
    elif comp == Comparator.GT:
        return f"{field_expr}:>{value_str}"
    elif comp == Comparator.GTE:
        return f"{field_expr}:>={value_str}"
    elif comp == Comparator.SEARCH:
        return f"(inputs_flat:{value_str} OR outputs_flat:{value_str} OR error:{value_str})"
    elif comp == Comparator.HAS:
        return f"{field_expr}:{value_str}"
    else:
        raise ValueError(f"Unsupported comparator {comp}")


def _parse_bool(value: Any) -> bool:
    if isinstance(value, bool):
        return value
    if isinstance(value, str):
        lv = value.strip().lower()
        if lv in ["True", "true", "1"]:
            return True
        elif lv in ["False", "false", "0"]:
            return False
    raise ValueError(f"Cannot parse '{value}' as bool")


def _is_valid_uuid(value: Any) -> bool:
    try:
        _ = UUID(str(value))
        return True
    except ValueError:
        return False


def _parse_uuid(value: Any) -> str:
    if not _is_valid_uuid(value):
        raise ValueError(f"Value '{value}' is not a valid UUID")
    return str(value)


# parse a datetime to a microsecond timestamp
def _parse_datetime(value: Any) -> int:
    try:
        if isinstance(value, datetime):
            return int(value.timestamp() * 1_000_000)
        if isinstance(value, str):
            dt = datetime.fromisoformat(value)
            return int(dt.timestamp() * 1_000_000)
    except ValueError:
        raise ValueError(f"Cannot parse '{value}' as datetime")
    raise ValueError(f"Cannot parse '{value}' as datetime")


def _parse_string(value: Any) -> str:
    return str(value)


class QuickwitAttributeInfo(BaseModel):
    name: str
    quickwit_field: str | None = None
    type: QuickwitType

    def allowed_comparators(self) -> set[Comparator]:
        return TYPE_TO_ALLOWED_COMPARATORS.get(self.type, set())

    def parse_value_for_type(self, value: Any) -> str:
        """
        Parse/validate the input 'value' according to self.type.
        Return a string suitable for QuickWit query usage.
        """
        if self.type == QuickwitType.BOOL:
            return _parse_bool(value).__str__().lower()
        elif self.type == QuickwitType.UUID:
            return _parse_uuid(value)
        elif self.type == QuickwitType.DATETIME:
            return _parse_datetime(value).__str__()
        elif self.type == QuickwitType.STRING:
            return _parse_string(value)
        elif self.type == QuickwitType.ARRAY_TEXT:
            return _parse_string(value)
        elif self.type == QuickwitType.ANY:
            return _parse_string(value)
        elif self.type in (QuickwitType.JSON_KEY, QuickwitType.JSON_VALUE):
            return _parse_string(value)
        else:
            raise ValueError(f"Unknown or unsupported attribute type: {self.type}")

    def translate(self, comparison: Comparison) -> QuickwitResult:
        if comparison.attribute is None:
            raise ValueError(
                "Comparison attribute cannot be None unless comparator=SEARCH"
            )

        # check typing
        if comparison.comparator not in self.allowed_comparators():
            raise ValueError(
                f"Comparator {comparison.comparator} not allowed for type {self.type}"
            )

        # special case cursor
        if self.type == QuickwitType.CURSOR:
            # cursor is of the form "{start_time_micros}|{id_sort}"
            cursor_parts = comparison.value.split("|")
            if len(cursor_parts) != 2:
                raise ValueError(
                    f"Cursor value '{comparison.value}' does not match expected format"
                )
            start_time_micros = cursor_parts[0]
            id_sort = cursor_parts[1]

            # Note: we are using start_time for timestamp-based pruning as a performance technique,
            # It does however have second-level resolution which requires rounding it to closest value up or down
            # depending on the comparator
            if comparison.comparator == Comparator.LT:
                op = "<"
                start_time = round_timestamp(int(start_time_micros), up=True)
            elif comparison.comparator == Comparator.GT:
                op = ">"
                start_time = round_timestamp(int(start_time_micros), up=False)
            else:
                raise ValueError(
                    f"Unsupported comparator {comparison.comparator} for cursor"
                )

            # format the cursor query
            snippet = f"((start_time:{op}{start_time}) AND ((start_time_micros:{op}{start_time_micros}) OR (start_time_micros:{start_time_micros} AND id_sort:{op}{id_sort})))"
            return QuickwitResult(query_str=snippet)

        # If it's specifically JSON_KEY / JSON_VALUE (e.g. input_key or input_value),
        # that means the user wrote a "standalone" usage outside AND
        if self.type in (QuickwitType.JSON_KEY, QuickwitType.JSON_VALUE):
            prefix_k = _check_if_json_key(comparison)
            if prefix_k is not None:
                k_str = self.parse_value_for_type(comparison.value)
                snippet = _make_json_key_existence_snippet(prefix_k, k_str)
                return QuickwitResult(query_str=snippet)

            prefix_v = _check_if_json_value(comparison)
            if prefix_v is not None:
                v_str = self.parse_value_for_type(comparison.value)
                snippet = _make_json_value_existence_snippet(prefix_v, v_str)
                return QuickwitResult(query_str=snippet)

        # handle sub-key, eg "inputs_json.some_key"
        # verified in visit_comparison
        parts = comparison.attribute.split(".", 1)
        sub_key = parts[1] if len(parts) > 1 else None

        base_field = self.quickwit_field or self.name
        field_expr = f"{base_field}.{sub_key}" if sub_key else base_field

        parsed_value = self.parse_value_for_type(comparison.value)

        # if empty, effectively discard this filter
        if not parsed_value:
            return QuickwitResult(query_str="")

        # final escape for Quickwit syntax
        final_value_str = format_term(parsed_value)

        snippet = quickwit_comparison_query(
            comparison=comparison,
            field_expr=field_expr,
            value_str=final_value_str,
        )
        return QuickwitResult(query_str=snippet)


def _escape_quotes(s: str) -> str:
    """Escape double quotes in a string for Quickwit queries."""
    return s.replace('"', '\\"')


def _escape_colon(s: str) -> str:
    """Escape : in a string for Quickwit queries."""
    return s.replace(":", "\\:")


# TODO: probably need to sanitize input
def format_term(term: str) -> str:
    """Format a term for Quickwit search, handling spaces, quotes and wildcards."""
    if "*" in term or "?" in term:
        return term
    elif " " in term or '"' in term:
        return f'"{_escape_quotes(term)}"'
    elif ":" in term:
        return _escape_colon(term)
    else:
        return term


def _flatten_and_args(operation: Operation) -> List[Any]:
    """
    Flatten nested AND operations so that we can easily gather
    all direct comparisons for key-value pairing at once.

    e.g. AND(a, AND(b,c), d) => [a, b, c, d]
    """
    result = []
    for arg in operation.arguments:
        if isinstance(arg, Operation) and arg.operator == Operator.AND:
            result.extend(_flatten_and_args(arg))
        else:
            result.append(arg)
    return result


def _check_if_json_key(comp: Comparison) -> str | None:
    if comp.comparator != Comparator.EQ or not comp.attribute:
        return None
    if comp.attribute == "input_key":
        return "input"
    if comp.attribute == "output_key":
        return "output"
    if comp.attribute == "extra_key":
        return "extra"
    return None


def _check_if_json_value(comp: Comparison) -> str | None:
    if comp.comparator != Comparator.EQ or not comp.attribute:
        return None
    if comp.attribute == "input_value":
        return "input"
    if comp.attribute == "output_value":
        return "output"
    if comp.attribute == "extra_value":
        return "extra"
    return None


def _make_json_path_comparison(prefix: str, key: str, val: str) -> Comparison:
    """
    Convert eq(input_key, 'foo.bar') + eq(input_value, 'someVal')
    --> eq(inputs_json.foo.bar, 'someVal').
    """
    attribute = (
        f"{prefix}s_json.{key}" if prefix in ("input", "output") else f"{prefix}.{key}"
    )
    return Comparison(
        attribute=attribute,
        comparator=Comparator.EQ,
        value=val,
    )


def _make_json_key_existence_snippet(prefix: str, key: Any) -> str:
    k = str(key)
    if prefix in ("input", "output"):
        return f"{prefix}s.{k}:*"
    else:
        return f"{prefix}.{k}:*"


def _make_json_value_existence_snippet(prefix: str, val: Any) -> str:
    v = str(val)
    if prefix in ("input", "output"):
        return f"{prefix}s_flat:{format_term(v)}"
    else:
        return f"{prefix}_flat:{format_term(v)}"


class QuickwitVisitor(Visitor):
    attributes: List[QuickwitAttributeInfo]

    def visit_operation(self, operation: Operation) -> QuickwitResult:
        op = operation.operator

        if op == Operator.AND:
            # flatten nested AND nodes s.t. we can find multiple key/value pairs
            # AND[a, b, AND[c, d], OR[e, f, AND[g, h]]] => AND[a, b, c, d, OR[e, f, AND[g, h]]]
            flattened_args = _flatten_and_args(operation)

            prefix_to_keys: dict[str, list] = {"input": [], "output": [], "extra": []}
            prefix_to_values: dict[str, list] = {"input": [], "output": [], "extra": []}
            leftover_snippets = []

            for child in flattened_args:
                if isinstance(child, Comparison):
                    prefix_k = _check_if_json_key(child)
                    if prefix_k:
                        prefix_to_keys[prefix_k].append(child.value)
                        continue

                    prefix_v = _check_if_json_value(child)
                    if prefix_v:
                        prefix_to_values[prefix_v].append(child.value)
                        continue

                    # else it's a comparison on a non-key/value attribute
                    sub_result = child.accept(self)
                    if sub_result["query_str"]:
                        leftover_snippets.append(sub_result["query_str"])

                elif isinstance(child, Operation) and child.operator not in [
                    Operator.AND
                ]:
                    # no flattening for OR/NOT, recurse -> store its snippet
                    sub_result = child.accept(self)
                    if sub_result["query_str"]:
                        leftover_snippets.append(sub_result["query_str"])
                else:
                    # Should not happen, but just in case
                    sub_result = child.accept(self)
                    if sub_result["query_str"]:
                        leftover_snippets.append(sub_result["query_str"])

            # for each prefix, pair up keys/values
            for prefix in ("input", "output", "extra"):
                keys = prefix_to_keys[prefix]
                vals = prefix_to_values[prefix]

                # produce pairs
                keys.reverse()
                vals.reverse()
                while keys and vals:
                    k = str(keys.pop())
                    v = str(vals.pop())
                    # make a json path comparison
                    comp = _make_json_path_comparison(prefix, k, v)
                    sub_result = comp.accept(self)
                    leftover_snippets.append(sub_result["query_str"])

                # leftover unpaired keys => "inputs.foo.bar:*" or "extra.someKey:*"
                # this is equivalent to checking for the presence of the key in the JSON
                while keys:
                    snippet = _make_json_key_existence_snippet(prefix, keys.pop())
                    leftover_snippets.append(snippet)

                # leftover unpaired values => "inputs_flat:theVal"
                # this is equivalent to checking for the presence of the value in the JSON
                while vals:
                    snippet = _make_json_value_existence_snippet(prefix, vals.pop())
                    leftover_snippets.append(snippet)

            if not leftover_snippets:
                return QuickwitResult(query_str="")
            combined = " AND ".join(f"({s})" for s in leftover_snippets)
            return QuickwitResult(query_str=combined)

        elif op in (Operator.OR, Operator.NOT):
            sub_snippets = []
            for arg in operation.arguments:
                sub_result = arg.accept(self)
                if sub_result["query_str"]:
                    sub_snippets.append(f"({sub_result['query_str']})")

            if not sub_snippets:
                return QuickwitResult(query_str="")

            combined = " OR ".join(sub_snippets)

            if op == Operator.NOT:
                return QuickwitResult(query_str=f"NOT ({combined})")
            else:  # op == Operator.OR
                return QuickwitResult(query_str=combined)

        else:
            raise ValueError(f"Unsupported operator: {op}")

    def visit_comparison(self, comparison: Comparison) -> QuickwitResult:
        """
        For a comparison, find the attribute info, build the QuickWit snippet
        based on the comparator.
        """
        if comparison.attribute is None:
            if comparison.comparator == Comparator.SEARCH:
                val_str = format_term(comparison.value)
                return (
                    QuickwitResult(
                        query_str=f"(inputs_flat:{val_str} OR outputs_flat:{val_str} OR error:{val_str})"
                    )
                    if val_str
                    else QuickwitResult(query_str="")
                )
            else:
                raise ValueError(
                    "Comparison attribute cannot be None unless comparator=SEARCH"
                )

        # if dot-notation is used, ensure base is inputs_json, outputs_json, or extra, etc.
        attr_delimited = comparison.attribute.split(".")
        if len(attr_delimited) > 1 and attr_delimited[0] not in [
            "inputs_json",
            "outputs_json",
            "extra",
        ]:
            raise ValueError(
                f"Attribute {comparison.attribute} should be one of the JSON types if using dot notation"
            )

        attr_name = attr_delimited[0]
        for info in self.attributes:
            if info.name == attr_name:
                return info.translate(comparison)

        raise ValueError(f"Attribute '{attr_name}' not accepted.")

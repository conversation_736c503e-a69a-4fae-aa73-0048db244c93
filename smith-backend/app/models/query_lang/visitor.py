from abc import ABC, abstractmethod
from typing import Any

from pydantic import BaseModel

from app.models.query_lang.parse import Comparison, Operation


class Visitor(BaseModel, ABC):
    """Defines interface for IR translation using visitor pattern."""

    @abstractmethod
    def visit_operation(self, operation: Operation) -> Any:
        """Translate an Operation."""

    @abstractmethod
    def visit_comparison(self, comparison: Comparison) -> Any:
        """Translate a Comparison."""

token_counts_pre_agg = """
SELECT
    id,
    session_id,
    sum(total_tokens) as total_tokens,
    sum(prompt_tokens) as prompt_tokens,
    sum(completion_tokens) as completion_tokens,
    sum(total_cost) as total_cost,
    sum(prompt_cost) as prompt_cost,
    sum(completion_cost) as completion_cost,
    min(start_time) as min_start_time,
    min(first_token_time) as first_token_time
FROM runs_token_counts FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time} AND runs_token_counts.total_tokens < 4000000000
GROUP BY session_id, id"""

runs_stats_projection = """
    uniq(id) as run_count,
    max(start_time) as last_run_start_time,
    arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', start_time, end_time, 'UTC'))) as latency_ptiles,
    if(run_count = 0, 0, uniqIf(id, status = 'error') / run_count) as error_rate
    """

runs_stats_projection_agg = """,
    countMerge(run_count) as run_count,
    maxMerge(last_run_start_time) as last_run_start_time,
    arrayMap(x -> x / 1000, [quantileMerge(0.5)(latency_ptiles), quantileMerge(0.99)(latency_ptiles)]) as latency_ptiles,
    if(run_count = 0, 0, countMerge(error_run_count) / run_count) as error_rate
"""

runs_avg_latency_projection = """
    uniq(id) as run_count,
    if(run_count = 0, NULL, sum(date_diff('s', start_time, end_time, 'UTC')) / run_count) as latency_avg
"""


runs_facets_projection = """,
    topK(20)(name) as top_10_name,
    topK(5)(status) as top_10_status,
    topK(10)(run_type) as top_10_run_type,
    topKArray(20)(tags) as top_10_tags"""

runs_facets_projection_agg = """,
    topKMerge(20)(topk_run_names) as top_10_name,
    topKMerge(5)(topk_run_status) as top_10_status,
    topKMerge(10)(topk_run_types) as top_10_run_type,
    topKMerge(20)(topk_run_tags) as top_10_tags"""

token_count_stats_projection = """
    uniq(id) as token_run_count,
    arrayMap(x -> x / 1000, quantiles(0.5, 0.99)(date_diff('ms', min_start_time, first_token_time, 'UTC'))) as first_token_ptiles,
    if(token_run_count = 0, 0, toUInt64(sum(total_tokens))) as total_tokens,
    if(token_run_count = 0, 0, toUInt64(sum(prompt_tokens))) as prompt_tokens,
    if(token_run_count = 0, 0, toUInt64(sum(completion_tokens))) as completion_tokens,
    if(token_run_count = 0, 0, sum(total_cost)) as total_cost,
    if(token_run_count = 0, 0, sum(prompt_cost)) as prompt_cost,
    if(token_run_count = 0, 0, sum(completion_cost)) as completion_cost,
    if(token_run_count = 0, NULL, uniqIf(id, first_token_time is not null) / token_run_count) as streaming_rate
    """

feedback_stats_projection = """
    mapKeys(uniqMap(map(key, id))) as feedback_keys,
    mapValues(avgMap(map(key, COALESCE(
        CASE
            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
            ELSE NULL
        END,
        score
    )))) as feedback_avgs,
    mapValues(uniqMap(map(key, id))) as feedback_counts,
    mapValues(stddevPopMap(map(key, COALESCE(
        CASE
            WHEN JSONHas(correction, 'score') THEN CAST(JSONExtract(correction, 'score', 'Float32') AS Decimal(9, 4))
            ELSE NULL
        END,
        score
    )))) as feedback_stdevs,
    mapKeys(countMap(map(key || '|~|' || value, value))) as feedback_value_keys,
    mapValues(countMap(map(key || '|~|' || value, value))) as feedback_value_counts,
    mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as feedback_errors"""

session_feedback_stats_projection = """
    mapKeys(uniqMap(map(key, id))) as session_feedback_keys,
    mapValues(avgMap(map(key, score))) as session_feedback_avgs,
    mapValues(stddevPopMap(map(key, score))) as session_feedback_stdevs,
    mapValues(sumMap(map(key, JSONExtractBool(extra, 'error')))) as session_feedback_errors,
    mapValues(uniqMap(map(key, id))) as session_feedback_counts"""

feedback_facets_projection = """,
    topK(20)(key) as top_10_feedback_key,
    topK(20)(if(score is null or value <> '{{}}', null, key || ' == ' || toString(coalesce(score, 0)))) as top_10_feedback_key_score,
    topK(20)(if(value = '{{}}', null, key || ' == ' || toString(coalesce(value, 0)))) as top_10_feedback_key_value,
    topK(5)(JSONExtract(feedback_source, 'type', 'String')) as top_10_feedback_source"""

metadata_facets_projection = """
    topK(20)(key) as top_10_metadata_key,
    topK(20)(key || ' == ' || value) as top_10_metadata_key_value"""

metadata_facets_projection_agg = """
    topKMerge(20)(topk_metadata_keys) as top_10_metadata_key,
    topKMerge(20)(topk_metadata_key_values) as top_10_metadata_key_value"""


input_kv_facets_projection = """
    topK(20)(key) as top_10_input_key,
    topK(20)(key || ' == ' || value) as top_10_input_key_value"""

input_kv_facets_projection_agg = """
    topKMerge(20)(topk_input_keys) as top_10_input_key,
    topKMerge(20)(topk_input_key_values) as top_10_input_key_value"""


output_kv_facets_projection = """
    topK(20)(key) as top_10_output_key,
    topK(20)(key || ' == ' || value) as top_10_output_key_value"""

output_kv_facets_projection_agg = """
    topKMerge(20)(topk_output_keys) as top_10_output_key,
    topKMerge(20)(topk_output_key_values) as top_10_output_key_value"""


token_count_stats_projection_agg = """
    countMerge(llm_run_count) as token_run_count,
    arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
    sumMerge(total_tokens) as total_tokens,
    sumMerge(prompt_tokens) as prompt_tokens,
    sumMerge(completion_tokens) as completion_tokens,
    sumMerge(total_cost) as total_cost,
    sumMerge(prompt_cost) as prompt_cost,
    sumMerge(completion_cost) as completion_cost,
    countMerge(streaming_run_count) / token_run_count as streaming_rate,
    quantileMerge(0.5)(median_tokens) as median_tokens
"""


sql_stats_for_session = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + """
FROM runs FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
    SELECT session_id,"""
    + feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id != '00000000-0000-0000-0000-000000000000' AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
    GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
SELECT session_id,"""
    + token_count_stats_projection
    + """
FROM token_counts
GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)

sql_stats_w_facets_for_session = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + runs_facets_projection
    + """
FROM runs
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection
    + """
from runs_metadata_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection
    + """
from runs_inputs_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection
    + """
from runs_outputs_kv
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,"""
    + token_count_stats_projection
    + """
    FROM token_counts
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)

sql_stats_for_session_agg = (
    """
WITH

run_stats as (
SELECT session_id """
    + runs_stats_projection_agg
    + """
FROM runs_sessions_agg_hourly
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
GROUP BY session_id
),

feedback_stats as (
    SELECT session_id,"""
    + feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id != '00000000-0000-0000-0000-000000000000' AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
    GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

token_count_stats as (
    SELECT session_id,
        countMerge(llm_run_count) as token_run_count,
        arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
        sumMerge(total_tokens) as total_tokens,
        sumMerge(prompt_tokens) as prompt_tokens,
        sumMerge(completion_tokens) as completion_tokens,
        sumMerge(total_cost) as total_cost,
        sumMerge(prompt_cost) as prompt_cost,
        sumMerge(completion_cost) as completion_cost,
        countMerge(streaming_run_count) / token_run_count as streaming_rate
    FROM runs_sessions_agg_hourly
    WHERE tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)


# use topk aggregated merge tree for computation
sql_stats_w_facets_for_session_agg = (
    """
WITH

run_stats as (
SELECT session_id"""
    + runs_stats_projection_agg
    + runs_facets_projection_agg
    + """
FROM runs_topk_hourly_agg
JOIN runs_sessions_agg_hourly ON runs_topk_hourly_agg.session_id = runs_sessions_agg_hourly.session_id
WHERE is_root = true AND runs_topk_hourly_agg.tenant_id = {tenant_id} AND runs_topk_hourly_agg.session_id IN {session_ids} AND runs_topk_hourly_agg.hour >= toStartOfHour(toDateTime64({min_start_time}, 6))

GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,
        countMerge(llm_run_count) as token_run_count,
        arrayMap(x -> x / 1000, [quantileMerge(0.5)(first_token_ptiles), quantileMerge(0.99)(first_token_ptiles)]) as first_token_ptiles,
        sumMerge(total_tokens) as total_tokens,
        sumMerge(prompt_tokens) as prompt_tokens,
        sumMerge(completion_tokens) as completion_tokens,
        sumMerge(total_cost) as total_cost,
        sumMerge(prompt_cost) as prompt_cost,
        sumMerge(completion_cost) as completion_cost,
        countMerge(streaming_run_count) / token_run_count as streaming_rate
    FROM runs_sessions_agg_hourly
    WHERE tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)


sql_stats_w_facets_for_session_without_sessions_agg = (
    """
WITH

run_stats as (
SELECT session_id,"""
    + runs_stats_projection
    + runs_facets_projection
    + """
FROM runs
WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND start_time >= {min_start_time}
GROUP BY session_id
),

feedback_stats as (
SELECT session_id,"""
    + feedback_stats_projection
    + feedback_facets_projection
    + """
FROM feedbacks_rmt FINAL
WHERE is_root = true AND tenant_id = {tenant_id} AND run_id != '00000000-0000-0000-0000-000000000000' AND session_id IN {session_ids} AND start_time >= {min_start_time} AND comparative_experiment_id is NULL
GROUP BY session_id
),

session_feedback_stats as (
    SELECT session_id,"""
    + session_feedback_stats_projection
    + """
    FROM feedbacks_rmt FINAL
    WHERE is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND run_id = '00000000-0000-0000-0000-000000000000' AND comparative_experiment_id is NULL
    GROUP BY session_id
),

metadata_stats as (
select session_id,"""
    + metadata_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

input_kv_stats as (
select session_id,"""
    + input_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

output_kv_stats as (
select session_id,"""
    + output_kv_facets_projection_agg
    + """
from runs_topk_hourly_agg
where is_root = true AND tenant_id = {tenant_id} AND session_id IN {session_ids} AND hour >= toStartOfHour(toDateTime64({min_start_time}, 6))
group by session_id
),

token_counts as (
    """
    + token_counts_pre_agg
    + """
),

token_count_stats as (
    SELECT session_id,"""
    + token_count_stats_projection
    + """
    FROM token_counts
    GROUP BY session_id
)

SELECT
    run_stats.session_id as session_id,
    run_stats.run_count as run_count,
    run_stats.* EXCEPT (session_id, run_count),
    feedback_stats.* EXCEPT (session_id),
    session_feedback_stats.* EXCEPT (session_id),
    metadata_stats.* EXCEPT (session_id),
    input_kv_stats.* EXCEPT (session_id),
    output_kv_stats.* EXCEPT (session_id),
    token_count_stats.* EXCEPT (session_id)
FROM run_stats
LEFT JOIN feedback_stats ON run_stats.session_id = feedback_stats.session_id
LEFT JOIN session_feedback_stats ON run_stats.session_id = session_feedback_stats.session_id
LEFT JOIN metadata_stats ON run_stats.session_id = metadata_stats.session_id
LEFT JOIN input_kv_stats ON run_stats.session_id = input_kv_stats.session_id
LEFT JOIN output_kv_stats ON run_stats.session_id = output_kv_stats.session_id
LEFT JOIN token_count_stats ON run_stats.session_id = token_count_stats.session_id
SETTINGS multiple_joins_try_to_keep_original_names = 1
"""
)

from typing import Any


def get_first_path(x: dict, first: list[str], *others: list[str]) -> Any:
    """Get the first matching path in a dictionary."""
    for path in (first, *others):
        try:
            v = x
            for k in path:
                v = v[k]
        except (<PERSON><PERSON><PERSON><PERSON>, TypeError):
            continue
        else:
            return v
    return None

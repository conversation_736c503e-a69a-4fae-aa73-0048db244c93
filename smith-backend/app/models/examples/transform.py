import copy
import re
from typing import Any, Callable, Tuple

import structlog
from fastapi import HTT<PERSON>Exception
from langchain_core.load import load
from langchain_core.messages.utils import convert_to_openai_messages
from langchain_core.utils.function_calling import (
    convert_to_openai_tool,
)
from lc_logging import trace as trace_utils
from referencing import Registry

from app import schemas
from app.schemas import DatasetTransformationType as DTT

logger = structlog.get_logger(__name__)


class TransformationError(Exception):
    def __init__(self, message: str):
        self.message = message
        super().__init__(self.message)

    def __str__(self):
        return self.message


def validate_transformations_format(
    transformations: list[schemas.DatasetTransformation],
):
    """
    Validates any schema independent conditions on transformations.
    """
    for t in transformations:
        if t.path is None or len(t.path) == 0 or t.path[0] not in ["inputs", "outputs"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid path for transformation: '{t.path}' must start with 'inputs' or 'outputs'.",
            )

        if t.transformation_type == DTT.remove_system_messages:
            if t.path[0] != "inputs":
                raise HTTPException(
                    status_code=400,
                    detail="Filters of type 'remove_system_messages' must be applied to inputs.",
                )

        elif t.transformation_type == DTT.extract_tools_from_run:
            raise HTTPException(
                status_code=422,
                detail="extract_tools_from_run transformation is no longer supported.",
            )


def validate_transformations_for_schema(
    field: str,
    schema: dict,
    transformations: list[schemas.DatasetTransformation],
):
    for t in transformations or []:
        if t.path is None or len(t.path) == 0 or t.path[0] not in ["inputs", "outputs"]:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid path for transformation: '{t.path}' must start with 'inputs' or 'outputs'.",
            )
        if t.path[0] == field:
            schema_at_path = _match_path(t.path[1:], schema)
            if t.transformation_type == DTT.remove_system_messages:
                if not _is_message_array_schema(schema_at_path):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid input transformation path: {t.path} for remove_system_messages transformation. Expected a list of messages.",
                    )
            elif t.transformation_type == DTT.convert_to_openai_tool:
                if not (_is_tool_def_array_schema(schema_at_path)):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid input transformation path: {t.path} for convert_to_openai_tool transformation. Expected tool or a list of tools.",
                    )
                if not len(t.path) == 2 and t.path[0] == "inputs":
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid input transformation path: {t.path} for convert_to_openai_tool transformation. Tool extraction only supported for top level fields.",
                    )
            elif t.transformation_type == DTT.convert_to_openai_message:
                if not (
                    _is_message_array_schema(schema_at_path)
                    or _is_message_schema(schema_at_path)
                ):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid input transformation path: {t.path} for convert_to_openai_message transformation. Expected message or a list of messages.",
                    )
            elif t.transformation_type == DTT.remove_extra_fields:
                if not _is_object_type(schema_at_path):
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid input transformation path: {t.path} for remove_extra_fields transformation. Expected an object.",
                    )
            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid input transformation type: {t.transformation_type}",
                )


@trace_utils.wrap(
    "validation",
    "apply_on_validation_error",
    swallow_exceptions=lambda e: isinstance(e, TransformationError),
)
def apply_on_validation_error(
    path: list[str | int],
    field: str,
    example: dict,
    schema: dict,
    registry: Registry,
    transformations: list[schemas.DatasetTransformation],
) -> dict | None:
    if path and isinstance(path[-1], int):
        path = path[:-1]

    normalized_path = [field] + path
    for i in range(len(normalized_path)):
        if isinstance(normalized_path[i], int):
            normalized_path[i] = "*"

    error_schema = _get_schema_at_error_path(path, schema, registry)
    # Find matching coercion based transformation
    for transform in transformations:
        matches_path = normalized_path == transform.path

        if transform.transformation_type == DTT.convert_to_openai_message:
            if matches_path and _is_message_schema_error(error_schema):
                error_key, error_subexample = _get_errored_subexample(example, path)

                if error_key is not None:
                    converted = _convert_to_openai_messages(error_subexample[error_key])
                    error_subexample[error_key] = converted
                else:
                    # Implies we're at the root of the example
                    example = _convert_to_openai_messages(example)

                return example

            elif normalized_path == [field] and len(transform.path) == 2:
                error_key, error_subexample = _get_errored_subexample(example, path)

                # If we don't find a valid message for the transform, but we have a validation
                # error at the root of the example, we do a fancy conversion for chat model
                # output schemas to convert them to OpenAI format
                message_subfields = _subfields_with_messages(error_schema)
                if len(message_subfields) > 1:
                    raise TransformationError(
                        "Coercion failed, cannot support multiple message types in a single example"
                    )
                elif len(message_subfields) == 1:
                    messages_key = message_subfields[0]
                    error_subexample[messages_key] = _convert_to_openai_messages(
                        error_subexample[error_key]
                        if error_key is not None
                        else error_subexample
                    )

                    return example

        elif transform.transformation_type == DTT.convert_to_openai_tool:
            if normalized_path == transform.path and _is_tool_schema_error(
                error_schema
            ):
                error_key, error_subexample = _get_errored_subexample(example, path)
                if error_key is not None:
                    tool_body = error_subexample[error_key]
                    if tool_body is None:
                        del error_subexample[error_key]
                    else:
                        converted = _convert_to_openai_tool(tool_body)
                        error_subexample[error_key] = converted
                else:
                    # Implies we're at the root of the example
                    example = _convert_to_openai_tool(example)

                return example

    return None


@trace_utils.wrap(
    "validation",
    "apply_on_validation_start",
    swallow_exceptions=lambda e: isinstance(e, TransformationError),
)
def apply_on_validation_start(
    example: dict,
    extra: dict,
    transformations: list[schemas.DatasetTransformation],
):
    for t in transformations:
        if t.transformation_type == DTT.convert_to_openai_tool:
            # We know that the transformation path is at the top level of the
            # example's input, and therefore we only need to apply to one path
            _extract_tools_from_run(example, extra, t)

    return example


@trace_utils.wrap(
    "validation",
    "apply_on_validation_success",
    swallow_exceptions=lambda e: isinstance(e, TransformationError),
)
def apply_on_validation_success(
    example: dict,
    schema: dict,
    transformations: list[schemas.DatasetTransformation],
) -> dict:
    for transformation in transformations:
        matching_paths = _find_matching_paths(example, transformation.path[1:])
        schema_at_path = _match_path(transformation.path[1:], schema)

        if transformation.transformation_type == DTT.remove_system_messages:
            example = _apply_modifications(
                example, matching_paths, _remove_system_messages
            )
        elif transformation.transformation_type == DTT.remove_extra_fields:
            example = _apply_modifications(
                example,
                matching_paths,
                lambda x: _remove_extra_fields(x, schema_at_path),
            )

    return example


#################################################################
#             TRANSFORMATION HELPER FUNCTIONS                   #
#################################################################


def _apply_modifications(
    example: dict,
    paths: list[list[str | int]],
    transformation_func: Callable[[Any], Any],
) -> dict:
    modified_example = copy.deepcopy(example)
    for path in paths:
        if len(path) == 0:
            modified_example = transformation_func(modified_example)
            return modified_example

        # iterate to the right section of the example to modify
        example_subsection = modified_example
        for i in range(len(path) - 1):
            example_subsection = example_subsection[path[i]]

        # overwrite the example with the specified transforming expression
        example_subsection[path[-1]] = transformation_func(example_subsection[path[-1]])

    return modified_example


@trace_utils.wrap("validation", "extract_tools_from_run")
def _extract_tools_from_run(
    example: dict, extra: dict, transformation: schemas.DatasetTransformation
):
    if example.get(transformation.path[1], None) is None:
        tools_from_run = (
            extra.get("__source_run_extra", {})
            .get("invocation_params", {})
            .get("tools")
        )
        if tools_from_run:
            example[transformation.path[1]] = tools_from_run


@trace_utils.wrap(
    "validation",
    "remove_extra_fields",
    swallow_exceptions=lambda e: isinstance(e, TransformationError),
)
def _remove_extra_fields(fields, schema):
    # Apply array element transformation to each element in the array
    if isinstance(fields, list):
        element_schema = schema.get("items", {})
        return [_remove_extra_fields(field, element_schema) for field in fields]

    if not isinstance(fields, dict):
        raise TransformationError(
            f"Tried to remove extra fields on a non object type {fields=}"
        )

    schema_fields = schema.get("properties", {})
    schema_keys = set(schema_fields.keys())
    fields_keys = set(fields.keys())

    for key in fields_keys - schema_keys:
        del fields[key]

    return fields


@trace_utils.wrap("validation", "remove_system_messages")
def _remove_system_messages(messages: list[dict]) -> list[dict]:
    return [msg for msg in messages if msg.get("role") != "system"]


@trace_utils.wrap(
    "validation",
    "convert_to_openai_messages",
    swallow_exceptions=lambda e: isinstance(e, TransformationError),
)
def _convert_to_openai_messages(item: Any) -> Any:
    """
    Coerce the item to OpenAI message format.

    For known tracing formats (LangChain, Wrap OpenAI), this will do extra preprocessing
    to remove the outer layers of the trace and convert to OpenAI format.
    """

    # Step 1: preprocess the item to handle common langsmith formats
    if (
        # Wrap Open AI Trace format
        "output" in item
        and "choices" in item["output"]
        and isinstance(item["output"]["choices"], list)
        and len(item["output"]["choices"]) > 0
        and "message" in item["output"]["choices"][0]
    ):
        logger.info("coercing wrap openai messages to openai format")
        item_to_convert = item["output"]["choices"][0]["message"]

        del item["output"]
        item = item_to_convert
    elif (
        # Direct Open AI Trace format
        "choices" in item
        and isinstance(item["choices"], list)
        and len(item["choices"]) > 0
        and "message" in item["choices"][0]
    ):
        logger.info("coercing openai messages to openai format")
        item_to_convert = item["choices"][0]["message"]

        del item["choices"]
        item = item_to_convert
    elif (
        # LangChain Trace Format
        "generations" in item and isinstance(item["generations"], list)
    ):
        logger.info("detected langchain format, checking invoke or stream")
        if (
            len(item["generations"]) > 0
            and isinstance(item["generations"][0], list)
            and len(item["generations"][0]) > 0
            and all("message" in generation for generation in item["generations"][0])
        ):
            logger.info("coercing langchain invoke style trace output to openai format")
            item_to_convert = [
                generations.get("message") for generations in item["generations"][0]
            ]
        elif (
            len(item["generations"]) > 0
            and isinstance(item["generations"][0], dict)
            and "message" in item["generations"][0]
        ):
            logger.info("coercing langchain stream style trace output to openai format")
            item_to_convert = [
                generations.get("message") for generations in item["generations"]
            ]
        else:
            logger.warn("unrecognized generations format for langchain trace")

        if item_to_convert:
            del item["generations"]
            item = item_to_convert
            if len(item) == 1:
                item = item[0]

    else:
        logger.info("coercing raw messages to openai format")

    # Step 2: convert the item to OpenAI format
    try:
        if isinstance(item, list) and len(item) > 0 and isinstance(item[0], list):
            return convert_to_openai_messages(
                load([i for sublist in item for i in sublist])
            )

        return convert_to_openai_messages(load(item))
    except ValueError as e:
        logger.error("Error converting to openai messages", error=e)
        raise TransformationError(
            "An error occurred coercing messages to openai format: " + str(e)
        )


@trace_utils.wrap("validation", "convert_to_openai_tool")
def _convert_to_openai_tool(item: Any) -> Any:
    # Tool conversion expects a single tool rather than multiple
    try:
        if isinstance(item, list):
            return [convert_to_openai_tool(load(i)) for i in item]

        return convert_to_openai_tool(load(item))
    except ValueError as e:
        logger.error("Error converting to openai tool", error=e)
        raise HTTPException(
            status_code=400,
            detail="An error occurred coercing tool to openai format: " + str(e),
        )


################################################################
#             SCHEMA HELPER FUNCTIONS                          #
################################################################


def _is_tool_schema_error(subschema: dict) -> bool:
    # Check if the subschema is a tool object or a list of tools
    return _is_tool_def_schema(subschema) or _is_tool_def_array_schema(subschema)


def _is_object_type(schema: dict) -> bool:
    obj_type = schema.get("type")
    return obj_type or (
        isinstance(obj_type, str)
        and obj_type == "object"
        or isinstance(obj_type, list)
        and "object" in obj_type
    )


def _is_tool_def_schema(schema: dict) -> bool:
    ref = schema.get("$ref", "")
    pattern = r"/public/schemas/v\d+/tooldef\.json$"
    return bool(re.search(pattern, ref))


def _is_tool_def_array_schema(schema: dict) -> bool:
    return (
        schema.get("type") == "array"
        and isinstance(schema.get("items"), dict)
        and _is_tool_def_schema(schema["items"])
    )


def _is_message_schema_error(subschema: dict) -> bool:
    # Check if the subschema is a message object or a list of messages
    return _is_message_schema(subschema) or _is_message_array_schema(subschema)


def _subfields_with_messages(subschema: dict) -> list[str]:
    properties = subschema.get("properties", [])

    return [p for p in properties if _is_message_schema_error(properties[p])]


def _is_message_schema(schema: dict) -> bool:
    ref = schema.get("$ref", "")
    pattern = r"/public/schemas/v\d+/message\.json$"
    return bool(re.search(pattern, ref))


def _is_message_array_schema(schema: dict) -> bool:
    return (
        schema.get("type") == "array"
        and isinstance(schema.get("items"), dict)
        and _is_message_schema(schema["items"])
    )


#################################################################
#             PATH OPERATIONS FOR TRANSFORMATIONS               #
#################################################################


def _get_errored_subexample(example: dict, error_path: list) -> Tuple[str | None, dict]:
    # Navigate to the specific item using the error path
    current = example
    for key in error_path[:-1]:
        current = current[key]

    item_key = error_path[-1] if error_path else None
    return item_key, current


def _find_matching_paths(
    data: dict | list,
    transformation_path: list[str],
    current_path: list[str | int] | None = None,
) -> list[list[str | int]]:
    """Find all paths in the data that match the transformation path.

    If the transformation path contains a *, then paths to all values in
    the corresponding array will be matched.
    """
    current_path = current_path or []
    if not transformation_path:
        return [current_path]

    key, *next_path = transformation_path

    if isinstance(data, dict):
        if key in data:
            return _find_matching_paths(data[key], next_path, current_path + [key])
        else:
            return []

    elif isinstance(data, list):
        if key == "*":
            paths = []
            for i, item in enumerate(data):
                paths.extend(_find_matching_paths(item, next_path, current_path + [i]))
            return paths
        else:
            detail = (
                "Path for transformation was malformed at key "
                f"{key} example had a list at that location but * "
                "notation was not used"
            )
            raise HTTPException(status_code=400, detail=detail)
    else:
        detail = (
            f"Path for transformation was malformed at key {key} "
            "example was not a list or dict at that location"
        )
        raise HTTPException(status_code=400, detail=detail)


def _match_path(path: list[str], schema: dict) -> dict:
    schema_at_curr_path = schema
    if len(path) == 0:
        # This should only happen if calling on the inputs/outputs fields
        return schema_at_curr_path
    if len(path) == 1 and path[0] == "*" and schema_at_curr_path.get("type") == "array":
        return schema_at_curr_path

    # Validate that the first element of the path is a valid key in the schema
    curr_element = path[0]
    if curr_element == "*":
        if schema_at_curr_path.get("type") != "array":
            if "*" in schema_at_curr_path.get("properties", {}):
                raise HTTPException(
                    status_code=400,
                    detail="transformation error: '*' is a reserved path variable representing arrays in json schema. LangSmith does not support data transforming on json properties called '*'.",
                )
            else:
                raise HTTPException(
                    status_code=400,
                    detail="transformation error: expected array type to match wildcard '*'",
                )

        next_schema = schema_at_curr_path.get("items", {})

    else:
        next_schema = schema_at_curr_path.get("properties", {}).get(curr_element)

    if next_schema is None:
        raise HTTPException(
            status_code=400,
            detail="transformation error: path not found in schema.",
        )

    # If the first element is valid, either traverse the schema,
    # or return the schema at the current path if the path is fully matched
    if len(path) == 1:
        return next_schema
    else:
        return _match_path(path[1:], next_schema)


def _get_schema_at_error_path(path: list, schema: dict, registry: Registry) -> dict:
    # Traverse the schema based on the error path
    subschema = schema
    for key in path:
        if not isinstance(subschema, dict):
            raise HTTPException(
                status_code=400,
                detail="Malformed schema, schema should always be a dict",
            )

        # If it's a reference, resolve it
        if "$ref" in subschema:
            subschema = registry.get(subschema["$ref"]).contents

        if isinstance(key, str):
            subschema = subschema.get("properties", {}).get(key, {})
        elif isinstance(key, int):
            subschema = subschema.get("items", {})
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unexpected error key type {type(key)}, must be a string or integer",
            )

    return subschema

# A helper function to parse SSE events from raw bytes
import structlog

from app.models.datasets.types import StreamPart

logger = structlog.get_logger(__name__)


def get_completed_runs_from_stream_event(data: StreamPart) -> list[dict]:
    parsed_runs = []
    if data.data and data.event == "data":
        patch = data.data.get("patch", [])
        if patch and isinstance(patch, list):
            for p in patch:
                if (
                    p.get("op") == "replace"
                    and "value" in p
                    and "end_time" in p["value"]
                    and p["value"]["end_time"]
                ):
                    parsed_runs.append(p["value"])
        elif not isinstance(patch, list):
            logger.warn(
                "Unexpected patch. Patch should be a list.", patch=patch, data=data
            )
    return parsed_runs

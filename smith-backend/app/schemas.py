from __future__ import annotations

import datetime
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum
from typing import (
    Annotated,
    Any,
    Dict,
    List,
    Literal,
    Optional,
    Self,
    Union,
    cast,
)
from uuid import UUID, uuid4

import structlog
from fastapi import HTTPException, Query
from lc_config.env import ENV_FILE_PATH
from lc_config.tenant_config import OrganizationConfig, TenantConfig
from pydantic import (
    AnyHttpUrl,
    ConfigDict,
    Field,
    RootModel,
    ValidationInfo,
    field_validator,
    model_validator,
    validate_email,
)
from pydantic import (
    BaseModel as BaseBaseModel,
)
from pydantic.functional_validators import BeforeValidator
from pydantic_settings import BaseSettings, SettingsConfigDict

from app.api.auth.basic_auth import (
    ADMIN_SUPPLIED_PASSWORD_MIN_LENGTH,
    is_valid_password,
)
from app.api.auth.schemas import Permissions
from app.config import settings

logger = structlog.get_logger(__name__)


def validate_password(password: str) -> str:
    if not is_valid_password(password):
        raise ValueError(
            f"Password must be at least {ADMIN_SUPPLIED_PASSWORD_MIN_LENGTH} characters long "
            f"and have at least one lowercase, uppercase, and symbol."
        )
    return password


PasswordInput = Annotated[str, BeforeValidator(validate_password)]

GROUP_BY_TOP_K = 5


def to_isoformat(v: datetime.datetime) -> str:
    return v.isoformat()


class BaseModel(BaseBaseModel):
    model_config = ConfigDict(
        ser_json_timedelta="float",
        json_encoders={
            datetime.datetime: to_isoformat,
            Decimal: float,
        },
    )


@dataclass(eq=False, frozen=True)
class Missing:
    __missing__: Literal["__missing__"]


MISSING = Missing(__missing__="__missing__")


class TenantBase(BaseModel):
    """Base class for Tenant."""

    pass


@dataclass
class WorkspaceInfoBare:
    id: UUID
    display_name: str


class TenantCreate(TenantBase):
    """Creation model for the tenant."""

    id: UUID = Field(default_factory=uuid4)

    organization_id: UUID | None = None

    # this must not include colon (':') which is the separator used for SCIM group names
    display_name: str = Field(..., min_length=1, pattern=r"^[a-zA-Z0-9\-_ ']+$")

    tenant_handle: str | None = None

    is_personal: bool = False


class WorkspaceCreate(TenantBase):
    """Creation model for the workspace."""

    id: UUID = Field(default_factory=uuid4)

    # this must not include colon (':') which is the separator used for SCIM group names
    display_name: str = Field(..., min_length=1, pattern=r"^[a-zA-Z0-9\-_ '@()]+$")

    tenant_handle: str | None = None


class WorkspacePatch(TenantBase):
    """Patch model for the workspace."""

    # this must not include colon (':') which is the separator used for SCIM group names
    display_name: str = Field(..., min_length=1, pattern=r"^[a-zA-Z0-9\-_ '@()]+$")


class TenantCreatePrivileged(TenantCreate):
    """Privileged creation model for the tenant, cannot be exposed via API."""

    config: TenantConfig | None = None

    is_personal: bool = False


class TenantClaimInviteCode(BaseModel):
    code: str


class Tenant(TenantBase):
    """Tenant schema."""

    id: UUID

    organization_id: UUID | None = None

    created_at: datetime.datetime

    display_name: str

    is_personal: bool

    is_deleted: bool

    tenant_handle: str | None = None

    model_config = ConfigDict(from_attributes=True)


class WorkspaceInfo(Tenant):
    """Workspace info, including config."""

    config: TenantConfig | None = None


class TenantForUser(Tenant):
    read_only: bool = False

    role_id: UUID | None = None

    role_name: str | None = None

    permissions: list[str] | None = None


class TenantUsageLimitType(str, Enum):
    payload_size = "payload_size"
    events_ingested_per_hour = "events_ingested_per_hour"
    total_unique_traces = "total_unique_traces"
    events_ingested_per_minute = "events_ingested_per_minute"

    user_defined_monthly_traces = "user_defined_monthly_traces"
    user_defined_monthly_longlived_traces = "user_defined_monthly_longlived_traces"

    user_defined_unknown = "user_defined_unknown"


class TenantUsageLimitInfo(BaseModel):
    in_reject_set: bool
    usage_limit_type: TenantUsageLimitType | None = None
    tenant_limit: int | None = None


class AccessScope(str, Enum):
    organization = "organization"
    workspace = "workspace"


class ListPendingMembersQueryParams(BaseModel):
    limit: int = Field(50, ge=1, le=500)
    offset: int = Field(0, ge=0)
    emails: list[str] = []

    @model_validator(mode="after")
    def check_list_params(self: Self) -> Self:
        max_length = 100
        if len(self.emails) > max_length:
            raise HTTPException(
                400, f"emails query param cannot have more than {max_length} items"
            )
        return self


class ListMembersQueryParams(ListPendingMembersQueryParams):
    ls_user_ids: list[UUID] = []
    user_ids: list[UUID] = []

    @model_validator(mode="after")
    def check_list_params(self: Self) -> Self:
        max_length = 100
        if len(self.ls_user_ids) > max_length or len(self.user_ids) > max_length:
            raise HTTPException(
                400, f"user ids query params cannot have more than {max_length} items"
            )
        return self


class PendingIdentityCreate(BaseModel):
    email: str
    read_only: bool = False
    role_id: UUID | None = None
    workspace_ids: list[UUID] | None = None
    workspace_role_id: UUID | None = None
    # password is only populated in the response if a basic auth user
    # was created without an inputted password, so a password was autogenerated.
    # password and full_name can only be used with basic auth enabled.
    password: PasswordInput | None = None
    full_name: str | None = None

    @model_validator(mode="after")
    def check_basic_auth(self) -> Self:
        if not settings.BASIC_AUTH_ENABLED and self.password:
            raise HTTPException(
                status_code=400,
                detail="Password is only allowed when basic auth is enabled",
            )
        if not settings.BASIC_AUTH_ENABLED and self.full_name:
            raise HTTPException(
                status_code=400,
                detail="Full name is only allowed when basic auth is enabled",
            )
        return self


class PendingIdentityCreateInternal(PendingIdentityCreate):
    access_scope: AccessScope = AccessScope.workspace


class IdentityCreate(BaseModel):
    # This model is exposed externally
    user_id: UUID | None = None
    # org_identity_id should be used because it uniquely identifies the user in the organization
    # rather than the user_id which is a specific login method
    org_identity_id: UUID | None = None
    read_only: bool | None = None
    role_id: UUID | None = None

    @model_validator(mode="after")
    def check_params(self) -> Self:
        if not self.user_id and not self.org_identity_id:
            raise HTTPException(
                status_code=400,
                detail="Either user_id or org_identity_id must be provided",
            )
        return self


class IdentityCreateInternal(IdentityCreate):
    # This model is not exposed externally, so we allow access_scope
    access_scope: AccessScope = AccessScope.workspace
    ls_user_id: UUID | None = None


class PendingIdentity(PendingIdentityCreateInternal):
    id: UUID
    user_id: UUID | None = None
    tenant_id: UUID | None = None
    organization_id: UUID | None = None
    created_at: datetime.datetime
    role_name: str | None = None
    # These are for the parent organization-scoped pending identity role.
    # They are only present in a workspace scope.
    org_role_id: UUID | None = None
    org_role_name: str | None = None


class OrgPendingIdentity(PendingIdentity):
    # This is the list of tenant_ids that the user is pending in; does not include disabled tenants
    tenant_ids: List[UUID] = []


class Identity(BaseModel):
    id: UUID
    organization_id: UUID
    tenant_id: UUID | None = None
    created_at: datetime.datetime
    user_id: UUID
    ls_user_id: UUID
    read_only: bool
    role_id: UUID | None = None
    role_name: str | None = None
    access_scope: AccessScope = AccessScope.workspace


class IdentityPatch(BaseModel):
    read_only: bool | None = None
    role_id: UUID


class IdentityPatchInternal(IdentityPatch):
    # This model is not exposed externally, so we allow access_scope
    access_scope: AccessScope = AccessScope.workspace


class MemberIdentity(Identity):
    email: Optional[str] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    linked_login_methods: List[ProviderUserSlim] = []
    # These are for the parent organization-scoped identity role.
    # They are only present in a workspace scope.
    org_role_id: UUID | None = None
    org_role_name: str | None = None


class OrgMemberIdentity(MemberIdentity):
    # This is the list of tenant_ids that the user is a member of; does not include disabled tenants
    tenant_ids: List[UUID] = []
    linked_login_methods: List[ProviderUserSlim] = []


class WorkspaceIdentityCreateInternal(BaseModel):
    ls_user_id: Optional[UUID] = None
    user_id: Optional[UUID] = None
    read_only: Optional[bool] = None
    role_id: Optional[UUID] = None
    workspace_role_id: UUID | None = None
    workspace_ids: list[UUID] | None = None


class BasicAuthMemberCreate(BaseModel):
    user_id: Optional[UUID] = None
    ls_user_id: Optional[UUID] = None
    email: str
    read_only: Optional[bool] = None
    role_id: Optional[UUID] = None
    password: Optional[PasswordInput] = None
    full_name: Optional[str] = None
    workspace_role_id: UUID | None = None
    workspace_ids: list[UUID] | None = None

    @field_validator("email", mode="before")
    @classmethod
    def check_email(cls, v: str):
        _, email = validate_email(v)
        return email.lower()


class GetUserInput(BaseModel):
    email: Optional[str] = None


class BasicAuthUserPatch(BaseModel):
    password: Optional[PasswordInput] = None
    full_name: Optional[str] = None

    @model_validator(mode="after")
    def check_basic_auth(self) -> Self:
        if not settings.BASIC_AUTH_ENABLED and self.password:
            raise HTTPException(
                status_code=400,
                detail="Password is only allowed when basic auth is enabled",
            )
        if not settings.BASIC_AUTH_ENABLED and self.full_name:
            raise HTTPException(
                status_code=400,
                detail="Full name is only allowed when basic auth is enabled",
            )
        return self


class OrgIdentityPatch(BasicAuthUserPatch):
    role_id: UUID | None = None


class OrgIdentityPatchInternal(OrgIdentityPatch):
    # This model is not exposed externally, so we allow access_scope.
    # Keep read_only for compatibility until deprecated, it will always be None
    access_scope: AccessScope = AccessScope.workspace
    read_only: bool | None = None


class User(BaseModel):
    id: UUID
    ls_user_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    email: str
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None


class UserWithPassword(User):
    password: Optional[str] = None


class UserWithPasswordHash(User):
    hashed_password: Optional[str] = None


class AuthProvider(str, Enum):
    email = "email"
    supabase_non_sso = "supabase:non-sso"
    supabase_sso = "supabase:sso"
    oidc = "oidc"
    custom_oidc = "custom-oidc"


class ProvisioningMethod(str, Enum):
    scim = "scim"
    saml_jit = "saml:jit"


class ProviderUserSlim(BaseModel):
    id: UUID
    provider: AuthProvider | None = None
    ls_user_id: UUID
    saml_provider_id: UUID | None = None
    provider_user_id: UUID | None = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    email: str | None = None
    full_name: str | None = None
    email_confirmed_at: datetime.datetime | None = None


# This should only be used internally
class ProviderUser(ProviderUserSlim):
    hashed_password: str | None = None


class OrganizationMembers(BaseModel):
    """Organization members schema."""

    organization_id: UUID
    members: List[OrgMemberIdentity]
    pending: List[OrgPendingIdentity]


class TenantMembers(BaseModel):
    """Tenant members schema."""

    tenant_id: UUID
    members: List[MemberIdentity]
    pending: List[PendingIdentity]


class TenantStats(BaseModel):
    """Stats for a tenant."""

    tenant_id: UUID
    dataset_count: int
    tracer_session_count: int
    repo_count: int
    annotation_queue_count: int
    deployment_count: int
    dashboards_count: int

    model_config = ConfigDict(from_attributes=True)


class Task(BaseModel):
    """Task schema."""

    id: UUID
    tenant_id: UUID
    target: str
    args: Dict[str, Any]
    result: Dict[str, Any] | None = None
    status: str
    created_at: datetime.datetime
    updated_at: datetime.datetime | None = None
    model_config = ConfigDict(from_attributes=True)


class TaskCreatedResponse(BaseModel):
    """Task created response."""

    id: UUID


class TraceTier(str, Enum):
    longlived = "longlived"
    shortlived = "shortlived"

    @property
    def ttl_seconds(self) -> TraceTier:
        return settings.TRACE_TIER_TTL_DURATION_SEC_MAP[self.value]


class TracerSessionBase(BaseModel):
    """Base class for TracerSession."""

    start_time: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    end_time: datetime.datetime | None = None
    extra: Dict[str, Any] | None = None
    name: str = Field(default_factory=lambda: uuid4().hex)
    description: str | None = None
    default_dataset_id: UUID | None = None
    reference_dataset_id: UUID | None = None
    trace_tier: TraceTier | None = None


class TracerSessionUpdate(BaseModel):
    """Update class for TracerSession."""

    name: str | None = None
    description: str | None = None
    default_dataset_id: UUID | None = None
    end_time: datetime.datetime | None = None
    extra: Dict[str, Any] | None = None
    trace_tier: TraceTier | None = None


class TracerSessionCreate(TracerSessionBase):
    """Create class for TracerSession."""

    id: UUID | None = None
    reference_dataset_id: UUID | None = None


class TracerSessionWithoutVirtualFields(TracerSessionCreate):
    """TracerSession schema."""

    id: UUID
    tenant_id: UUID
    last_run_start_time_live: datetime.datetime | None = None
    model_config = ConfigDict(from_attributes=True)


def to_float(v: Any) -> float:
    if isinstance(v, datetime.timedelta):
        return v.total_seconds()
    return v


class TracerSession(TracerSessionCreate):
    """TracerSession schema."""

    id: UUID
    run_count: int | None = None
    latency_p50: Annotated[float | None, BeforeValidator(to_float)] = None
    latency_p99: Annotated[float | None, BeforeValidator(to_float)] = None
    first_token_p50: Annotated[float | None, BeforeValidator(to_float)] = None
    first_token_p99: Annotated[float | None, BeforeValidator(to_float)] = None
    total_tokens: int | None = None
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    total_cost: Decimal | None = None
    prompt_cost: Decimal | None = None
    completion_cost: Decimal | None = None
    tenant_id: UUID
    last_run_start_time: datetime.datetime | None = None
    last_run_start_time_live: datetime.datetime | None = None
    feedback_stats: Dict[str, Any] | None = None
    session_feedback_stats: Dict[str, Any] | None = None
    run_facets: List[Dict[str, Any]] | None = None
    error_rate: float | None = None
    streaming_rate: float | None = None
    test_run_number: int | None = None
    model_config = ConfigDict(from_attributes=True)


class SimpleExperimentInfo(BaseModel):
    """Simple experiment info schema for use with comparative experiments"""

    id: UUID
    name: str


class ComparativeExperimentBase(BaseModel):
    """ComparativeExperiment schema."""

    id: UUID
    name: str | None = None
    description: str | None = None
    tenant_id: UUID
    created_at: datetime.datetime
    modified_at: datetime.datetime
    reference_dataset_id: UUID
    extra: Dict[str, Any] | None = None


class ComparativeExperiment(ComparativeExperimentBase):
    """ComparativeExperiment schema."""

    experiments_info: List[SimpleExperimentInfo]
    feedback_stats: Dict[str, Any] | None = None


class PublicComparativeExperiment(BaseModel):
    """Publicly-shared ComparativeExperiment schema."""

    id: UUID
    name: str | None = None
    description: str | None = None
    created_at: datetime.datetime
    modified_at: datetime.datetime
    extra: Dict[str, Any] | None = None
    experiments_info: List[SimpleExperimentInfo]
    feedback_stats: Dict[str, Any] | None = None


class RunStats(BaseModel):
    run_count: int
    latency_p50: Annotated[float | None, BeforeValidator(to_float)] = None
    latency_p99: Annotated[float | None, BeforeValidator(to_float)] = None
    first_token_p50: Annotated[float | None, BeforeValidator(to_float)] = None
    first_token_p99: Annotated[float | None, BeforeValidator(to_float)] = None
    total_tokens: int | None = None
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    median_tokens: int | None = None
    completion_tokens_p50: int | None = None
    prompt_tokens_p50: int | None = None
    tokens_p99: int | None = None
    completion_tokens_p99: int | None = None
    prompt_tokens_p99: int | None = None
    last_run_start_time: datetime.datetime | None = None
    feedback_stats: Dict[str, Any] | None = None
    run_facets: List[Dict[str, Any]] | None = None
    error_rate: float | None = None
    streaming_rate: float | None = None
    total_cost: Decimal | None = None
    prompt_cost: Decimal | None = None
    completion_cost: Decimal | None = None
    cost_p50: Decimal | None = None
    cost_p99: Decimal | None = None


class RunGroupStats(RunStats):
    group_count: int


class TimedeltaInput(BaseModel):
    """Timedelta input."""

    days: int = 0
    hours: int = 0
    minutes: int = 0

    @model_validator(mode="after")
    def normalize(self) -> Self:
        """Essential to normalize since this object is used as a cache key in various places."""
        overflow_hrs = self.minutes // 60
        self.minutes = self.minutes % 60
        hours = self.hours + overflow_hrs
        overflow_days = hours // 24
        self.hours = hours % 24
        self.days = self.days + overflow_days
        return self


class MetadataKeyValue(BaseModel):
    key: str
    value: str


class CustomChartsSectionsRequest(BaseModel):
    limit: int = Field(default=100, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    title_contains: str | None = None
    ids: Optional[List[UUID]] = Field(
        Query(None), description="Dashboard IDs to filter by"
    )
    sort_by: Optional[str] = "created_at"
    sort_by_desc: Optional[bool] = True
    tag_value_id: Optional[List[UUID]] = Field(
        Query(None), description="Tag value IDs of resource tags to filter by"
    )


class CustomChartsSectionsCloneRequest(BaseModel):
    section_id: UUID | None = None
    session_id: UUID | None = None

    @model_validator(mode="after")
    def check_exclusivity(self) -> Self:
        if self.section_id and self.session_id:
            raise HTTPException(
                status_code=422, detail="Cannot set both section_id and session_id"
            )
        if not self.section_id and not self.session_id:
            raise HTTPException(
                status_code=422, detail="Must set one of section_id or session_id"
            )
        return self


class CustomChartsRequestBase(BaseModel):
    timezone: str = "UTC"
    start_time: datetime.datetime | None = None
    end_time: datetime.datetime | None = None
    stride: TimedeltaInput = TimedeltaInput(minutes=15)
    omit_data: bool = False

    @model_validator(mode="after")
    def check_end_time(self) -> Self:
        if self.omit_data:
            return self
        if self.end_time and not self.start_time:
            raise HTTPException(
                status_code=422, detail="Start time must exist if end time is provided"
            )
        if self.end_time and self.start_time and self.end_time < self.start_time:
            raise HTTPException(
                status_code=422, detail="End time must be greater than start time"
            )
        end_time = self.end_time or datetime.datetime.now(datetime.timezone.utc)
        total_seconds_diff = (end_time - self.start_time).total_seconds()  # type: ignore
        total_seconds_stride = datetime.timedelta(
            days=self.stride.days,
            hours=self.stride.hours,
            minutes=self.stride.minutes,
        ).total_seconds()
        if total_seconds_stride < 60:
            raise HTTPException(
                status_code=422, detail="Stride must be at least 1 minute"
            )
        if total_seconds_diff / total_seconds_stride > settings.CUSTOM_CHART_MAX_POINTS:
            raise HTTPException(
                status_code=422,
                detail=f"Stride must be such that the number of data points is less than {settings.CUSTOM_CHART_MAX_POINTS}",
            )
        return self


class CustomChartsRequest(CustomChartsRequestBase):
    after_index: int | None = None  # for pagination by section
    tag_value_id: list[UUID] | None = None


class CustomChartsSectionRequest(CustomChartsRequestBase):
    group_by: RunStatsGroupBy | None = None
    """Global group by to apply to all applicable charts in the section."""


class CustomChartsRequestInternal(CustomChartsRequest):
    access_scope: AccessScope = AccessScope.workspace

    @model_validator(mode="after")
    def check_access_scope(self) -> Self:
        if self.access_scope == AccessScope.organization and self.tag_value_id:
            raise HTTPException(
                status_code=422,
                detail="Tag value filtering is only supported for workspace-scoped charts",
            )
        return self


class CustomChartType(str, Enum):
    """Enum for custom chart types."""

    line = "line"
    bar = "bar"


class RunStatsSelect(str, Enum):
    """Metrics you can select from run stats endpoint."""

    run_count = "run_count"
    latency_p50 = "latency_p50"
    latency_p99 = "latency_p99"
    latency_avg = "latency_avg"
    first_token_p50 = "first_token_p50"
    first_token_p99 = "first_token_p99"
    total_tokens = "total_tokens"
    prompt_tokens = "prompt_tokens"
    completion_tokens = "completion_tokens"
    median_tokens = "median_tokens"
    completion_tokens_p50 = "completion_tokens_p50"
    prompt_tokens_p50 = "prompt_tokens_p50"
    tokens_p99 = "tokens_p99"
    completion_tokens_p99 = "completion_tokens_p99"
    prompt_tokens_p99 = "prompt_tokens_p99"
    last_run_start_time = "last_run_start_time"
    feedback_stats = "feedback_stats"
    run_facets = "run_facets"
    error_rate = "error_rate"
    streaming_rate = "streaming_rate"
    total_cost = "total_cost"
    prompt_cost = "prompt_cost"
    completion_cost = "completion_cost"
    cost_p50 = "cost_p50"
    cost_p99 = "cost_p99"


class CustomChartMetric(str, Enum):
    """Metrics you can chart. Feedback metrics are not available for organization-scoped charts."""

    run_count = "run_count"
    latency_p50 = "latency_p50"
    latency_p99 = "latency_p99"
    latency_avg = "latency_avg"
    first_token_p50 = "first_token_p50"
    first_token_p99 = "first_token_p99"
    total_tokens = "total_tokens"
    prompt_tokens = "prompt_tokens"
    completion_tokens = "completion_tokens"
    median_tokens = "median_tokens"
    completion_tokens_p50 = "completion_tokens_p50"
    prompt_tokens_p50 = "prompt_tokens_p50"
    tokens_p99 = "tokens_p99"
    completion_tokens_p99 = "completion_tokens_p99"
    prompt_tokens_p99 = "prompt_tokens_p99"
    feedback = "feedback"
    feedback_score_avg = "feedback_score_avg"
    feedback_values = "feedback_values"
    total_cost = "total_cost"
    prompt_cost = "prompt_cost"
    completion_cost = "completion_cost"
    error_rate = "error_rate"
    streaming_rate = "streaming_rate"
    cost_p50 = "cost_p50"
    cost_p99 = "cost_p99"


class HostProjectChartSubMetric(str, Enum):
    worker_max = "worker_max"
    worker_active = "worker_active"
    runs_pending = "runs_pending"
    runs_running = "runs_running"


class HostProjectChartMetric(str, Enum):
    """LGP Metrics you can chart."""

    memory_usage = "memory_usage"
    cpu_usage = "cpu_usage"
    disk_usage = "disk_usage"
    restart_count = "restart_count"
    replica_count = "replica_count"
    worker_count = "worker_count"
    lg_run_count = "lg_run_count"
    responses_per_second = "responses_per_second"
    error_responses_per_second = "error_responses_per_second"
    p95_latency = "p95_latency"


class OrgCustomChartMetric(str, Enum):
    """Metrics you can chart at the organization level."""

    # This is a subset of CustomChartMetric to avoid the heaviest queries

    run_count = "run_count"
    latency_p50 = "latency_p50"
    latency_p99 = "latency_p99"
    first_token_p50 = "first_token_p50"
    first_token_p99 = "first_token_p99"
    total_tokens = "total_tokens"
    prompt_tokens = "prompt_tokens"
    completion_tokens = "completion_tokens"
    median_tokens = "median_tokens"
    total_cost = "total_cost"
    prompt_cost = "prompt_cost"
    completion_cost = "completion_cost"
    error_rate = "error_rate"
    streaming_rate = "streaming_rate"


class CustomChartSeriesFilters(BaseModel):
    filter: str | None = None
    trace_filter: str | None = None
    tree_filter: str | None = None
    session: list[UUID] | None = None

    @model_validator(mode="after")
    def check_at_least_one_filter(self) -> Self:
        if not any(
            [
                self.filter,
                self.trace_filter,
                self.tree_filter,
                self.session,
            ]
        ):
            raise HTTPException(
                status_code=422, detail="At least one filter must be provided"
            )
        return self


class CustomChartSeriesBase(BaseModel):
    name: str
    filters: CustomChartSeriesFilters | None = None
    metric: CustomChartMetric
    feedback_key: str | None = None
    workspace_id: UUID | None = None
    project_metric: HostProjectChartMetric | None = None

    @model_validator(mode="after")
    def check_feedback_key(self) -> Self:
        if (
            self.metric == CustomChartMetric.feedback
            or self.metric == CustomChartMetric.feedback_score_avg
            or self.metric == CustomChartMetric.feedback_values
        ) and not self.feedback_key:
            raise HTTPException(
                status_code=422,
                detail="Feedback key is required for feedback metric",
            )
        return self


class CustomChartSeriesCreate(CustomChartSeriesBase):
    group_by: RunStatsGroupBy | None = None


class CustomChartSeries(CustomChartSeriesBase):
    id: UUID | str  # for prebuilt charts, this will be a str
    group_by: RunStatsGroupBySeriesResponse | None = None


class CustomChartSeriesUpdate(CustomChartSeriesCreate):
    id: UUID | None = None


# This is the externally-exposed model
class CustomChartCreate(BaseModel):
    title: str
    description: str | None = None
    index: int | None = Field(None, ge=0, le=100)
    chart_type: CustomChartType
    series: list[CustomChartSeriesCreate]
    section_id: UUID | None = None
    metadata: dict[str, Any] | None = None
    common_filters: CustomChartSeriesFilters | None = None


def _check_custom_charts(
    access_scope: AccessScope,
    series: list[CustomChartSeriesCreate] | None | Missing,
    enforce_present: bool = True,
) -> None:
    series_present = series is not None and not isinstance(series, Missing)
    if access_scope == AccessScope.workspace and not series_present and enforce_present:
        raise HTTPException(
            status_code=422,
            detail="Series are required for workspace-scoped charts",
        )

    if series_present:
        max_series = (
            settings.CHARTS_MAX_SERIES_WS
            if access_scope == AccessScope.workspace
            else settings.CHARTS_MAX_SERIES_ORG
        )
        min_series = 1 if access_scope == AccessScope.workspace else 0
        series_list = cast(list[CustomChartSeriesCreate], series)
        if len(series_list) < min_series or len(series_list) > max_series:
            raise HTTPException(
                status_code=422,
                detail="Number of series must be between 1 and 3 for workspace-scoped charts",
            )
        if access_scope == AccessScope.organization and any(
            s.metric.value not in [v.value for v in OrgCustomChartMetric]
            for s in series_list
        ):
            raise HTTPException(
                status_code=422,
                detail="Unsupported metric for organization charts.",
            )
        if access_scope == AccessScope.organization and any(
            s.filters.trace_filter or s.filters.tree_filter
            for s in series_list
            if s.filters
        ):
            # This is because they force a join on the runs table, but we use
            # the runs_history table for organization charts
            raise HTTPException(
                status_code=422,
                detail="Trace filter and tree filter are not supported for organization charts.",
            )

        if access_scope == AccessScope.workspace and any(
            s.workspace_id for s in series_list
        ):
            raise HTTPException(
                status_code=422,
                detail="workspace_id is only supported for organization charts",
            )


class CustomChartCreateInternal(CustomChartCreate):
    access_scope: AccessScope = AccessScope.workspace

    @model_validator(mode="after")
    def check_series(self) -> Self:
        _check_custom_charts(self.access_scope, self.series)
        return self


class CustomChartCreatePreview(BaseModel):
    series: list[CustomChartSeries]
    common_filters: CustomChartSeriesFilters | None = None


class CustomChartPreviewRequest(BaseModel):
    bucket_info: CustomChartsRequestBase
    chart: CustomChartCreatePreview


class CustomChartPreviewRequestInternal(CustomChartPreviewRequest):
    access_scope: AccessScope = AccessScope.workspace

    @model_validator(mode="after")
    def check_series(self) -> Self:
        _check_custom_charts(self.access_scope, self.chart.series)
        return self


# This is the externally-exposed model
class CustomChartUpdate(BaseModel):
    title: str | Missing = MISSING
    description: str | None | Missing = MISSING
    index: int | Missing = Field(MISSING, ge=0, le=100)
    chart_type: CustomChartType | Missing = MISSING
    series: list[CustomChartSeriesUpdate] | Missing = MISSING
    section_id: UUID | Missing = MISSING
    metadata: dict[str, Any] | None | Missing = MISSING
    common_filters: CustomChartSeriesFilters | None | Missing = MISSING


class CustomChartUpdateInternal(CustomChartUpdate):
    access_scope: AccessScope = AccessScope.workspace

    @model_validator(mode="after")
    def check_series(self) -> Self:
        _check_custom_charts(self.access_scope, self.series, enforce_present=False)
        return self


class CustomChartResponse(BaseModel):
    id: UUID
    title: str
    description: str | None = None
    index: int
    chart_type: CustomChartType
    section_id: UUID
    metadata: dict[str, Any] | None = None
    series: list[CustomChartSeries] | None


class CustomChartsDataPoint(BaseModel):
    series_id: str
    timestamp: datetime.datetime
    value: int | float | dict[str, Any] | None
    group: str | None = None


class SingleCustomChartResponseBase(BaseModel):
    data: list[CustomChartsDataPoint]


class OrgUsage(BaseModel):
    customer_id: str
    billable_metric_id: str
    billable_metric_name: str
    start_timestamp: str
    end_timestamp: str
    value: float | None
    groups: dict[str, float] | None


class SingleCustomChartResponse(SingleCustomChartResponseBase):
    id: UUID | str  # for prebuilt charts, this will be a string
    title: str
    description: str | None = None
    metadata: dict[str, Any] | None = None
    index: int
    chart_type: CustomChartType
    series: list[CustomChartSeries]
    common_filters: CustomChartSeriesFilters | None = None


class CustomChartsSectionCreate(BaseModel):
    title: str
    description: str | None = None
    index: int | None = None


class CustomChartsSectionCreateInternal(CustomChartsSectionCreate):
    access_scope: AccessScope = AccessScope.workspace


# This is the externally-exposed model
class CustomChartsSectionUpdate(BaseModel):
    title: str | Missing = MISSING
    description: str | None | Missing = MISSING
    index: int | Missing = MISSING


class CustomChartsSectionUpdateInternal(CustomChartsSectionUpdate):
    access_scope: AccessScope = AccessScope.workspace


class CustomChartsSectionResponse(CustomChartsSectionCreate):
    id: UUID
    chart_count: int | None = None
    created_at: datetime.datetime | None = None
    modified_at: datetime.datetime | None = None


class SingleCustomChartSubSectionResponse(BaseModel):
    title: str
    description: str | None = None
    index: int
    id: UUID | str  # for pre-built charts, this will be a string
    charts: list[SingleCustomChartResponse]


class CustomChartsSection(CustomChartsSectionCreate):
    id: UUID | str  # for pre-built charts, this will be a string
    session_id: UUID | None = None
    charts: list[SingleCustomChartResponse]
    sub_sections: list[SingleCustomChartSubSectionResponse] | None = None


class CustomChartsResponse(BaseModel):
    sections: list[CustomChartsSection]


class ExampleCore(BaseModel):
    """Core class for Example."""

    outputs: Dict[str, Any] | None = None
    dataset_id: UUID
    source_run_id: UUID | None = None
    metadata: Dict[str, Any] | None = None


class ExampleBase(ExampleCore):
    """Base class for Example, with created_at."""

    inputs: Dict[str, Any]
    created_at: datetime.datetime = Field(
        default_factory=lambda: datetime.datetime.now(tz=datetime.timezone.utc)
    )


DatasetSplitType = list[str] | str | None


class ExampleCreateBase(ExampleCore):
    """Create class for Example."""

    inputs: Dict[str, Any] | None = None
    split: DatasetSplitType = "base"
    id: UUID | None = Field(default_factory=uuid4)
    use_source_run_io: bool = False
    use_source_run_attachments: Optional[List[str]] = None

    @model_validator(mode="after")
    def check_use_source_run_io(self) -> Self:
        if self.use_source_run_io and not self.source_run_id:
            raise HTTPException(
                status_code=422,
                detail="source_run_id is required when use_source_run_io is True",
            )
        elif not self.use_source_run_io and self.inputs is None:
            raise HTTPException(
                status_code=422,
                detail="inputs is required unless a source run is provided and use_source_run_io is true",
            )
        elif self.use_source_run_attachments and not self.source_run_id:
            raise HTTPException(
                status_code=422,
                detail="source_run_id is required when use_source_run_attachments is used",
            )
        return self


class ExampleCreate(ExampleCreateBase):
    """Create class for Example."""

    created_at: datetime.datetime = Field(
        default_factory=lambda: datetime.datetime.now(tz=datetime.timezone.utc)
    )


class ExampleValidationResult(BaseModel):
    """Validation result for Example, combining fields from Create/Base/Update schemas."""

    dataset_id: UUID | None = None
    inputs: Dict[str, Any] | None = None
    outputs: Dict[str, Any] | None = None
    created_at: datetime.datetime | None = None
    metadata: Dict[str, Any] | None = None
    source_run_id: UUID | None = None
    split: DatasetSplitType | None = "base"
    id: UUID | None = Field(default_factory=uuid4)
    use_source_run_io: bool = False
    overwrite: bool = False


class ExampleBulkCreate(ExampleCreateBase):
    """Example class with optional created_at field to prevent multiple versions when bulk creating examples."""

    created_at: datetime.datetime | None = None


class AttachmentsOperations(BaseModel):
    rename: Dict[str, str] = Field(
        default_factory=dict, description="Mapping of old attachment names to new names"
    )
    retain: List[str] = Field(
        default_factory=list, description="List of attachment names to keep"
    )


class ExampleUpdate(BaseModel):
    """Update class for Example."""

    dataset_id: UUID | None = None
    inputs: Dict[str, Any] | None = None
    outputs: Dict[str, Any] | None = None
    attachments_operations: AttachmentsOperations | None = None
    metadata: Dict[str, Any] | None = None
    split: DatasetSplitType | None = None
    overwrite: bool = False


class ExampleUpdateWithID(ExampleUpdate):
    """Bulk update class for Example (includes example id)."""

    id: UUID


class Example(ExampleBase):
    """Example schema."""

    id: UUID
    name: str
    modified_at: datetime.datetime | None = None
    attachment_urls: Dict[str, Any] | None = None

    model_config = ConfigDict(from_attributes=True)


class DataType(str, Enum):
    """Enum for dataset data types."""

    kv = "kv"
    llm = "llm"
    chat = "chat"


class DatasetTransformationType(str, Enum):
    """Enum for dataset data types."""

    remove_system_messages = "remove_system_messages"
    convert_to_openai_message = "convert_to_openai_message"
    convert_to_openai_tool = "convert_to_openai_tool"
    remove_extra_fields = "remove_extra_fields"

    # DEPRECATED
    extract_tools_from_run = "extract_tools_from_run"


class DatasetTransformation(BaseModel):
    path: list[str]
    transformation_type: DatasetTransformationType


class DatasetCore(BaseModel):
    """Core class for Dataset."""

    name: str
    description: str | None = None
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    inputs_schema_definition: Dict[str, Any] | None = None
    outputs_schema_definition: Dict[str, Any] | None = None
    externally_managed: bool | None = False
    transformations: List[DatasetTransformation] | None = None


class DatasetBase(DatasetCore):
    """Base class for Dataset."""

    data_type: DataType | None = DataType.kv


class DatasetCreate(DatasetCore):
    """Create class for Dataset."""

    id: UUID | None = None
    extra: Dict[str, Any] | None = None
    data_type: DataType = DataType.kv


class DatasetUpdate(BaseModel):
    """Update class for Dataset."""

    name: str | None | Missing = MISSING
    description: str | None | Missing = MISSING
    inputs_schema_definition: Dict[str, Any] | None | Missing = MISSING
    outputs_schema_definition: Dict[str, Any] | None | Missing = MISSING
    patch_examples: Dict[UUID, ExampleUpdate] | None = None
    transformations: List[DatasetTransformation] | None | Missing = MISSING


class Dataset(DatasetBase):
    """Dataset schema."""

    id: UUID
    tenant_id: UUID
    example_count: int
    session_count: int
    modified_at: datetime.datetime
    last_session_start_time: datetime.datetime | None = None
    inputs_schema_definition: Dict[str, Any] | None = None
    outputs_schema_definition: Dict[str, Any] | None = None

    model_config = ConfigDict(from_attributes=True)


class DatasetIndexRequest(BaseModel):
    """Dataset schema for serving."""

    tag: str | None = "latest"


class SearchDatasetRequest(BaseModel):
    """Dataset schema for serving."""

    # NOTE: THIS WILL BE DEPRECATED AND MOVED TO GO

    inputs: Dict[str, Any]
    limit: int = Field(default=5, ge=1, le=100)
    debug: bool = False
    filter: str | None = None


class DatasetIndexInfo(BaseModel):
    """Dataset schema for serving."""

    # NOTE: THIS WILL BE DEPRECATED AND MOVED TO GO

    dataset_id: UUID
    tag: str | None = "latest"
    last_updated_version: datetime.datetime | None = None


class SearchedFewShotExample(BaseModel):
    """Dataset schema for serving."""

    # NOTE: THIS WILL BE DEPRECATED AND MOVED TO GO

    inputs: Dict[str, Any]
    outputs: Dict[str, Any]
    id: UUID
    debug_info: Dict[str, Any] | None = None


class SearchDatasetResponse(BaseModel):
    """Dataset schema for serving."""

    # NOTE: THIS WILL BE DEPRECATED AND MOVED TO GO

    examples: List[SearchedFewShotExample]


class DatasetWithExamples(Dataset):
    """Dataset schema with examples."""

    examples: List[Example]


class DatasetSchemaForUpdate(DatasetBase):
    id: UUID
    tenant_id: UUID

    model_config = ConfigDict(from_attributes=True)


class DatasetSchemaForShareInfo(DatasetBase):
    id: UUID

    model_config = ConfigDict(from_attributes=True)


class SortByDatasetColumn(str, Enum):
    """Enum for available dataset columns to sort by."""

    name = "name"
    created_at = "created_at"
    last_session_start_time = "last_session_start_time"
    example_count = "example_count"
    session_count = "session_count"


class SortByComparativeExperimentColumn(str, Enum):
    """Enum for available comparative experiment columns to sort by."""

    name = "name"
    created_at = "created_at"


class QueryParamsForComparativeExperimentsSchema(BaseModel):
    """Query params for comparative experiments."""

    name: str | None = None
    name_contains: str | None = (
        None  # search by comparative experiment or tracer session name
    )
    limit: int = Field(default=100, ge=1, le=100)
    offset: int = Field(default=0, ge=0)
    sort_by: SortByComparativeExperimentColumn = (
        SortByComparativeExperimentColumn.created_at
    )
    sort_by_desc: bool = True
    reference_dataset_id: UUID | None = None
    id: list[UUID] | None = None


class ComparativeExperimentCreate(BaseModel):
    """Create class for ComparativeExperiment."""

    id: UUID = Field(default_factory=uuid4)
    experiment_ids: List[UUID]
    name: str | None = None
    description: str | None = None
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    modified_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    reference_dataset_id: UUID
    extra: Dict[str, Any] | None = None


class QueryParamsForShareDatasetSchema(BaseModel):
    share_projects: bool = False


class QueryParamsForDatasetVersionsSchema(BaseModel):
    search: str | None = None
    example: UUID | None = None
    limit: int = Field(default=100, ge=1, le=100)
    offset: int = Field(default=0, ge=0)


def coerce_isoformat(v: str | datetime.datetime) -> str | datetime.datetime:
    if isinstance(v, str):
        try:
            return datetime.datetime.fromisoformat(v)
        except ValueError:
            pass

    return v


AsOfDescription = Query(
    description="Only modifications made on or before this time are included."
    " If None, the latest version of the dataset is used.",
)

AsOfType = Annotated[
    Union[datetime.datetime, str], BeforeValidator(coerce_isoformat), AsOfDescription
]


class QueryParamsForDatasetVersionsDiffSchema(BaseModel):
    from_version: AsOfType
    to_version: AsOfType


class QueryParamsForSingleDatasetVersionSchema(BaseModel):
    as_of: datetime.datetime | None = None
    tag: str | None = None


class PutDatasetVersionsSchema(BaseModel):
    as_of: AsOfType
    tag: str


class DatasetVersion(BaseModel):
    """Dataset version schema."""

    tags: List[str] | None = None
    as_of: datetime.datetime


class DatasetDiffInfo(BaseModel):
    """Dataset diff schema."""

    examples_modified: List[UUID]
    examples_added: List[UUID]
    examples_removed: List[UUID]


class DatasetPublicSchema(DatasetBase):
    """Public schema for datasets.

    Doesn't currently include session counts/stats
    since public test project sharing is not yet shipped
    """

    id: UUID
    example_count: int

    model_config = ConfigDict(from_attributes=True)


class APIKeyGetResponse(BaseModel):
    """API key GET schema."""

    created_at: datetime.datetime | None = None
    id: UUID
    short_key: str
    description: str
    read_only: bool = False
    last_used_at: datetime.datetime | None = None
    expires_at: datetime.datetime | None = None

    model_config = ConfigDict(from_attributes=True)


class APIKeyCreateRequest(BaseModel):
    """API key POST schema.

    expires_at: Optional datetime when the API key will expire.
    workspaces: List of workspace UUIDs this key can access (feature-flagged).
    """

    description: str = "Default API key"
    read_only: bool = False
    expires_at: datetime.datetime | None = None
    workspaces: list[UUID] | None = (
        None  # If None, key has access to all workspaces; feature-flagged
    )


class APIKeyCreateResponse(APIKeyGetResponse):
    """API key POST schema."""

    key: str


class RunTypeEnum(str, Enum):
    """Enum for run types."""

    tool = "tool"
    chain = "chain"
    llm = "llm"
    retriever = "retriever"
    embedding = "embedding"
    prompt = "prompt"
    parser = "parser"


class RunsFilterDataSourceTypeEnum(str, Enum):
    """Enum for run data source types."""

    # current has full data, used for fetching run data
    current = "current"
    # non-ttl version of current, used for dashboards
    historical = "historical"
    # same as historical but also has metadata_kv, inputs_kv, outputs_kv columns, used for filtering
    lite = "lite"
    # same as lite but only has root runs
    root_lite = "root_lite"

    def get_table_name(self) -> str:
        if self == RunsFilterDataSourceTypeEnum.current:
            return "runs"
        elif self == RunsFilterDataSourceTypeEnum.historical:
            return "runs_history"
        elif self == RunsFilterDataSourceTypeEnum.lite:
            return "runs_lite"
        elif self == RunsFilterDataSourceTypeEnum.root_lite:
            return "root_runs_lite"
        else:
            raise ValueError(f"Unsupported data source type: {self}")


class FilterQueryParamsForRunSchema(BaseModel):
    """Query params for run endpoints."""

    id: list[UUID] | None = None
    trace: UUID | None = None
    parent_run: UUID | None = None
    run_type: RunTypeEnum | None = None
    session: list[UUID] | None = None
    reference_example: list[UUID] | None = None
    execution_order: int | None = Field(None, ge=1, le=1)
    start_time: datetime.datetime | None = None
    end_time: datetime.datetime | None = None
    error: bool | None = None
    query: str | None = None
    filter: str | None = None
    trace_filter: str | None = None
    tree_filter: str | None = None
    is_root: bool | None = None
    data_source_type: RunsFilterDataSourceTypeEnum | None = None
    skip_pagination: bool | None = None

    # For search
    # If this is set and ALL attributes besides
    # `session`, `start_time`, `end_time`, `cursor`, `parent_run`
    # are ignored and search database (eg Quickwit) will be used
    search_filter: str | None = None
    use_experimental_search: bool = False

    @model_validator(mode="after")
    def validate_model(self) -> Self:
        if self.id and len(self.id) > 100:
            logger.warning("Detected large id list", size=len(self.id))
        if self.is_root is not None and self.execution_order is not None:
            raise ValueError("Cannot filter by both is_root and execution_order")
        if self.use_experimental_search and not self.session:
            raise ValueError("Session must be set when using experimental search")

        return self


class RunStatsGroupBy(BaseModel):
    """Group by param for run stats."""

    attribute: Literal["name", "run_type", "tag", "metadata"]
    path: str | None = None
    max_groups: int = GROUP_BY_TOP_K

    @model_validator(mode="after")
    def validate_path(self) -> Self:
        if self.attribute != "metadata" and self.path:
            raise ValueError(
                f"Can only specify 'path' for attribute 'metadata'. Received\n{self.path=}, {self.attribute=}"
            )
        if self.max_groups > 20:
            raise ValueError("max_groups must be less than or equal to 20")
        # Essential to normalize path since this object is used as a cache key in various places.
        self.path = self.path or None
        return self


class RunStatsGroupBySeriesResponse(RunStatsGroupBy):
    """Include additional information about where the group_by param was set."""

    set_by: Literal["section", "series"] | None = None
    """Whether the group by was set by the series params or the section params."""


class RunStatsQueryParams(FilterQueryParamsForRunSchema):
    """Query params for run stats."""

    group_by: RunStatsGroupBy | None = None
    groups: list[str | None] | None = None


class RunSelect(str, Enum):
    """Enum for available run columns."""

    id = "id"
    name = "name"
    run_type = "run_type"
    start_time = "start_time"
    end_time = "end_time"
    status = "status"
    error = "error"
    extra = "extra"
    events = "events"
    inputs = "inputs"
    inputs_preview = "inputs_preview"
    inputs_s3_urls = "inputs_s3_urls"
    inputs_or_signed_url = "inputs_or_signed_url"
    outputs = "outputs"
    outputs_preview = "outputs_preview"
    outputs_s3_urls = "outputs_s3_urls"
    outputs_or_signed_url = "outputs_or_signed_url"
    s3_urls = "s3_urls"
    error_or_signed_url = "error_or_signed_url"
    events_or_signed_url = "events_or_signed_url"
    extra_or_signed_url = "extra_or_signed_url"
    serialized_or_signed_url = "serialized_or_signed_url"
    parent_run_id = "parent_run_id"
    manifest_id = "manifest_id"
    manifest_s3_id = "manifest_s3_id"
    manifest = "manifest"
    session_id = "session_id"
    serialized = "serialized"
    reference_example_id = "reference_example_id"
    reference_dataset_id = "reference_dataset_id"
    total_tokens = "total_tokens"
    prompt_tokens = "prompt_tokens"
    completion_tokens = "completion_tokens"
    total_cost = "total_cost"
    prompt_cost = "prompt_cost"
    completion_cost = "completion_cost"
    price_model_id = "price_model_id"
    first_token_time = "first_token_time"
    trace_id = "trace_id"
    dotted_order = "dotted_order"
    last_queued_at = "last_queued_at"
    feedback_stats = "feedback_stats"
    child_run_ids = "child_run_ids"
    parent_run_ids = "parent_run_ids"
    tags = "tags"
    in_dataset = "in_dataset"
    app_path = "app_path"
    share_token = "share_token"
    trace_tier = "trace_tier"
    trace_first_received_at = "trace_first_received_at"
    ttl_seconds = "ttl_seconds"
    trace_upgrade = "trace_upgrade"
    thread_id = "thread_id"


# Exclude these from default if no select sent
RunSelectExcludeDefault = {
    RunSelect.child_run_ids,
    RunSelect.inputs_preview,
    RunSelect.inputs_or_signed_url,
    RunSelect.outputs_preview,
    RunSelect.outputs_or_signed_url,
    RunSelect.error_or_signed_url,
    RunSelect.events_or_signed_url,
    RunSelect.extra_or_signed_url,
    RunSelect.serialized_or_signed_url,
    RunSelect.inputs_s3_urls,
    RunSelect.outputs_s3_urls,
    RunSelect.s3_urls,
}


class RunDateOrder(str, Enum):
    """Enum for run start date order."""

    asc = "asc"
    desc = "desc"


class BodyParamsForRunSchema(FilterQueryParamsForRunSchema):
    """Query params for run endpoints."""

    cursor: Optional[str] = None
    limit: int = Field(default=100, ge=1, le=settings.RUNS_QUERY_LIMIT)
    select: list[RunSelect] = [e for e in RunSelect if e not in RunSelectExcludeDefault]
    order: RunDateOrder = RunDateOrder.desc
    skip_prev_cursor: bool = False


class RunsGenerateQueryFeedbackKeys(str, Enum):
    user_score = "user_score"
    user_edited = "user_edited"
    user_removed = "user_removed"
    user_opened_run = "user_opened_run"
    user_selected_run = "user_selected_run"
    results_size = "results_size"
    valid_filter = "valid_filter"


class RequestBodyForRunsGenerateQuery(BaseModel):
    query: str
    feedback_keys: list[RunsGenerateQueryFeedbackKeys] = Field(default_factory=list)


class ResponseBodyForRunsGenerateQuery(BaseModel):
    filter: str
    feedback_urls: dict[RunsGenerateQueryFeedbackKeys, str]


class SortParamsForRunsComparisonView(BaseModel):
    sort_by: str
    sort_order: Literal["ASC", "DESC"] = "DESC"


class QueryExampleSchemaWithRunsBase(BaseModel):
    session_ids: list[UUID]
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=10, ge=1)
    preview: bool = False


class GroupExampleRunsByField(str, Enum):
    run_metadata = "run_metadata"
    example_metadata = "example_metadata"


class QueryGroupedExamplesWithRuns(QueryExampleSchemaWithRunsBase):
    group_by: GroupExampleRunsByField
    metadata_key: str
    per_group_limit: int = Field(default=5, ge=1, le=10)

    @field_validator("session_ids")
    def validate_session_ids(cls, v: list[UUID], info: ValidationInfo) -> list[UUID]:
        if len(v) != 1:
            raise HTTPException(
                status_code=422,
                detail=f"Must pass exactly one session_id in order to group by metadata, got {len(v)}",
            )
        return v

    @model_validator(mode="after")
    def validate_limit_based_on_format(self):
        """Apply limit validation based on format."""
        if self.limit > 100:
            raise ValueError("Input should be less than or equal to 100")
        return self


class QueryExampleSchemaWithRuns(QueryExampleSchemaWithRunsBase):
    format: Literal["csv"] | None = None
    comparative_experiment_id: UUID | None = None
    sort_params: SortParamsForRunsComparisonView | None = None
    filters: dict[UUID, list[str]] | None = None

    @model_validator(mode="before")
    def enforce_csv_rules(cls, values):
        """If the 'format' is 'csv', impose certain constraints."""
        fmt = values.get("format")
        if fmt == "csv":
            # 1) No other filter-like fields allowed:
            if values.get("comparative_experiment_id") is not None:
                raise ValueError(
                    "comparative_experiment_id is not allowed when format=csv"
                )
            if values.get("filters"):
                raise ValueError("filters are not allowed when format=csv")
            if values.get("sort_params") is not None:
                raise ValueError("sort_params is not allowed when format=csv")
            if values.get("preview") is not False:
                raise ValueError("preview is not allowed when format=csv")

            # Force values for CSV format
            values["offset"] = 0
            values["limit"] = settings.DOWNLOAD_EXPERIMENT_RESULTS_LIMIT

        return values

    @model_validator(mode="after")
    def validate_limit_based_on_format(self):
        """Validate limit based on format after all fields are populated."""
        max_limit = (
            settings.DOWNLOAD_EXPERIMENT_RESULTS_LIMIT if self.format == "csv" else 100
        )
        if getattr(self, "limit", 0) > max_limit:
            raise ValueError(f"Input should be less than or equal to {max_limit}")
        return self


class QueryParamsForPublicRunSchema(BaseModel):
    """Query params for public run endpoints."""

    id: list[UUID] | None = None


class QueryParamsForPublicDatasetSchema(BaseModel):
    """Query params for public dataset endpoints."""

    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    sort_by: SortByDatasetColumn = SortByDatasetColumn.last_session_start_time
    sort_by_desc: bool = True


class FilterQueryParamsForDatasetSchema(BaseModel):
    id: list[UUID] | None = None
    data_type: DataType | list[DataType] | None = None
    name: str | None = None
    name_contains: str | None = None
    metadata: str | None = None
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    sort_by: SortByDatasetColumn = SortByDatasetColumn.last_session_start_time
    sort_by_desc: bool = True
    tag_value_id: list[UUID] | None = None


class ExampleListOrder(str, Enum):
    recent = "recent"
    random = "random"
    recently_created = "recently_created"


class ExampleSelect(str, Enum):
    id = "id"
    created_at = "created_at"
    modified_at = "modified_at"
    name_ = "name"
    dataset_id = "dataset_id"
    source_run_id = "source_run_id"
    metadata = "metadata"
    inputs = "inputs"
    outputs = "outputs"
    attachment_urls = "attachment_urls"


class FilterQueryParamsForCountExampleSchema(BaseModel):
    """Query parameters for counting examples."""

    id: list[UUID] | None = None
    as_of: AsOfType = "latest"
    metadata: str | None = None
    full_text_contains: list[str] | None = None
    splits: list[str] | None = None
    dataset: UUID | None = None
    filter: str | None = None


class FilterQueryParamsForExampleSchema(BaseModel):
    id: list[UUID] | None = None
    as_of: AsOfType = "latest"
    metadata: str | None = None
    full_text_contains: list[str] | None = None
    splits: list[str] | None = None
    dataset: UUID | None = None
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=1000)
    filter: str | None = None
    order: ExampleListOrder = ExampleListOrder.recent
    random_seed: float | None = None

    select: tuple[ExampleSelect, ...] = tuple(e for e in ExampleSelect)
    ignore_unselected: bool = False


class SessionSortableColumns(Enum):
    # postgres
    NAME = "name"
    START_TIME = "start_time"
    # clickhouse
    LAST_RUN_START_TIME = "last_run_start_time"
    LATENCY_P50 = "latency_p50"
    LATENCY_P99 = "latency_p99"
    ERROR_RATE = "error_rate"
    FEEDBACK = "feedback"


SORTABLE_COLUMNS_FOR_NON_DATASET_PROJECTS = {
    SessionSortableColumns.NAME,
    SessionSortableColumns.START_TIME,
    SessionSortableColumns.LAST_RUN_START_TIME,
    SessionSortableColumns.ERROR_RATE,
    SessionSortableColumns.LATENCY_P50,
    SessionSortableColumns.LATENCY_P99,
}

SORTABLE_COLUMNS_IN_POSTGRES = {
    SessionSortableColumns.NAME,
    SessionSortableColumns.START_TIME,
}


class FilterQueryParamsForTracerSessionSchema(BaseModel):
    reference_free: bool | None = None
    reference_dataset: list[UUID] | None = None
    id: list[UUID] | None = None
    name: str | None = None
    name_contains: str | None = None
    dataset_version: str | None = None
    metadata: str | None = None
    tag_value_id: list[UUID] | None = None

    sort_by: SessionSortableColumns = SessionSortableColumns.START_TIME
    sort_by_desc: bool = True
    sort_by_feedback_key: str | None = None

    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    facets: bool = False
    filter: str | None = None  # query lang filter
    include_stats: bool = False
    use_approx_stats: bool = False

    @model_validator(mode="after")
    def check_sort_by(self) -> Self:
        if (
            self.sort_by == SessionSortableColumns.FEEDBACK
            and self.sort_by_feedback_key is None
        ):
            raise HTTPException(
                status_code=422,
                detail="Must specify sort_by_feedback_key when sorting by feedback",
            )
        if not self.reference_dataset and self.sort_by is not None:
            if self.sort_by not in SORTABLE_COLUMNS_FOR_NON_DATASET_PROJECTS:
                raise HTTPException(
                    status_code=422,
                    detail="sort_by must be one of last_run_start_time_live, start_time, error_rate, latency, or name when reference_dataset is not provided",
                )
        if (
            self.name or self.name_contains
        ) and self.sort_by not in SORTABLE_COLUMNS_IN_POSTGRES:
            raise HTTPException(
                status_code=400,
                detail="Filtering by name or dataset version only supported when sorting by name or start time.",
            )
        return self


TracerSessionMetadataResponse = RootModel[Dict[str, list[str]]]


class QueryParamsForSingleTracerSessionSchema(BaseModel):
    include_stats: bool = False


class QueryParamsForTracerSessionMetadataSchema(BaseModel):
    metadata_keys: list[str] | None = None
    start_time: datetime.datetime | None = None
    k: int = Field(default=10, ge=1)
    root_runs_only: bool = False


class QueryParamsForTasksSchema(BaseModel):
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)


class RunBaseSchema(BaseModel):
    name: str
    inputs: dict | None = None
    inputs_preview: str | None = None
    run_type: RunTypeEnum
    start_time: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    end_time: datetime.datetime | None = None
    extra: dict | None = None
    error: str | None = None
    execution_order: int = Field(default=1, ge=1)
    serialized: dict | None = None
    outputs: dict | None = None
    outputs_preview: str | None = None
    parent_run_id: UUID | None = None
    manifest_id: UUID | None = None
    manifest_s3_id: UUID | None = None
    events: list[dict] | None = None
    tags: list[str] | None = None
    inputs_s3_urls: dict | None = None
    outputs_s3_urls: dict | None = None
    s3_urls: dict | None = None
    trace_id: UUID | None = None
    dotted_order: str | None = None


class RunPublicSchema(RunBaseSchema):
    id: UUID
    status: str
    child_run_ids: List[UUID] | None = None
    direct_child_run_ids: List[UUID] | None = None
    parent_run_ids: List[UUID] | None = None
    feedback_stats: Dict[str, dict] | None = None
    reference_example_id: UUID | None = None
    total_tokens: int = 0
    prompt_tokens: int = 0
    completion_tokens: int = 0
    prompt_token_details: dict[str, int] | None = None
    completion_token_details: dict[str, int] | None = None
    total_cost: Decimal | None = None
    prompt_cost: Decimal | None = None
    completion_cost: Decimal | None = None
    prompt_cost_details: dict[str, Decimal] | None = None
    completion_cost_details: dict[str, Decimal] | None = None
    price_model_id: UUID | None = None
    first_token_time: datetime.datetime | None = None
    trace_id: UUID
    dotted_order: str

    model_config = ConfigDict(from_attributes=True)


class RunSchemaWithoutVirtualFields(RunBaseSchema):
    """Run schema."""

    id: UUID
    session_id: UUID
    reference_example_id: UUID | None = None
    total_tokens: int | None = None
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    total_cost: Decimal | None = None
    prompt_cost: Decimal | None = None
    completion_cost: Decimal | None = None


class RunSchemaComparisonView(RunSchemaWithoutVirtualFields):
    """Run schema for comparison view."""

    status: str
    trace_id: UUID
    feedback_stats: Dict[str, dict] | None = None
    app_path: str | None = None


class RunSchema(RunPublicSchema):
    """Run schema."""

    manifest_id: UUID | None = None
    session_id: UUID
    app_path: str
    last_queued_at: datetime.datetime | None = None
    in_dataset: bool | None = None
    share_token: UUID | None = None
    trace_tier: TraceTier | None = None
    trace_first_received_at: datetime.datetime | None = None
    ttl_seconds: int | None = None
    trace_upgrade: bool = False
    reference_dataset_id: UUID | None = None
    thread_id: str | None = None

    model_config = ConfigDict(from_attributes=True)


class ListRunsResponse(BaseModel):
    runs: list[RunSchema]
    cursors: dict[str, str | None]
    search_cursors: dict[str, Any | None] | None = (
        None  # only used when qw search is enabled
    )
    parsed_query: str | None = None


class ListPublicRunsResponse(BaseModel):
    runs: list[RunPublicSchema]
    cursors: dict[str, str | None]
    parsed_query: str | None = None


class RunPublicDatasetSchema(RunPublicSchema):
    """Schema for a run in a publicly-shared dataset."""

    session_id: UUID
    reference_example_id: UUID | None = None

    model_config = ConfigDict(from_attributes=True)


class ListPublicDatasetRunsResponse(BaseModel):
    runs: list[RunPublicDatasetSchema]
    cursors: dict[str, str | None]
    parsed_query: str | None = None


class RunSchemaWithAnnotationQueueInfo(RunSchema):
    """Run schema with annotation queue info."""

    queue_run_id: UUID
    last_reviewed_time: datetime.datetime | None = None
    added_at: datetime.datetime | None = None
    effective_added_at: datetime.datetime | None = None


class IdentityAnnotationQueueRunStatusCreateSchema(BaseModel):
    """Identity annotation queue run status create schema."""

    status: str | None = None
    override_added_at: Optional[datetime.datetime] = None


class ExportAnnotationQueueRunsRequest(BaseModel):
    """Export annotation queue runs request schema."""

    start_time: datetime.datetime | None = None
    end_time: datetime.datetime | None = None


class ExampleWithRuns(Example):
    # TODO: delete after CH migration
    """Example schema with list of runs."""

    runs: list[RunSchema]


class ExampleWithRunsCH(Example):
    """Example schema with list of runs."""

    runs: list[RunSchemaComparisonView]


class QueryFeedbackDelta(BaseModel):
    baseline_session_id: UUID
    comparison_session_ids: list[UUID]
    feedback_key: str
    filters: dict[UUID, list[str]] | None = None
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    comparative_experiment_id: UUID | None = None


class FeedbackDelta(BaseModel):
    """Feedback key with number of improvements and regressions."""

    improved_examples: list[UUID]
    regressed_examples: list[UUID]


class SessionFeedbackDelta(BaseModel):
    """List of feedback keys with number of improvements and regressions for each."""

    feedback_deltas: dict[UUID, FeedbackDelta]


class PublicExampleWithRuns(Example):
    """Schema for an example in a publicly-shared dataset with list of runs."""

    runs: list[RunPublicDatasetSchema]


class RunManifestSchema(BaseModel):
    id: UUID
    manifest: dict
    model_config = ConfigDict(from_attributes=True)


class RunShareSchema(BaseModel):
    run_id: UUID

    share_token: UUID
    model_config = ConfigDict(from_attributes=True)


class DatasetShareSchema(BaseModel):
    dataset_id: UUID
    share_token: UUID

    model_config = ConfigDict(from_attributes=True)


class TaskPayload(BaseModel):
    """Task payload."""

    target: str
    args: dict
    subject: UUID | None = None


class TaskTargetPayload(TaskPayload):
    """Task target payload."""

    task_id: UUID


class FeedbackSourceBase(BaseModel):
    type: str | None = None
    metadata: Dict[str, Any] | None = None


class FeedbackSource(FeedbackSourceBase):
    """The feedback source loaded from the database."""

    user_id: UUID | None = None
    # optionally enriched with user's name
    user_name: str | None = None


class AppFeedbackSource(FeedbackSourceBase):
    """Feedback from the LangChainPlus App."""

    type: str = "app"


class APIFeedbackSource(FeedbackSourceBase):
    """API feedback source."""

    type: str = "api"


class ModelFeedbackSource(FeedbackSourceBase):
    """Model feedback source."""

    type: str = "model"


class AutoEvalFeedbackSource(FeedbackSourceBase):
    """Auto eval feedback source."""

    type: str = "auto_eval"


class SourceType(str, Enum):
    """Enum for feedback source types."""

    api = "api"
    model = "model"
    app = "app"
    auto_eval = "auto_eval"


class FeedbackType(str, Enum):
    """Enum for feedback types."""

    continuous = "continuous"
    categorical = "categorical"
    freeform = "freeform"


class FeedbackCategory(BaseModel):
    """Specific value and label pair for feedback"""

    value: float
    label: str | None = Field(default=None, min_length=1)


class FeedbackConfig(BaseModel):
    type: FeedbackType
    min: float | None = None
    max: float | None = None
    categories: List[FeedbackCategory] | None = None

    @model_validator(mode="after")
    def validate_feedback(self) -> Self:
        if self.type == FeedbackType.continuous:
            if self.min is not None and self.max is not None and self.min >= self.max:
                raise ValueError("min must be less than max")

            if self.categories is not None:
                category_labels = [
                    category.label
                    for category in self.categories
                    if category.label is not None
                ]
                if len(set(category_labels)) != len(category_labels):
                    raise ValueError("category labels must be unique")

                category_values = [category.value for category in self.categories]
                if len(set(category_values)) != len(category_values):
                    raise ValueError("category values must be unique")

                if self.min is not None and any(
                    value < self.min for value in category_values
                ):
                    raise ValueError(
                        "category values must be greater than or equal to min"
                    )

                if self.max is not None and any(
                    value > self.max for value in category_values
                ):
                    raise ValueError(
                        "category values must be less than or equal to max"
                    )

        elif self.type == FeedbackType.categorical:
            if not self.categories:
                raise ValueError(
                    "categories must not be empty for categorical feedback"
                )
            if len(self.categories) < 2:
                raise ValueError(
                    "categories must have at least 2 values for categorical feedback"
                )
            if self.min is not None or self.max is not None:
                raise ValueError(
                    "min and max should not be specified for categorical feedback"
                )

            category_values = [category.value for category in self.categories]
            if len(set(category_values)) != len(category_values):
                raise ValueError("category values must be unique")

            category_labels = [
                category.label
                for category in self.categories
                if category.label is not None
            ]
            if len(set(category_labels)) != len(category_labels):
                raise ValueError("category labels must be unique")

        elif self.type == FeedbackType.freeform:
            if (
                self.min is not None
                or self.max is not None
                or self.categories is not None
            ):
                raise ValueError(
                    "min, max and categories should not be specified for freeform feedback"
                )

        if self.min is not None:
            if FeedbackBaseSchema.invalid_scale(self.min):
                raise ValueError(
                    "min must be between -99999.9999 and 99999.9999 inclusive"
                )
            if FeedbackBaseSchema.invalid_precision(self.min):
                raise ValueError(
                    f"min has a maximum precision of {FeedbackConstants.MAX_SCORE_PRECISION} decimal places"
                )
        if self.max is not None:
            if FeedbackBaseSchema.invalid_scale(self.max):
                raise ValueError(
                    "max must be between -99999.9999 and 99999.9999 inclusive"
                )
            if FeedbackBaseSchema.invalid_precision(self.max):
                raise ValueError(
                    f"max has a maximum precision of {FeedbackConstants.MAX_SCORE_PRECISION} decimal places"
                )

        return self


class FeedbackConfigSchema(BaseModel):
    feedback_key: str
    feedback_config: FeedbackConfig
    tenant_id: UUID
    modified_at: datetime.datetime
    is_lower_score_better: bool | None = None


class CreateFeedbackConfigSchema(BaseModel):
    feedback_key: str
    feedback_config: FeedbackConfig
    is_lower_score_better: bool | None = False


class UpdateFeedbackConfigSchema(BaseModel):
    feedback_key: str
    feedback_config: FeedbackConfig | None = None
    is_lower_score_better: bool | None = None


class FeedbackConstants:
    # allow -99999.9999 to 99999.9999 with 0.0001 step: https://clickhouse.com/docs/sql-reference/data-types/decimal#decimal-value-ranges
    MAX_SCORE_ABS: int = 100_000
    MAX_SCORE_PRECISION: int = 4


class FeedbackCoreSchema(BaseModel):
    """Feedback without run id or session id"""

    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    """The time the feedback was created."""
    modified_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    """The time the feedback was last modified."""
    key: str
    """The metric name, tag, or aspect to provide feedback on."""
    score: float | int | bool | None = None
    """Value or score to assign the run."""
    value: float | int | bool | str | dict | None = None
    """The display value for the feedback if not a metric."""
    comment: str | None = None
    """Comment or explanation for the feedback."""
    correction: dict | str | None = None
    """Correction for the run."""
    feedback_group_id: UUID | None = None
    """For grouping comparative feedback."""
    comparative_experiment_id: UUID | None = None
    """The associated comparative experiment ID this feedback is logged for."""

    @staticmethod
    def invalid_precision(v: float) -> bool:
        return (
            -1 * int(Decimal(str(v)).as_tuple().exponent)
            > FeedbackConstants.MAX_SCORE_PRECISION
        )

    @staticmethod
    def invalid_scale(v: float | int) -> bool:
        return (
            v <= -1 * FeedbackConstants.MAX_SCORE_ABS
            or v >= FeedbackConstants.MAX_SCORE_ABS
        )

    @staticmethod
    def raise_invalid_score(v: float | int | bool | None) -> None:
        if isinstance(v, (float, int)) and FeedbackCoreSchema.invalid_scale(v):
            raise ValueError(
                f"score must be between -99999.9999 and 99999.9999 inclusive, was {v}"
            )
        if isinstance(v, float) and FeedbackCoreSchema.invalid_precision(v):
            raise ValueError(
                f"score has a maximum precision of {FeedbackConstants.MAX_SCORE_PRECISION} decimal places, was {v}"
            )


class FeedbackBaseSchema(FeedbackCoreSchema):
    """Feedback schema."""

    run_id: UUID | None = None
    """The associated run ID this feedback is logged for."""
    session_id: UUID | None = None
    """The associated session ID this feedback is logged for."""


class FeedbackUpdateSchema(BaseModel):
    """Schema used for updating feedback"""

    score: float | int | bool | None = None
    """Value or score to assign the run."""
    value: float | int | bool | str | dict | None = None
    """The display value for the feedback if not a metric."""
    comment: str | None = None
    """Comment or explanation for the feedback."""
    correction: dict | str | None = None
    """Correction for the run."""
    feedback_config: FeedbackConfig | None = None
    """Feedback config schema for validating the feedback. Will be used to validate against the feedback and stored for the tenant if not already present."""

    @model_validator(mode="after")
    def check_score(self) -> Self:
        FeedbackCoreSchema.raise_invalid_score(self.score)
        return self


class FeedbackCreateSchema(FeedbackBaseSchema):
    """Schema used for creating feedback."""

    trace_id: UUID | None = None
    id: UUID = Field(default_factory=uuid4)
    key: str = Field(max_length=180)
    """The metric name, tag, or aspect to provide feedback on."""
    feedback_source: (
        AppFeedbackSource
        | APIFeedbackSource
        | ModelFeedbackSource
        | AutoEvalFeedbackSource
        | None
    ) = None
    """The source of the feedback."""
    feedback_config: FeedbackConfig | None = None
    """Feedback config schema for validating the feedback. Will be used to validate against the feedback and stored for the tenant if not already present."""
    error: bool | None = None
    """Whether the evaluator run errored"""

    @model_validator(mode="after")
    def run_id_or_session_id(self) -> Self:
        if self.run_id is None and self.session_id is None:
            raise ValueError("run_id or session_id is required")
        elif self.run_id is not None and self.session_id is not None:
            raise ValueError("run_id and session_id cannot be provided together")
        elif self.trace_id is not None and self.session_id is not None:
            raise ValueError("trace_id and session_id cannot be provided together")
        return self

    @model_validator(mode="after")
    def check_score(self) -> Self:
        FeedbackCoreSchema.raise_invalid_score(self.score)
        return self


class FeedbackCreateSchemaInternal(FeedbackCreateSchema):
    """Schema used for creating feedback internally."""

    extra: dict | None = None


class FeedbackCreateCoreSchema(FeedbackCoreSchema):
    """Schema used for creating feedback without run id or session id."""

    id: UUID = Field(default_factory=uuid4)
    key: str = Field(max_length=180)
    """The metric name, tag, or aspect to provide feedback on."""
    feedback_source: (
        AppFeedbackSource
        | APIFeedbackSource
        | ModelFeedbackSource
        | AutoEvalFeedbackSource
        | None
    ) = None
    """The source of the feedback."""
    feedback_config: FeedbackConfig | None = None
    """Feedback config schema for validating the feedback. Will be used to validate against the feedback and stored for the tenant if not already present."""
    extra: dict | None = None
    """The metadata of the feedback"""

    @model_validator(mode="after")
    def check_score(self) -> Self:
        FeedbackCoreSchema.raise_invalid_score(self.score)
        return self


class FeedbackSchema(FeedbackBaseSchema):
    """Schema for getting feedback."""

    id: UUID
    trace_id: UUID | None = None
    start_time: datetime.datetime | None = None
    feedback_source: FeedbackSource | None = None
    """The source of the feedback."""
    model_config = ConfigDict(from_attributes=True)
    extra: dict | None = None
    """The metadata of the feedback"""


class FeedbackLevel(str, Enum):
    """Enum for feedback levels."""

    run = "run"
    session = "session"


class QueryParamsForFeedbackSchema(BaseModel):
    """Query Params for listing feedback."""

    run: list[UUID] | None = None
    key: list[str] | None = None
    session: list[UUID] | None = None
    source: list[SourceType] | None = None
    limit: int = Field(default=0, ge=0, le=100)
    offset: int = Field(default=0, ge=0)
    user: list[UUID] | None = None
    has_comment: bool | None = None
    has_score: bool | None = None
    level: FeedbackLevel | None = None
    max_created_at: datetime.datetime | None = None
    min_created_at: datetime.datetime | None = None
    include_user_names: bool | None = None
    use_select_sequential_consistency: bool = False


class ExperimentResultRow(BaseModel):
    """Class for a single row in the uploaded experiment results."""

    row_id: UUID | None = None
    inputs: dict
    expected_outputs: dict | None = None
    actual_outputs: dict | None = None
    evaluation_scores: list[FeedbackCreateCoreSchema] | None = None
    start_time: datetime.datetime
    end_time: datetime.datetime
    run_name: str | None = None
    error: str | None = None
    run_metadata: Dict[str, Any] | None = None


class ExperimentResultsUpload(BaseModel):
    """Class for uploading the results of an already-run experiment."""

    experiment_name: str
    experiment_description: str | None = None
    dataset_id: UUID | None = None
    dataset_name: str | None = None
    dataset_description: str | None = None
    summary_experiment_scores: list[FeedbackCreateCoreSchema] | None = None
    results: list[ExperimentResultRow]
    experiment_start_time: datetime.datetime
    experiment_end_time: datetime.datetime
    experiment_metadata: Dict[str, Any] | None = None

    @model_validator(mode="after")
    def check_experiment_times(self) -> Self:
        if self.experiment_start_time >= self.experiment_end_time:
            raise ValueError("experiment_start_time must be before experiment_end_time")
        if self.dataset_name is None and self.dataset_id is None:
            raise ValueError("dataset_name or dataset_id is required")
        for result in self.results:
            if result.start_time >= result.end_time:
                raise ValueError("start_time must be before end_time")
            if result.start_time < self.experiment_start_time:
                raise ValueError("start_time must be after experiment_start_time")
            if result.end_time > self.experiment_end_time:
                raise ValueError("end_time must be before experiment_end_time")
        return self


class ExperimentResultsUploadResult(BaseModel):
    """Class for uploading the results of an already-run experiment."""

    dataset: Dataset
    experiment: TracerSession


class ChatMessageData(BaseModel):
    content: str | List[Dict | str]
    role: Optional[str] = None
    name: Optional[str] = None
    additional_kwargs: Optional[dict] = None


class ChatMessage(BaseModel):
    type: str
    data: ChatMessageData


class SerializedChatMessage(BaseModel):
    lc: int
    id: list[str]
    type: str
    kwargs: ChatMessageData


class ChatOutput(BaseModel):
    generations: List[ChatMessage | SerializedChatMessage]


class InstructGeneration(BaseModel):
    text: str
    message: Optional[ChatMessage | SerializedChatMessage] = None


class InstructOutput(BaseModel):
    generations: List[InstructGeneration]


class ChatInput(BaseModel):
    messages: List[ChatMessage | SerializedChatMessage]


class ChatPluralInput(BaseModel):
    messages: List[List[ChatMessage | SerializedChatMessage]] = Field(..., max_length=1)


class InstructInput(BaseModel):
    prompt: str


class InstructPluralInput(BaseModel):
    prompts: list[str] = Field(..., max_length=1)


class AnnotationQueueBaseSchema(BaseModel):
    """AnnotationQueue schema."""

    name: str
    """The name of the annotation queue."""
    description: Optional[str] = None
    """The description of the annotation queue."""
    created_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    """The time the annotation queue was created."""
    updated_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    """The last time the annotation queue was modified (any run added or reviewed)"""
    default_dataset: UUID | None = None
    """The default dataset ID for the annotation queue."""
    num_reviewers_per_item: Optional[int] = 1
    """The number of reviewers per item for the annotation queue."""
    enable_reservations: Optional[bool] = True
    """Whether reservations are enabled for the annotation queue."""
    reservation_minutes: Optional[int] = 1
    """The number of minutes for a reservation."""


class AnnotationQueueCreateSchema(AnnotationQueueBaseSchema):
    """AnnotationQueue schema."""

    id: UUID = Field(default_factory=uuid4)
    """The ID of the annotation queue."""
    rubric_items: list[AnnotationQueueRubricItemSchema] | None = None
    rubric_instructions: str | None = None
    session_ids: list[UUID] | None = None
    """The session id with which to populate the queue."""


class PopulateAnnotationQueueSchema(BaseModel):
    queue_id: UUID
    """The ID of the annotation queue."""
    session_ids: list[UUID]
    """The session id with which to populate the queue."""


class AnnotationQueueSchema(AnnotationQueueBaseSchema):
    """AnnotationQueue schema."""

    id: UUID
    """The ID of the annotation queue."""
    tenant_id: UUID
    """The associated tenant ID this annotation queue is created for."""
    source_rule_id: UUID | None = None
    """The rule id which is populating this queue."""
    run_rule_id: UUID | None = None
    """The run rule associated with this queue."""


class AnnotationQueueSchemaWithSize(AnnotationQueueSchema):
    """AnnotationQueue schema with size."""

    total_runs: int
    """The number of runs in the annotation queue."""


class AnnotationQueueRubricItemSchema(BaseModel):
    feedback_key: str
    description: str | None = None
    value_descriptions: dict[str, str] | None = None
    score_descriptions: dict[str, str] | None = None


class AnnotationQueueSchemaWithRubric(AnnotationQueueSchema):
    """AnnotationQueue schema with rubric."""

    rubric_items: list[AnnotationQueueRubricItemSchema] | None = None
    rubric_instructions: str | None = None


class AnnotationQueueUpdateSchema(BaseModel):
    """AnnotationQueue update schema."""

    name: str | None = None
    """The name of the annotation queue."""
    description: Optional[str] = None
    """The description of the annotation queue."""
    default_dataset: UUID | None = None
    """The default dataset ID for the annotation queue."""
    num_reviewers_per_item: Optional[int] = 1
    """The number of reviewers per item for the annotation queue."""
    enable_reservations: bool = True
    """Whether reservations are enabled for the annotation queue."""
    reservation_minutes: int | None = None
    """The number of minutes for a reservation."""
    rubric_items: list[AnnotationQueueRubricItemSchema] | None = None
    rubric_instructions: str | None = None


class AnnotationQueueRunBaseSchema(BaseModel):
    """AnnotationQueueRun schema."""

    run_id: UUID
    """The associated run IDs this annotation queue run is created for."""
    queue_id: UUID
    """The associated queue ID this annotation queue run is created for."""
    last_reviewed_time: Optional[datetime.datetime] = None
    """The last reviewed time of the annotation queue run."""
    added_at: datetime.datetime = Field(default_factory=datetime.datetime.utcnow)
    """The time the annotation queue run was added."""


class AnnotationQueueRunSchema(AnnotationQueueRunBaseSchema):
    id: UUID
    """The ID of the annotation queue run."""


class AnnotationQueueRunUpdateSchema(BaseModel):
    last_reviewed_time: Optional[datetime.datetime] = None
    added_at: Optional[datetime.datetime] = None


class AnnotationQueueBulkDeleteRunsRequest(BaseModel):
    delete_all: bool = False
    run_ids: Optional[list[UUID]] = None
    exclude_run_ids: Optional[list[UUID]] = None

    @model_validator(mode="after")
    def validate_fields(self):
        if self.run_ids and self.exclude_run_ids:
            raise ValueError("Cannot specify both run_ids and exclude_run_ids")
        if not self.delete_all and self.exclude_run_ids:
            raise ValueError("exclude_run_ids can only be used with delete_all=true")
        if self.delete_all and self.run_ids:
            raise ValueError("run_ids can only be used with delete_all=false")
        return self


class QueryParamsForAnnotationQueueSchema(BaseModel):
    ids: list[UUID] | None = None
    name: str | None = None
    name_contains: str | None = None
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    tag_value_id: list[UUID] | None = None
    dataset_id: UUID | None = None


class QueryParamsForAnnotationQueueRunSchema(BaseModel):
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=100, ge=1, le=100)
    archived: bool | None = None
    include_stats: bool | None = None


class AnnotationQueueSizeSchema(BaseModel):
    """Size of an Annotation Queue"""

    size: int


class BatchIngestConfig(BaseSettings):
    """Batch ingest config."""

    model_config = SettingsConfigDict(
        env_file=ENV_FILE_PATH, env_prefix="BATCH_INGEST_", extra="ignore"
    )

    use_multipart_endpoint: bool = Field(default=True)
    scale_up_qsize_trigger: int = Field(default=1000)
    scale_up_nthreads_limit: int = Field(default=16)
    scale_down_nempty_trigger: int = Field(default=4)
    size_limit: int = Field(default=100)
    # 20 MB
    size_limit_bytes: int = Field(default=20_971_520)


class CustomerInfo(BaseModel):
    """Customer info."""

    customer_id: str
    customer_name: str


# This should only include instance-wide information, and is cached globally in cloud.
class InfoGetResponse(BaseModel):
    """The LangSmith server info."""

    version: str
    license_expiration_time: datetime.datetime | None = None
    batch_ingest_config: BatchIngestConfig = Field(default_factory=BatchIngestConfig)
    instance_flags: dict[str, Any] = Field(default_factory=dict)
    customer_info: CustomerInfo | None = None


class HealthInfoGetResponse(BaseModel):
    """The LangSmith server info."""

    clickhouse_disk_free_pct: float


class ModelPriceMapSchema(BaseModel):
    """Model price map schema."""

    id: UUID = Field(default_factory=uuid4)
    name: str
    start_time: datetime.datetime | None = None
    tenant_id: UUID | None = None

    match_path: list[str] = [
        "model",
        "model_name",
        "model_id",
        "model_path",
        "endpoint_name",
    ]
    match_pattern: str

    prompt_cost: Decimal
    completion_cost: Decimal
    prompt_cost_details: dict[str, Decimal] | None = None
    completion_cost_details: dict[str, Decimal] | None = None

    provider: str | None = None


class ModelPriceMapCreateSchema(BaseModel):
    """Model price map create schema."""

    name: str
    start_time: datetime.datetime | None = None
    match_path: list[str] = [
        "model",
        "model_name",
        "model_id",
        "model_path",
        "endpoint_name",
    ]
    match_pattern: str

    prompt_cost: Decimal
    completion_cost: Decimal
    prompt_cost_details: dict[str, Decimal] | None = None
    completion_cost_details: dict[str, Decimal] | None = None

    provider: str | None = None


class ModelPriceMapUpdateSchema(ModelPriceMapCreateSchema):
    """Model price map update schema."""


class StripeSetupIntentResponse(BaseModel):
    """Stripe setup intent response."""

    client_secret: str


class StripeCustomerAddress(BaseModel):
    """Stripe customer address."""

    line1: str
    line2: str | None = None
    city: str
    state: str | None = None
    postal_code: str
    country: str


class StripeCustomerBillingInfo(BaseModel):
    """Stripe customer billing information."""

    name: str
    address: StripeCustomerAddress


class StripeBusinessBillingInfo(BaseModel):
    """Stripe customer billing information."""

    name: str
    address: Optional[StripeCustomerAddress] = None


class StripeBusinessInfo(BaseModel):
    company_info: Optional[StripeBusinessBillingInfo] = None
    tax_id: StripeTaxId | None = None
    invoice_email: Optional[str] = None
    is_business: bool = False


class StripeTaxId(BaseModel):
    """Stripe tax ID."""

    value: str
    type: str


class StripePaymentInformation(BaseModel):
    """Stripe payment information."""

    billing_info: StripeCustomerBillingInfo
    setup_intent: str


class PaymentPlanTier(str, Enum):
    no_plan = "no_plan"
    developer = "developer"
    plus = "plus"
    enterprise = "enterprise"
    developer_legacy = "developer_legacy"
    plus_legacy = "plus_legacy"
    free = "free"
    enterprise_legacy = "enterprise_legacy"
    startup = "startup"
    partner = "partner"
    premier = "premier"


class StripePaymentMethodInfo(BaseModel):
    """Stripe customer billing info."""

    brand: str | None = None
    last4: str | None = None
    exp_month: int | None = None
    exp_year: int | None = None
    email: str | None = None


class OrganizationCreate(BaseModel):
    """Create organization schema."""

    # this must not include colon (':') which is the separator used for SCIM group names
    display_name: str = Field(..., min_length=1, pattern=r"^[a-zA-Z0-9\-_ ]+$")
    is_personal: bool


class OrganizationUpdate(BaseModel):
    """Update organization schema."""

    display_name: str = Field(None, min_length=1, pattern=r"^[a-zA-Z0-9\-_ ]+$")
    public_sharing_disabled: bool | None = None
    unshare_all: bool | None = None
    jit_provisioning_enabled: bool | None = None

    @model_validator(mode="after")
    def check_fields(self) -> Self:
        if (
            self.display_name is None
            and self.public_sharing_disabled is None
            and self.unshare_all is None
            and self.jit_provisioning_enabled is None
        ):
            raise HTTPException(
                status_code=422,
                detail="At least one field is required to update",
            )

        return self


class Wallet(BaseModel):
    credit_balance_micros: int
    inflight_balance_micros: int


class Organization(BaseModel):
    """Information about an organization."""

    id: UUID | None = None
    display_name: str | None = None
    config: OrganizationConfig
    connected_to_stripe: bool
    connected_to_metronome: bool
    is_personal: bool
    tier: PaymentPlanTier | None = None
    payment_method: StripePaymentMethodInfo | None = None
    has_cancelled: bool
    end_of_billing_period: datetime.datetime | None = None
    current_plan: CustomerVisiblePlanInfo | None = None
    upcoming_plan: CustomerVisiblePlanInfo | None = None
    reached_max_workspaces: bool = False
    permissions: list[str] = []
    marketplace_payouts_enabled: bool = False
    wallet: Wallet | None = None


class OrganizationInfo(BaseModel):
    """Information about an organization."""

    id: UUID | None = None
    display_name: str | None = None
    config: OrganizationConfig
    is_personal: bool
    tier: PaymentPlanTier | None = None
    reached_max_workspaces: bool = False
    permissions: list[str] = []
    disabled: bool = False
    sso_only: bool = False
    jit_provisioning_enabled: bool = True
    sso_login_slug: str | None = None
    public_sharing_disabled: bool = False
    marketplace_payouts_enabled: bool = False
    wallet: Wallet | None = None


class OrganizationBillingInfo(BaseModel):
    """Information about an organization's billing configuration."""

    id: UUID | None = None
    display_name: str
    config: OrganizationConfig
    connected_to_stripe: bool
    connected_to_metronome: bool
    is_personal: bool
    tier: PaymentPlanTier | None = None
    payment_method: StripePaymentMethodInfo | None = None
    end_of_billing_period: datetime.datetime | None = None
    current_plan: CustomerVisiblePlanInfo | None = None
    upcoming_plan: CustomerVisiblePlanInfo | None = None
    reached_max_workspaces: bool = False
    disabled: bool = False


class OrganizationPGSchemaSlim(BaseModel):
    """Schema for an organization in postgres for list views."""

    id: UUID
    display_name: str
    created_at: datetime.datetime | None = None
    created_by_user_id: UUID | None = None
    modified_at: datetime.datetime | None = None
    is_personal: bool
    disabled: bool
    sso_login_slug: str | None = None
    sso_only: bool = False
    jit_provisioning_enabled: bool = True
    public_sharing_disabled: bool = False


class OrganizationPGSchema(OrganizationPGSchemaSlim):
    """Schema for an organization in postgres."""

    stripe_customer_id: str | None = None
    stripe_connected_account_id: str | None = None
    metronome_customer_id: str | None = None


class OrganizationWithWalletPGSchema(OrganizationPGSchema):
    """Schema for an organization in postgres."""

    wallet: Wallet | None = None


class OrganizationDashboardSchema(BaseModel):
    """Organization dashboard for usage or invoices."""

    embeddable_url: str


class OrganizationDashboardType(str, Enum):
    """Enum for acceptable types of dashboards."""

    invoices = "invoices"
    usage = "usage"
    credits = "credits"


class OrganizationDashboardColorScheme(str, Enum):
    """Enum for acceptable color schemes of dashboards."""

    light = "light"
    dark = "dark"


class CustomerVisiblePlanInfo(BaseModel):
    """Customer visible plan information."""

    tier: PaymentPlanTier
    started_on: datetime.datetime
    ends_on: datetime.datetime | None = None


class PaymentPlanInfo(CustomerVisiblePlanInfo):
    """Payment plan information."""

    id: str
    plan_id: str

    def to_customer_visible_plan_info(self) -> CustomerVisiblePlanInfo:
        return CustomerVisiblePlanInfo(
            tier=self.tier,
            started_on=self.started_on,
            ends_on=self.ends_on,
        )


class RelevantPlansForCustomer(BaseModel):
    """Relevant plans for a customer."""

    current_plan: PaymentPlanInfo | None = None
    upcoming_plan: PaymentPlanInfo | None = None


class ChangePaymentPlanReq(str, Enum):
    """Enum for payment plans that the user can change to. Developer plans are permanent and enterprise plans will be changed manually."""

    disabled = "disabled"
    developer = "developer"
    plus = "plus"
    startup = "startup"
    partner = "partner"
    premier = "premier"
    free = "free"

    def to_payment_plan_tier(self) -> PaymentPlanTier:
        return (
            PaymentPlanTier(self.value)
            if self != "disabled"
            else PaymentPlanTier.no_plan
        )


class ChangePaymentPlanSchema(BaseModel):
    """Change payment plan schema."""

    tier: ChangePaymentPlanReq


class FeedbackIngestTokenSchema(BaseModel):
    """Feedback ingest token schema."""

    id: UUID
    url: str
    expires_at: datetime.datetime
    feedback_key: str


class FeedbackIngestTokenCreateSchema(BaseModel):
    """Feedback ingest token create schema."""

    expires_in: TimedeltaInput | None = None
    expires_at: datetime.datetime | None = None

    run_id: UUID
    feedback_key: str
    feedback_config: FeedbackConfig | None = None


class FeedbackCreateWithTokenExtendedSchema(BaseModel):
    """Feedback create schema with token."""

    score: float | int | bool | None = None
    """Value or score to assign the run."""
    value: float | int | bool | str | None = None
    """The display value for the feedback if not a metric."""
    comment: str | None = None
    """Comment or explanation for the feedback."""
    correction: dict | str | None = None
    """Correction for the run."""
    metadata: dict[str, Any] | None = None
    """Metadata for the feedback, eg. user_id, etc."""


class EvaluatorStructuredOutput(BaseModel):
    """Evaluator structured output schema."""

    hub_ref: str | None = None  # eg. efriis/my-prompt:latest

    prompt: List[tuple[str, str]] | None = None
    template_format: str | None = None
    tool_schema: dict[str, Any] | None = Field(None, alias="schema")
    variable_mapping: dict[str, str] | None = None

    model: dict[str, Any] | None = None  # serialized chat_model

    @model_validator(mode="after")
    def check_fields(self) -> Self:
        if not self.hub_ref:
            if not self.prompt:
                raise ValueError("prompt is required when hub_ref is not provided")
            if not self.tool_schema:
                raise ValueError("tool_schema is required when hub_ref is not provided")
        # Validate template_format
        if self.template_format and self.template_format not in [
            "f-string",
            "mustache",
        ]:
            raise ValueError('template_format must be either "f-string" or "mustache"')

        return self

    @property
    def schema(self) -> dict[str, Any] | None:
        return self.tool_schema


class EvaluatorTopLevel(BaseModel):
    structured: EvaluatorStructuredOutput


class CodeEvaluatorTopLevel(BaseModel):
    code: str


class RunRulesAlertType(str, Enum):
    """Enum for alert types."""

    pagerduty = "pagerduty"


class RunRulesAlertSchema(BaseModel):
    type: RunRulesAlertType | None = RunRulesAlertType.pagerduty


class PagerdutySeverity(str, Enum):
    """Enum for severity."""

    critical = "critical"
    warning = "warning"
    error = "error"
    info = "info"


class RunRulesPagerdutyAlertSchema(RunRulesAlertSchema):
    routing_key: str
    summary: str | None = None
    severity: PagerdutySeverity | None = PagerdutySeverity.warning


class RunRulesWebhookSchema(BaseModel):
    url: str
    headers: dict[str, str] | None = None


class RunRulesSchema(BaseModel):
    """Run rules schema."""

    id: UUID
    tenant_id: UUID
    is_enabled: bool = True
    session_id: UUID | None = None
    session_name: str | None = None
    dataset_id: UUID | None = None
    dataset_name: str | None = None
    display_name: str

    sampling_rate: float
    filter: str | None = None
    trace_filter: str | None = None
    tree_filter: str | None = None

    add_to_annotation_queue_id: UUID | None = None
    add_to_annotation_queue_name: str | None = None

    add_to_dataset_id: UUID | None = None
    add_to_dataset_name: str | None = None
    add_to_dataset_prefer_correction: bool = False

    corrections_dataset_id: UUID | None = None
    use_corrections_dataset: bool = False
    num_few_shot_examples: int | None = None

    evaluators: list[EvaluatorTopLevel] | None = None
    code_evaluators: list[CodeEvaluatorTopLevel] | None = None
    alerts: list[RunRulesPagerdutyAlertSchema] | None = Field(default_factory=list)
    webhooks: list[RunRulesWebhookSchema] | None
    extend_only: bool = False

    created_at: datetime.datetime
    updated_at: datetime.datetime
    backfill_from: datetime.datetime | None = None
    transient: bool = False

    evaluator_version: int
    evaluator_id: UUID | None = None

    alignment_annotation_queue_id: UUID | None = None

    model_config = ConfigDict(from_attributes=True)

    @property
    def at_least_v3(self) -> bool:
        return (self.evaluator_version or 0) >= 3


class RuleLogActionOutcome(str, Enum):
    success = "success"
    skipped = "skipped"
    error = "error"


class RuleLogActionResponse(BaseModel):
    outcome: RuleLogActionOutcome
    payload: dict[str, Any] | None = None


class RuleLogSchema(BaseModel):
    """Run rules log schema."""

    rule_id: UUID

    run_id: UUID
    run_name: str | None = None
    run_type: str | None = None
    run_session_id: UUID | None = None

    start_time: datetime.datetime
    end_time: datetime.datetime
    application_time: datetime.datetime | None = None

    add_to_annotation_queue: RuleLogActionResponse | None = None
    add_to_dataset: RuleLogActionResponse | None = None
    evaluators: RuleLogActionResponse | None = None
    alerts: RuleLogActionResponse | None = None
    webhooks: RuleLogActionResponse | None = None


class RunRulesCreateSchema(BaseModel):
    display_name: str
    session_id: UUID | None = None
    is_enabled: bool = True
    dataset_id: UUID | None = None
    sampling_rate: float
    filter: str | None = None
    trace_filter: str | None = None
    tree_filter: str | None = None
    backfill_from: datetime.datetime | None = None
    use_corrections_dataset: bool = False
    num_few_shot_examples: int | None = None
    extend_only: bool = False
    transient: bool = False

    add_to_annotation_queue_id: UUID | None = None
    add_to_dataset_id: UUID | None = None
    add_to_dataset_prefer_correction: bool = False
    evaluators: list[EvaluatorTopLevel] | None = None
    code_evaluators: list[CodeEvaluatorTopLevel] | None = None
    alerts: list[RunRulesPagerdutyAlertSchema] | None = Field(default_factory=list)
    webhooks: list[RunRulesWebhookSchema] | None = None
    evaluator_version: int | None = None

    @model_validator(mode="after")
    def check_session_or_dataset(self) -> Self:
        if self.session_id is None and self.dataset_id is None:
            raise ValueError("session_id or dataset_id is required")
        elif self.session_id is not None and self.dataset_id is not None:
            raise ValueError("session_id and dataset_id cannot be provided together")
        if self.add_to_dataset_id is None and self.add_to_dataset_prefer_correction:
            raise ValueError(
                "add_to_dataset_id is required when add_to_dataset_prefer_correction is True"
            )
        return self

    @property
    def at_least_v3(self) -> bool:
        return (self.evaluator_version or 0) >= 3


class TriggerRulesRequest(BaseModel):
    rule_ids: list[UUID] | None = None
    dataset_id: UUID | None = None

    @model_validator(mode="after")
    def check_rule_ids_or_dataset_id(self) -> Self:
        if self.rule_ids is None and self.dataset_id is None:
            raise ValueError("rule_ids or dataset_id is required")
        elif self.rule_ids is not None and self.dataset_id is not None:
            raise ValueError("rule_ids and dataset_id cannot be provided together")
        return self


class RunGroupBy(str, Enum):
    conversation = "conversation"


class RunGroupRequest(BaseModel):
    session_id: UUID
    group_by: RunGroupBy

    filter: str | None = None

    start_time: datetime.datetime | None = None
    end_time: datetime.datetime | None = None

    # pagination
    offset: int = Field(default=0, ge=0)
    limit: int = Field(default=10, ge=1, le=100)


class RunGroupBase(BaseModel):
    filter: str
    count: int | None = None
    total_tokens: int | None = None
    total_cost: Decimal | None = None
    min_start_time: datetime.datetime | None = None
    max_start_time: datetime.datetime | None = None
    latency_p50: datetime.timedelta | None = None
    latency_p99: datetime.timedelta | None = None
    feedback_stats: dict[str, Any] | None = None


class RunGroup(RunGroupBase):
    group_key: str
    first_inputs: dict[str, Any] | str | None = None
    last_outputs: dict[str, Any] | str | None = None
    last_error: str | None = None
    first_run_id: UUID | None = None
    last_run_id: UUID | None = None


class ExampleWithRunsGroup(RunGroupBase):
    """Group of examples with runs."""

    group_key: Any
    examples: list[ExampleWithRuns] | list[ExampleWithRunsCH]
    prompt_tokens: int | None = None
    completion_tokens: int | None = None
    prompt_cost: Decimal | None = None
    completion_cost: Decimal | None = None
    error_rate: float | None = None


class GroupedExamplesWithRunsResponse(BaseModel):
    """Grouped examples with runs."""

    groups: list[ExampleWithRunsGroup]


class RunGroupResponse(BaseModel):
    groups: List[RunGroup]
    total: int


class Secret(BaseModel):
    key: str
    value: str


class SecretKey(BaseModel):
    key: str


class SecretUpsert(BaseModel):
    key: str
    value: str | None


class InternalSecretsRequest(BaseModel):
    secret_key: str


class InternalSecretsResponse(BaseModel):
    encrypted_secrets: str


class FilterValidationRequest(BaseModel):
    filter: str


class TenantShareRunToken(BaseModel):
    type: Literal["run"]
    share_token: str
    created_at: datetime.datetime

    run_id: UUID
    run_name: str | None = None
    run_type: str | None = None

    session_id: UUID | None = None
    session_name: str | None = None


class TenantShareDatasetToken(BaseModel):
    type: Literal["dataset"]
    share_token: str
    created_at: datetime.datetime

    dataset_id: UUID
    dataset_name: str | None = None


class TenantShareTokensResponse(BaseModel):
    entities: List[
        Annotated[
            Union[TenantShareRunToken, TenantShareDatasetToken],
            Field(..., discriminator="type"),
        ]
    ]


class TenantBulkUnshareRequest(BaseModel):
    share_tokens: list[UUID] = Field(..., default_factory=list, min_length=1)


class Role(BaseModel):
    id: UUID
    name: str
    display_name: str
    description: str
    organization_id: UUID | None = None
    permissions: list[str]
    access_scope: AccessScope | None = None


def validate_permissions(permissions: List[str]) -> List[str]:
    for permission in permissions:
        try:
            Permissions(permission)
        except ValueError:
            raise ValueError(f"Invalid permission: {permission}")
    if Permissions.WORKSPACES_READ.value not in permissions:
        raise ValueError("WORKSPACES_READ permission is required.")
    return permissions


PermissionsInput = Annotated[List[str], BeforeValidator(validate_permissions)]


def validate_role_display_name(display_name: str) -> None:
    if ":" in display_name:
        raise ValueError("display_name cannot contain colons")


class CreateRoleRequest(BaseModel):
    # prevent colons in display_name to future-proof SCIM group names
    display_name: str
    description: str
    permissions: PermissionsInput

    @model_validator(mode="after")
    def check_display_name(self) -> Self:
        validate_role_display_name(self.display_name)
        return self


class UpdateRoleRequest(BaseModel):
    # prevent colons in display_name to future-proof SCIM group names
    display_name: str
    description: str
    permissions: PermissionsInput

    @model_validator(mode="after")
    def check_display_name(self) -> Self:
        validate_role_display_name(self.display_name)
        return self


class PermissionResponse(BaseModel):
    name: str
    description: str
    access_scope: AccessScope


class PlaygroundSavedOptions(BaseModel):
    requests_per_second: int | None = None


class PlaygroundSettingsCreateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    settings: dict[str, Any]
    options: PlaygroundSavedOptions | None = None


class PlaygroundSettingsUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    settings: Optional[dict[str, Any]] = None
    options: PlaygroundSavedOptions | None = None


class PlaygroundSettingsResponse(BaseModel):
    id: UUID
    settings: dict[str, Any]
    options: PlaygroundSavedOptions | None = None
    name: Optional[str] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    description: Optional[str] = None


class SSOProviderSlim(BaseModel):
    provider_id: UUID
    organization_id: UUID
    organization_display_name: str


class SSOProvider(BaseModel):
    id: UUID
    organization_id: UUID
    provider_id: UUID
    default_workspace_role_id: UUID
    default_workspace_ids: List[UUID]
    metadata_url: Optional[str] = None
    metadata_xml: Optional[str] = None


class SSOProviderWithSettings(SSOProvider):
    jit_provisioning_enabled: bool


class SSOProviderCreateInternal(BaseModel):
    provider_id: UUID
    default_workspace_role_id: UUID
    default_workspace_ids: List[UUID]
    metadata_url: Optional[str] = None
    metadata_xml: Optional[str] = None


class SSOSettingsUpdate(BaseModel):
    default_workspace_role_id: Optional[UUID] = None
    default_workspace_ids: Optional[List[UUID]] = None
    metadata_url: Optional[str] = None
    metadata_xml: Optional[str] = None

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if (
            "default_workspace_role_id" not in self.model_fields_set
            and "default_workspace_ids" not in self.model_fields_set
            and "metadata_url" not in self.model_fields_set
            and "metadata_xml" not in self.model_fields_set
            and "sso_only" not in self.model_fields_set
        ):
            raise ValueError("At least one parameter must be provided")
        if (
            "default_workspace_role_id" in self.model_fields_set
            and not self.default_workspace_role_id
        ):
            raise ValueError("default_workspace_role_id cannot be empty")
        if (
            "default_workspace_ids" in self.model_fields_set
            and not self.default_workspace_ids
        ):
            raise ValueError("default_workspace_ids cannot be empty")
        if self.metadata_xml and self.metadata_url:
            raise ValueError(
                "Only one of metadata_xml or metadata_url should be provided"
            )
        return self


class AllowedLoginMethodsUpdate(BaseModel):
    sso_only: bool


class SSOSettingsCreate(BaseModel):
    default_workspace_role_id: UUID
    default_workspace_ids: List[UUID]
    metadata_xml: Optional[str] = None
    metadata_url: Optional[str] = None
    attribute_mapping: Dict[str, str] = Field(default_factory=dict)

    @model_validator(mode="after")
    def validate_metadata(self) -> Self:
        if not self.metadata_xml and not self.metadata_url:
            raise ValueError("Either metadata_xml or metadata_url must be provided")
        if self.metadata_xml and self.metadata_url:
            raise ValueError(
                "Only one of metadata_xml or metadata_url should be provided"
            )

        return self


class FilterViewType(str, Enum):
    RUNS = "runs"
    THREADS = "threads"


class FilterViewBase(BaseModel):
    filter_string: str | None = None
    trace_filter_string: str | None = None
    tree_filter_string: str | None = None
    display_name: str
    description: str | None = None
    type: FilterViewType = FilterViewType.RUNS


class FilterViewCreate(FilterViewBase):
    pass


class FilterViewUpdate(BaseModel):
    filter_string: str | None = None
    display_name: str | None = None
    description: str | None = None
    trace_filter_string: str | None = None
    tree_filter_string: str | None = None
    type: FilterViewType | None = None


class FilterView(FilterViewBase):
    id: UUID
    session_id: UUID | None = None
    created_at: datetime.datetime
    updated_at: datetime.datetime


class ResourceType(str, Enum):
    PROMPT = "prompt"
    PROJECT = "project"
    QUEUE = "queue"
    DEPLOYMENT = "deployment"
    EXPERIMENT = "experiment"
    DATASET = "dataset"
    DASHBOARD = "dashboard"


class TagKeyCreate(BaseModel):
    key: str = Field(max_length=255, min_length=1)
    description: str | None = None


class TagKey(TagKeyCreate):
    id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime


class TagKeyWithValues(TagKey):
    values: List[TagValue] = Field(default_factory=list)


class TagKeyWithValuesAndTaggings(TagKeyWithValues):
    values: List[TagValueWithTaggings] = Field(default_factory=list)


class TagKeyUpdate(BaseModel):
    key: str | None = Field(max_length=255, min_length=1, default=None)
    description: str | None = None


class TagValueCreate(BaseModel):
    value: str = Field(max_length=255, min_length=1)
    description: str | None = None


class TagValueUpdate(BaseModel):
    value: str | None = Field(max_length=255, min_length=1, default=None)
    description: str | None = None


class TagValue(TagValueCreate):
    id: UUID
    tag_key_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime


class TagValueWithTaggings(TagValue):
    taggings: List[Tagging] = Field(default_factory=list)


class TaggingCreate(BaseModel):
    tag_value_id: UUID
    resource_type: ResourceType
    resource_id: UUID


class Tagging(TaggingCreate):
    id: UUID
    created_at: datetime.datetime


class ListTagsForResourceRequest(BaseModel):
    resource_id: UUID
    resource_type: ResourceType


class Resource(BaseModel):
    tagging_id: UUID
    resource_name: str
    resource_id: UUID


class TaggingsByResourceType(BaseModel):
    prompts: List[Resource] = []
    projects: List[Resource] = []
    queues: List[Resource] = []
    deployments: List[Resource] = []
    experiments: List[Resource] = []
    datasets: List[Resource] = []
    dashboards: List[Resource] = []


class TaggingsResponse(BaseModel):
    tag_key: str
    tag_key_id: UUID
    tag_value: str
    tag_value_id: UUID
    resources: TaggingsByResourceType


class GenerateSyntheticExamplesBody(BaseModel):
    example_ids: Optional[List[UUID]] = None
    num_examples: int

    @model_validator(mode="after")
    def validate_fields(self) -> "GenerateSyntheticExamplesBody":
        if self.example_ids is not None and len(self.example_ids) > 10:
            raise ValueError("Number of example ids must be 10 or less.")

        if self.num_examples < 1 or self.num_examples > 10:
            raise ValueError("Number of examples must be between 1 and 10.")

        return self


class TraceTransactionSource(str, Enum):
    """
    Represents where the transaction originated (e.g. is it a local
    transaction or a transaction from a remote self hosted deployment).
    """

    local = "local"
    remote_self_hosted = "remote_self_hosted"


class TraceTransaction(BaseModel):
    id: UUID
    tenant_id: UUID
    session_id: UUID
    trace_count: int
    start_insertion_time: datetime.datetime
    end_insertion_time: datetime.datetime
    start_interval_time: datetime.datetime
    end_interval_time: datetime.datetime
    status: str
    num_failed_send_attempts: int = 0
    transaction_type: str = "all_traces"
    source: TraceTransactionSource = TraceTransactionSource.local


class TraceTransactionWithOrg(TraceTransaction):
    organization_id: UUID
    # If the trace transaction is from a remote self-hosted deployment, this field will be populated
    self_hosted_customer_id: UUID | None = None


class NodesTransaction(BaseModel):
    id: UUID
    # This can be empty for self-hosted customers
    tenant_id: UUID | None = None
    project_id: UUID | None = None
    project_created_at: datetime.datetime | None = None
    api_key: str | None = None
    remote_reconciled: bool = False
    nodes_count: int
    deduped_standby_minutes: int
    deployment_type: str
    start_insertion_time: datetime.datetime
    end_insertion_time: datetime.datetime
    start_interval_time: datetime.datetime
    end_interval_time: datetime.datetime
    status: str
    num_failed_send_attempts: int = 0
    transaction_type: str = "nodes_executed"
    source: TraceTransactionSource = TraceTransactionSource.local


class NodesTransactionWithOrg(NodesTransaction):
    # This can be empty for self-hosted customers
    organization_id: UUID | None = None
    # If the nodes transaction is from a remote self-hosted deployment, this field will be populated
    self_hosted_customer_id: UUID | None = None


class BulkExportDestinationType(str, Enum):
    S3 = "s3"


# Value must match one of https://clickhouse.com/docs/en/interfaces/formats
class BulkExportFormat(str, Enum):
    PARQUET = "Parquet"


# Value must match one from https://pypi.org/project/fastparquet
class BulkExportCompression(str, Enum):
    NONE = "none"
    GZIP = "gzip"
    SNAPPY = "snappy"


class BulkExportDestinationS3Config(BaseModel):
    endpoint_url: Optional[str] = Field(None, max_length=2048)
    prefix: str = Field("", max_length=2048)
    bucket_name: Optional[str] = Field(None, min_length=3, max_length=63)
    region: Optional[str] = Field(None, min_length=1)

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if not self.bucket_name and not self.endpoint_url:
            raise ValueError("Either bucket_name or endpoint_url must be provided")

        return self


class BulkExportDestinationS3Credentials(BaseModel):
    access_key_id: str = Field(..., min_length=1, max_length=255)
    secret_access_key: str = Field(..., min_length=1, max_length=2048)
    session_token: Optional[str] = Field(None, max_length=2048)


class BulkExportDestinationBase(BaseModel):
    destination_type: BulkExportDestinationType = BulkExportDestinationType.S3
    display_name: str = Field(..., min_length=1, pattern=r"^[a-zA-Z0-9\-_ ']+$")
    config: BulkExportDestinationS3Config


class BulkExportDestinationCreate(BulkExportDestinationBase):
    # These are stored encrypted
    credentials: Optional[BulkExportDestinationS3Credentials] = None


class BulkExportDestinationInternal(BulkExportDestinationBase):
    decrypted_credentials: Optional[BulkExportDestinationS3Credentials] = None


class BulkExportDestination(BulkExportDestinationBase):
    id: UUID
    tenant_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime
    # Show the keys but not the values
    credentials_keys: List[str]


class BulkExportStatus(str, Enum):
    # Cancelled by the user
    CANCELLED = "Cancelled"
    # Completed successfully
    COMPLETED = "Completed"
    # Created but not yet running (no runs created yet)
    CREATED = "Created"
    # Interval export is scheduled to run every X hours unless cancelled or hard-deleted.
    INTERVAL_SCHEDULED = "IntervalScheduled"
    # Failed and not retryable
    FAILED = "Failed"

    TIMEDOUT = "TimedOut"
    RUNNING = "Running"


def _validate_bulk_export_interval(interval_hours: int):
    if (
        interval_hours < settings.BULK_EXPORT_INTERVAL_HOURS_MIN
        or interval_hours > settings.BULK_EXPORT_INTERVAL_HOURS_MAX
    ):
        raise ValueError(
            f"Bulk export interval_hours must be between {settings.BULK_EXPORT_INTERVAL_HOURS_MIN} and {settings.BULK_EXPORT_INTERVAL_HOURS_MAX} inclusive"
        )


class BulkExportBase(BaseModel):
    bulk_export_destination_id: UUID
    session_id: UUID
    start_time: datetime.datetime
    end_time: datetime.datetime | None = None
    filter: str | None = None
    format: BulkExportFormat = BulkExportFormat.PARQUET
    compression: BulkExportCompression = BulkExportCompression.GZIP
    interval_hours: int | None = None


class BulkExportCreate(BulkExportBase):
    @model_validator(mode="after")
    def validate_interval(self):
        if self.interval_hours is not None:
            _validate_bulk_export_interval(self.interval_hours)
        return self


class BulkExportCreateInternal(BulkExportCreate):
    # Bulk exports for scheduled exports are created internally.
    # this field is not settable by users.
    source_bulk_export_id: UUID | None = None


class BulkExport(BulkExportBase):
    id: UUID
    tenant_id: UUID
    status: BulkExportStatus
    created_at: datetime.datetime
    updated_at: datetime.datetime
    finished_at: Optional[datetime.datetime]
    source_bulk_export_id: Optional[UUID] = None


# We only support cancelling a bulk export for now
class BulkExportUpdate(BaseModel):
    status: Literal[BulkExportStatus.CANCELLED]


class BulkExportUpdateInternal(BaseModel):
    status: Optional[BulkExportStatus] = None
    metadata: Optional[Dict] = None
    errors: Optional[Dict] = None
    finished_at: Optional[datetime.datetime] = None
    interval_hours: Optional[int] = None

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if (
            "status" not in self.model_fields_set
            and "metadata" not in self.model_fields_set
            and "errors" not in self.model_fields_set
            and "finished_at" not in self.model_fields_set
            and "interval_hours" not in self.model_fields_set
        ):
            raise ValueError("At least one parameter must be provided")

        if self.interval_hours is not None:
            _validate_bulk_export_interval(self.interval_hours)

        return self


# Runs are only used internally
class BulkExportRunStatus(str, Enum):
    # Cancelled by the user
    CANCELLED = "Cancelled"
    # Completed successfully
    COMPLETED = "Completed"
    # Created but not yet running
    CREATED = "Created"
    # Failed and not retryable
    FAILED = "Failed"
    # Run has timed out
    TIMEDOUT = "TimedOut"
    # Run is currently in progress
    RUNNING = "Running"


class BulkExportRunProgress(BaseModel):
    rows_written: int
    exported_files: list[str]
    export_path: str
    latest_cursor: Optional[str]


class BulkExportRunMetadata(BaseModel):
    prefix: str
    start_time: datetime.datetime
    end_time: datetime.datetime
    result: Optional[BulkExportRunProgress] = None


class BulkExportRunBase(BaseModel):
    bulk_export_id: UUID
    metadata: BulkExportRunMetadata


class BulkExportRunCreate(BulkExportRunBase):
    pass


class BulkExportRunCreateBatchItem(BaseModel):
    metadata: BulkExportRunMetadata


class BulkExportRunCreateBatch(BaseModel):
    bulk_export_id: UUID
    creates: List[BulkExportRunCreateBatchItem]


class BulkExportRunUpdateBatchItem(BaseModel):
    id: UUID
    status: Optional[BulkExportRunStatus] = None
    metadata: Optional[BulkExportRunMetadata] = None
    errors: Optional[dict] = None
    retry_number: Optional[int] = None
    finished_at: Optional[datetime.datetime] = None

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if (
            "status" not in self.model_fields_set
            and "metadata" not in self.model_fields_set
            and "errors" not in self.model_fields_set
            and "retry_number" not in self.model_fields_set
            and "finished_at" not in self.model_fields_set
        ):
            raise ValueError("At least one parameter must be provided")

        if self.status == BulkExportRunStatus.COMPLETED and not self.finished_at:
            raise ValueError("finished_at is required when status is Completed")
        if self.finished_at and self.status != BulkExportRunStatus.COMPLETED:
            raise ValueError("finished_at is only allowed when status is Completed")

        return self


class BulkExportRunUpdateBatch(BaseModel):
    bulk_export_id: UUID
    updates: List[BulkExportRunUpdateBatchItem]


class BulkExportRun(BulkExportRunBase):
    id: UUID
    status: BulkExportRunStatus
    retry_number: int = 0
    errors: Optional[Dict] = None
    created_at: datetime.datetime
    updated_at: datetime.datetime
    finished_at: Optional[datetime.datetime]


class PlaygroundOptimizePromptRequestSchema(BaseModel):
    prompt: str
    highlight_start: int | None = None
    highlight_end: int | None = None
    improvement_instruction: str
    template_format: Literal["f-string", "mustache"]


class LastSentEmail(BaseModel):
    ls_user_id: UUID
    email: str
    last_onboarding_email_id: str | None


class PendingEmailToSend(BaseModel):
    ls_user_id: UUID
    email: str
    last_onboarding_email_id: str | None
    this_onboarding_email_id: str


# Credit purchases
class StripeCheckoutSessionsCreate(BaseModel):
    amount_cents: int
    success_path: str


class StripeCheckoutSessionsConfirm(BaseModel):
    stripe_session_id: str


# Payouts


class StripeAccountLinksCreate(BaseModel):
    success_path: str


# Prompt Optimization Jobs


class EPromptOptimizationAlgorithm(str, Enum):
    promptim = "promptim"
    demo = "demo"


class PromptimConfig(BaseModel):
    message_index: int
    task_description: str
    dataset_name: str
    train_split: str | None
    dev_split: str | None
    test_split: str | None
    evaluators: List[UUID]
    num_epochs: int
    auto_commit: bool


class DemoConfig(BaseModel):
    message_index: int
    metaprompt: dict
    examples: List[dict]
    overall_feedback: str | None


class PromptOptimizationJobCreate(BaseModel):
    algorithm: EPromptOptimizationAlgorithm
    config: Union[PromptimConfig, DemoConfig]

    @model_validator(mode="after")
    def validate_and_transform(self):
        """Validate and transform the `config` field based on the `algorithm`."""
        if not self.algorithm:
            raise ValueError("Algorithm must be provided before validating the config.")

        config_dict = self.config
        if not isinstance(config_dict, dict):
            config_dict = self.config.model_dump()

        if self.algorithm == EPromptOptimizationAlgorithm.promptim:
            self.config = PromptimConfig(**config_dict)
        elif self.algorithm == EPromptOptimizationAlgorithm.demo:
            self.config = DemoConfig(**config_dict)
        else:
            raise ValueError(f"Unsupported algorithm: {self.algorithm}")

        return self


class OptimizePromptJobRequest(PromptOptimizationJobCreate):
    """Request to optimize a prompt."""

    prompt_name: str


class OptimizeTrainRequest(OptimizePromptJobRequest):
    """Request to train a prompt."""

    optimization_job_id: UUID


class OptimizePromptResponse(BaseModel):
    """Response from optimizing a prompt."""

    optimization_job_id: UUID


class EPromptOptimizationJobStatus(str, Enum):
    CREATED = "created"
    RUNNING = "running"
    SUCCESSFUL = "successful"
    FAILED = "failed"


class PromptOptimizationJobUpdate(BaseModel):
    status: EPromptOptimizationJobStatus | None = None
    result: PromptOptimizationResult | None = None


class PromptOptimizationResult(BaseModel):
    timestamp: datetime.datetime
    x: float
    y: float


class PromptOptimizationJob(BaseModel):
    id: UUID
    repo_id: UUID
    status: EPromptOptimizationJobStatus
    tenant_id: UUID
    algorithm: EPromptOptimizationAlgorithm
    config: Union[PromptimConfig, DemoConfig]
    results: List[PromptOptimizationResult] = Field(default_factory=list)
    created_at: datetime.datetime
    updated_at: datetime.datetime

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if self.status not in EPromptOptimizationJobStatus:
            raise ValueError(f"Invalid status: {self.status}")
        return self


class PromptOptimizationJobWithLogs(PromptOptimizationJob):
    logs: List[PromptOptimizationJobLog]


class EPromptOptimizationJobLogType(str, Enum):
    INFO = "info"
    RESULT = "result"
    ERROR = "error"
    LINK = "link"


class PromptOptimizationJobLogCreate(BaseModel):
    log_type: EPromptOptimizationJobLogType
    message: str
    data: Optional[dict] = None

    @model_validator(mode="after")
    def validate_fields(self) -> Self:
        if self.log_type not in EPromptOptimizationJobLogType:
            raise ValueError(f"Invalid log type: {self.log_type}")
        return self


class PromptOptimizationJobLog(PromptOptimizationJobLogCreate):
    id: UUID
    job_id: UUID
    created_at: datetime.datetime


class WorkspaceMembersResponse(BaseModel):
    members: List[MemberIdentity]
    total: int


class WorkspacePendingMembersResponse(BaseModel):
    members: List[PendingIdentity]
    total: int


class EPromptWebhookTrigger(str, Enum):
    """Valid trigger types for prompt webhooks."""

    COMMIT = "commit"


class PromptWebhookCore(BaseModel):
    url: AnyHttpUrl
    headers: Optional[dict] = None


class PromptWebhookBase(PromptWebhookCore):
    """Base schema for prompt webhooks."""

    include_prompts: Optional[List[UUID]] = None
    exclude_prompts: Optional[List[UUID]] = None
    triggers: List[EPromptWebhookTrigger] = Field(default_factory=list)


class PromptWebhookCreate(PromptWebhookBase):
    """Schema for creating a prompt webhook."""

    id: Optional[UUID] = None


class PromptWebhookUpdate(BaseModel):
    """Schema for updating a prompt webhook."""

    include_prompts: Optional[List[UUID]] = None
    exclude_prompts: Optional[List[UUID]] = None
    url: Optional[AnyHttpUrl] = None
    headers: Optional[dict] = None
    triggers: Optional[List[EPromptWebhookTrigger]] = None


class PromptWebhook(PromptWebhookBase):
    """Schema for a prompt webhook."""

    id: UUID
    tenant_id: UUID
    created_at: datetime.datetime
    updated_at: datetime.datetime

    model_config = {"from_attributes": True, "arbitrary_types_allowed": True}


@dataclass
class PromptWebhookPayload:
    """Schema for the payload of a prompt webhook."""

    prompt_id: str
    prompt_name: str
    manifest: dict
    commit_hash: str
    created_at: str
    created_by: str


class PromptWebhookTest(BaseModel):
    """Schema for testing a prompt webhook."""

    webhook: PromptWebhookBase
    payload: PromptWebhookPayload


class EvaluatorType(str, Enum):
    llm = "llm"


@dataclass
class Evaluator:
    """Evaluator as it is represented in the db."""

    id: UUID
    name: str
    type: EvaluatorType


@dataclass
class LLMEvaluator:
    """LLM Evaluator as it is represented in the db."""

    evaluator_id: UUID
    prompt_id: UUID
    prompt_handle: str | None = None
    commit_hash_or_tag: str | None = None
    variable_mapping: dict | None = None
    annotation_queue_id: UUID | None = None


@dataclass
class EvaluatorReturnSchema(Evaluator):
    """The return schema for an evaluator."""

    evaluator: LLMEvaluator

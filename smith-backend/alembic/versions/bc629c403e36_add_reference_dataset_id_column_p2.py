"""Add reference dataset id column p2

Revision ID: bc629c403e36
Revises: 26759e7b176e
Create Date: 2023-10-20 12:02:09.754748

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "bc629c403e36"
down_revision = "26759e7b176e"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        UPDATE tracer_session
        SET reference_dataset_id = (
            SELECT reference_dataset_ids[1]
            FROM tracer_session_stats
            WHERE tracer_session_stats.tracer_session_id = tracer_session.id
            AND tracer_session_stats.reference_dataset_ids IS NOT NULL
            LIMIT 1
        )
        """
        # TODO: Drop the column after we do this deployment
        # ALTER TABLE tracer_session_stats DROP COLUMN reference_dataset_ids;
    )


def downgrade():
    # In later deployment, do ALTER TABLE tracer_session_stats ADD COLUMN reference_dataset_ids UUID[];
    op.execute(
        """
        UPDATE tracer_session_stats
        SET reference_dataset_ids = CASE
            WHEN 
                (SELECT reference_dataset_id FROM tracer_session WHERE tracer_session.id = tracer_session_stats.tracer_session_id) IS NULL
            THEN NULL
            ELSE ARRAY[
                (SELECT reference_dataset_id FROM tracer_session WHERE tracer_session.id = tracer_session_stats.tracer_session_id)
            ]
        END;
        """
    )

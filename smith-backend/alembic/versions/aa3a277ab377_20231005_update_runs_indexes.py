"""20231005 Update runs indexes

Revision ID: aa3a277ab377
Revises: 5bed6a26f073
Create Date: 2023-10-05 20:24:55.450857

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "aa3a277ab377"
down_revision = "5bed6a26f073"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("""create extension if not exists btree_gin;""")

    with op.get_context().autocommit_block():
        # Re-do trace_id indexes post-migration
        op.execute("drop index concurrently if exists runs_trace_inc_dotted_order_id")
        op.execute(
            """
create index concurrently if not exists runs_id_inc_session_trace_idx on runs (id) include (session_id, trace_id);
"""
        )
        op.execute(
            "drop index concurrently if exists runs_id_inc_session_trace_dotted_order_idx;"
        )
        op.execute(
            """
create index concurrently if not exists runs_trace_inc_id_idx on runs (trace_id) include (id);
"""
        )
        op.execute("""drop index concurrently if exists uq_runs_trace_dotted_order;""")

        # Add indexes on common filters
        op.execute(
            """
create index concurrently if not exists runs_session_name_root on runs (session_id, left(name, 128)) include (id) where execution_order = 1;
"""
        )
        op.execute(
            """
create index concurrently if not exists runs_session_type_root on runs (session_id, run_type) include (id) where execution_order = 1;
"""
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("drop index concurrently if exists runs_session_name_root;")
        op.execute("drop index concurrently if exists runs_session_type_root;")

        op.execute(
            """
create unique index concurrently if not exists uq_runs_trace_dotted_order
on runs (trace_id, md5(dotted_order));
"""
        )
        op.execute("""drop index concurrently if exists runs_trace_inc_id_idx;""")

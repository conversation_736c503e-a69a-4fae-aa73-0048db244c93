"""intervals column splits for bigquery/looker

Revision ID: f3b3e486d751
Revises: d4a8c071ec15
Create Date: 2025-07-09 14:20:43.324104

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f3b3e486d751"
down_revision = "d4a8c071ec15"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Add separate start and end columns for interval and insertion_time_range
    # to make them compatible with datastream for Looker/BigQuery reporting
    op.execute(
        """
        ALTER TABLE trace_count_transactions
        ADD COLUMN IF NOT EXISTS interval_start TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS interval_end TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS insertion_time_range_start TIMESTAMP WITH TIME ZONE,
        ADD COLUMN IF NOT EXISTS insertion_time_range_end TIMESTAMP WITH TIME ZONE;
        """
    )

    # Backfill the new columns with data from the existing TSTZRANGE columns
    op.execute(
        """
        UPDATE trace_count_transactions
        SET
            interval_start = lower(interval),
            interval_end = upper(interval),
            insertion_time_range_start = lower(insertion_time_range),
            insertion_time_range_end = upper(insertion_time_range)
        WHERE
            interval_start IS NULL
            OR interval_end IS NULL
            OR insertion_time_range_start IS NULL
            OR insertion_time_range_end IS NULL;
        """
    )

    # Make the new columns NOT NULL after backfilling.
    # TODO: in separate PR after this has run and code released, set to not null


def downgrade() -> None:
    # Remove the new columns
    op.execute(
        """
        ALTER TABLE trace_count_transactions
        DROP COLUMN IF EXISTS interval_start,
        DROP COLUMN IF EXISTS interval_end,
        DROP COLUMN IF EXISTS insertion_time_range_start,
        DROP COLUMN IF EXISTS insertion_time_range_end;
        """
    )

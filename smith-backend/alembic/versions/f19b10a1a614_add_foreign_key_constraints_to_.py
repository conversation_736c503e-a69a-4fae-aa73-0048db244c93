"""add foreign key constraints to annotation queue runs archive table

Revision ID: f19b10a1a614
Revises: 363e685c94ea
Create Date: 2023-10-24 15:41:13.244445

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f19b10a1a614"
down_revision = "363e685c94ea"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # In case there are any violations of the foreign key constraints, delete them
    op.execute(
        """
        DELETE FROM annotation_queue_runs_archive aqra
        WHERE (queue_id IS NOT NULL AND NOT EXISTS (
            SELECT 1 FROM annotation_queues aq WHERE aq.id = aqra.queue_id
        ))
        OR (run_id IS NOT NULL AND NOT EXISTS (
            SELECT 1 FROM runs r WHERE r.id = aqra.run_id
        ))
        """
    )
    op.execute(
        """
        ALTER TABLE annotation_queue_runs_archive
        ALTER COLUMN queue_id DROP NOT NULL
        """
    )

    op.execute(
        """
        ALTER TABLE annotation_queue_runs_archive
        ADD CONSTRAINT annotation_queue_runs_archive_run_id_fkey FOREIGN KEY (run_id)
        REFERENCES runs(id) ON DELETE CASCADE
        """
    )

    op.execute(
        """
        ALTER TABLE annotation_queue_runs_archive
        ADD CONSTRAINT annotation_queue_runs_archive_queue_id_fkey FOREIGN KEY (queue_id)
        REFERENCES annotation_queues(id) ON DELETE SET NULL
        """
    )


def downgrade() -> None:
    # Need to delete any rows where queue_id is null because the we are re-setting the not null constraint on queue_id
    op.execute(
        """
        DELETE FROM annotation_queue_runs_archive aqra
        WHERE (queue_id IS NULL)
        """
    )
    op.execute(
        """
        ALTER TABLE IF EXISTS annotation_queue_runs_archive
        DROP CONSTRAINT IF EXISTS annotation_queue_runs_archive_queue_id_fkey
        """
    )

    op.execute(
        """
        ALTER TABLE IF EXISTS annotation_queue_runs_archive
        DROP CONSTRAINT IF EXISTS annotation_queue_runs_archive_run_id_fkey
        """
    )

    op.execute(
        """
        ALTER TABLE IF EXISTS annotation_queue_runs_archive
        ALTER COLUMN queue_id SET NOT NULL
        """
    )

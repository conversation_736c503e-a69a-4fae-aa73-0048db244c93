"""provider_users store username

Revision ID: dcb4aec68a48
Revises: 44205692e503
Create Date: 2025-06-27 12:57:14.564332

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "dcb4aec68a48"
down_revision = "44205692e503"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS provider_users
        ADD COLUMN IF NOT EXISTS username TEXT;
    """)


def downgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS provider_users
        DROP COLUMN IF EXISTS username;
    """)

"""scim group mapping tables

Revision ID: aa16bf099f04
Revises: 458ecf7c0b7d
Create Date: 2025-06-30 18:29:28.044094

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "aa16bf099f04"
down_revision = "458ecf7c0b7d"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # This stores groups of users with the same role within a workspace
    # OR groups of users with the same organization role within an organization.
    # This is driven by the naming convention:
    # - Org admins: `LS:Organization Admin` or `LS:Organization Admins`
    # - Workspace roles `LS:<org_role_name>:<workspace_name>:<workspace_role_name>`  e.g. `Team A:Organization User:Annotators`
    # Do NOT allow deleting a referenced role if still in use by a SCIM group.
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS scim_groups (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            access_scope ACCESS_SCOPE NOT NULL DEFAULT 'workspace',
            organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
            workspace_id UUID NULL REFERENCES tenants(id) ON DELETE CASCADE,
            org_role_id UUID NULL REFERENCES roles(id) ON DELETE RESTRICT,
            workspace_role_id UUID NULL REFERENCES roles(id) ON DELETE RESTRICT,
            group_name TEXT NOT NULL,
            external_id TEXT NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(organization_id, group_name),
            UNIQUE(organization_id, external_id)
        )
        """
    )

    # ensure the org role belongs to the organization or is a system role.
    # if present, ensure the workspace role belongs to the organization or is a system role.
    # if present, ensure the workspace belongs to the organization.
    op.execute(
        """
        CREATE OR REPLACE FUNCTION validate_scim_groups()
        RETURNS TRIGGER AS $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM roles WHERE id = NEW.org_role_id AND access_scope = 'organization' AND (organization_id = NEW.organization_id OR organization_id IS NULL)) THEN
                RAISE EXCEPTION 'Organization role ID % does not exist for organization and is not a system role', NEW.org_role_id;
            END IF;
            IF NEW.workspace_role_id IS NOT NULL THEN
                IF NOT EXISTS (SELECT 1 FROM roles WHERE id = NEW.workspace_role_id AND access_scope = 'workspace' AND (organization_id = NEW.organization_id OR organization_id IS NULL)) THEN
                    RAISE EXCEPTION 'Workspace role ID % does not exist for organization and is not a system role', NEW.workspace_role_id;
                END IF;
            END IF;
            IF NEW.workspace_id IS NOT NULL THEN
                IF NOT EXISTS (SELECT 1 FROM tenants WHERE id = NEW.workspace_id AND organization_id = NEW.organization_id) THEN
                    RAISE EXCEPTION 'Workspace ID % does not exist for organization', NEW.workspace_id;
                END IF;
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;

        CREATE TRIGGER validate_scim_groups
        BEFORE INSERT OR UPDATE ON scim_groups
        FOR EACH ROW
        EXECUTE FUNCTION validate_scim_groups();
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scim_groups_org_id ON scim_groups (organization_id)
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scim_groups_external_id ON scim_groups (external_id)
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scim_groups_workspace_id ON scim_groups (workspace_id)
        """
    )
    # Add trigger to automatically update updated_at
    op.execute(
        """
        CREATE TRIGGER update_scim_groups_updated_at
        BEFORE UPDATE ON scim_groups
        FOR EACH ROW
        EXECUTE PROCEDURE update_updated_at_column();
        """
    )

    # store current membership in SCIM groups.
    # used to calculate resolved permissions when there are multiple
    # applicable groups.
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS scim_group_members (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            group_id UUID NOT NULL REFERENCES scim_groups(id) ON DELETE CASCADE,
            identity_id UUID NOT NULL REFERENCES identities(id) ON DELETE CASCADE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            UNIQUE(group_id, identity_id)
        )
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scim_group_members_group_id ON scim_group_members(group_id)
        """
    )
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_scim_group_members_identity_id ON scim_group_members(identity_id)
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP INDEX IF EXISTS idx_scim_group_members_identity_id;
        DROP INDEX IF EXISTS idx_scim_group_members_group_id;
        """
    )
    op.execute(
        """
        DROP TABLE IF EXISTS scim_group_members;
        """
    )
    # Drop the trigger first
    op.execute(
        """
        DROP TRIGGER IF EXISTS update_scim_groups_updated_at ON scim_groups;
        """
    )
    # Drop the indexes
    op.execute(
        """
        DROP INDEX IF EXISTS idx_scim_groups_external_id;
        DROP INDEX IF EXISTS idx_scim_groups_workspace_id;
        DROP INDEX IF EXISTS idx_scim_groups_org_id;
        DROP INDEX IF EXISTS idx_scim_groups_org_external_id;
        """
    )

    # Drop the tables

    op.execute(
        """
        DROP TABLE IF EXISTS scim_groups;
        """
    )

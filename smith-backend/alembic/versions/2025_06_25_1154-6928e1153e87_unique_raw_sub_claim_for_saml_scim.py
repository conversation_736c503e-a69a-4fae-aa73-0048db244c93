"""unique raw_sub_claim for SAML/SCIM

Revision ID: 6928e1153e87
Revises: 677f7e8640ce
Create Date: 2025-06-25 11:54:13.655076

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6928e1153e87"
down_revision = "677f7e8640ce"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("""
        CREATE UNIQUE INDEX IF NOT EXISTS uq_provider_users_raw_sub_claim_saml_provider_id
        ON provider_users (raw_sub_claim, saml_provider_id)
        WHERE provider = 'supabase:sso';
    """)


def downgrade() -> None:
    op.execute("""
        DROP INDEX IF EXISTS uq_provider_users_raw_sub_claim_saml_provider_id;
    """)

"""store tenants.external_id

Revision ID: 22763bf190fa
Revises: 571832f9eefc
Create Date: 2025-06-23 09:50:15.853333

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "22763bf190fa"
down_revision = "571832f9eefc"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # scim_external_id is the IdP group ID
    op.execute(
        """
        ALTER TABLE IF EXISTS tenants ADD COLUMN scim_external_id TEXT;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS tenants DROP COLUMN IF EXISTS scim_external_id;
        """
    )

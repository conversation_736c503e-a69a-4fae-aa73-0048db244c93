"""20230906 Populate runs.trace_id, runs.dotted_order

Revision ID: ac442fc5b2e1
Revises: 4cf2aa2d841a
Create Date: 2023-09-06 12:46:38.975803

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ac442fc5b2e1"
down_revision = "4cf2aa2d841a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """
create or replace function runs_set_trace_fields_func() returns trigger as $$
    declare
        calc_trace_id uuid;
        calc_dotted_order text;
    begin
        WITH RECURSIVE parent_runs(id, parent_run_id, dotted_order) AS (
            VALUES (
                NEW.id,
                NEW.parent_run_id,
                array[to_char(NEW.start_time, 'YYYYMMDD"T"HH24MISSUS"Z"') || NEW.id::text] )
            
          UNION
            
            SELECT
                runs.id,
                runs.parent_run_id,
                to_char(runs.start_time, 'YYYYMMDD"T"HH24MISSUS"Z"') || runs.id::text || parent_runs.dotted_order
            FROM runs
            JOIN parent_runs ON parent_runs.parent_run_id = runs.id
        )
        
        SELECT
            id,
            array_to_string(dotted_order, '.')
        INTO
            calc_trace_id,
            calc_dotted_order
        FROM parent_runs
        where parent_run_id is null;
        
        NEW.trace_id = calc_trace_id;
        NEW.dotted_order = calc_dotted_order;

        return NEW;
    end;
$$ language plpgsql;
"""
        )


def downgrade() -> None:
    pass

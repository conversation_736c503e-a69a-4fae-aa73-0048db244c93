"""scheduled bulk exports

Revision ID: d66893fcef27
Revises: 1faa701f0405
Create Date: 2025-07-14 09:41:02.074048

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d66893fcef27"
down_revision = "1faa701f0405"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade():
    # interval_hours: if set, create bulk exports with this as the source to process data every X hours.
    # source_bulk_export_id: present for bulk exports created by scheduled exports.
    op.execute(
        """
        ALTER TABLE bulk_exports
        ADD COLUMN interval_hours INTEGER CHECK (interval_hours > 0 AND interval_hours <= 168),
        ADD COLUMN source_bulk_export_id UUID references bulk_exports(id) ON DELETE CASCADE,
        ALTER COLUMN end_time DROP NOT NULL,
        ADD CONSTRAINT interval_or_source_check CHECK (
            (interval_hours IS NULL AND source_bulk_export_id IS NOT NULL) OR  -- export created by scheduled export
            (interval_hours IS NOT NULL AND source_bulk_export_id IS NULL) OR  -- scheduled export
            (interval_hours IS NULL AND source_bulk_export_id IS NULL) -- regular export
        ),
        -- end_time is required for non-scheduled exports, and not supported for scheduled exports
        ADD CONSTRAINT end_time_check CHECK (
            (interval_hours IS NULL AND end_time IS NOT NULL) OR
            (interval_hours IS NOT NULL AND end_time IS NULL)
        );
        """
    )

    with op.get_context().autocommit_block():
        op.execute(
            """
            CREATE INDEX concurrently idx_bulk_exports_source_tenant_endtime_desc
            ON bulk_exports (source_bulk_export_id, tenant_id, end_time DESC);
            """
        )
        op.execute(
            """
            CREATE UNIQUE INDEX concurrently uq_bulk_exports_for_scheduled ON bulk_exports (source_bulk_export_id, start_time, end_time) WHERE source_bulk_export_id IS NOT NULL;
            """
        )


def downgrade():
    op.execute(
        """
        ALTER TABLE bulk_exports
        DROP COLUMN IF EXISTS interval_hours,
        DROP COLUMN IF EXISTS source_bulk_export_id;
        """
    )

"""20230905 Add runs.trace_id, runs.dotted_order

Revision ID: a90df30962f0
Revises: 0dd7addbf2c8
Create Date: 2023-09-05 17:33:33.506181

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "a90df30962f0"
down_revision = "0dd7addbf2c8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add index for the trigger below
    with op.get_context().autocommit_block():
        op.execute(
            """create index concurrently if not exists runs_id_inc_parent_start_time_idx on runs (id) include (parent_run_id, start_time);"""
        )

    # Add trace_id and dotted_order columns
    op.execute(
        """
alter table runs
    add column trace_id uuid references runs (id) on delete cascade,
    add column dotted_order text collate "C";
"""
    )

    # Add trigger to set trace_id and dotted_order on insert
    op.execute(
        """
create or replace function runs_set_trace_fields_func() returns trigger as $$
    declare
        calc_trace_id uuid;
        calc_dotted_order text;
    begin
        WITH RECURSIVE parent_runs(id, parent_run_id, dotted_order) AS (
            VALUES (
                NEW.id,
                NEW.parent_run_id,
                array[to_char(NEW.start_time, 'YYYYMMDD"T"HH24MISSMS"Z"') || NEW.id::text] )
            
          UNION
            
            SELECT
                runs.id,
                runs.parent_run_id,
                to_char(runs.start_time, 'YYYYMMDD"T"HH24MISSMS"Z"') || runs.id::text || parent_runs.dotted_order
            FROM runs
            JOIN parent_runs ON parent_runs.parent_run_id = runs.id
        )
        
        SELECT
            id,
            array_to_string(dotted_order, '.')
        INTO
            calc_trace_id,
            calc_dotted_order
        FROM parent_runs
        where parent_run_id is null;
        
        NEW.trace_id = calc_trace_id;
        NEW.dotted_order = calc_dotted_order;

        return NEW;
    end;
$$ language plpgsql;
"""
    )
    op.execute(
        """
create or replace trigger runs_set_trace_fields_trigger
	before insert on runs
	for each row
	execute function runs_set_trace_fields_func();
"""
    )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """drop index concurrently if exists runs_id_inc_parent_start_time_idx;"""
        )
    op.execute(
        """
drop trigger runs_set_trace_fields_trigger on runs;
drop function runs_set_trace_fields_func();
"""
    )
    op.execute(
        """
alter table runs drop column trace_id, drop column dotted_order;
"""
    )

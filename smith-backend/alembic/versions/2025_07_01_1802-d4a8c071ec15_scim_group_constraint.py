"""scim group constraint

Revision ID: d4a8c071ec15
Revises: aa16bf099f04
Create Date: 2025-07-01 18:02:38.360043

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d4a8c071ec15"
down_revision = "aa16bf099f04"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # org role ID must always be present
    # 1) if the group is Org Admins, org_role_id must be set to the org admin role
    # 2) if the group is a workspace role, org_role_id will be Organization User
    # 3) in the future, if we support additional org roles, org_role_id will be set to an alternate org role
    op.execute(
        """
        ALTER TABLE scim_groups
        ALTER COLUMN org_role_id SET NOT NULL;
        """
    )

    # While we don't allow custom org roles today, to future-proof our naming convention,
    # we need to prevent the first 3 parts of SCIM group names from containing colons.
    # The convention is LS:<org_role_name> for org-scoped groups (now just org admins)
    # and LS:<org_role_name>:<workspace_name>:<workspace_role_name> for workspace-scoped groups.
    # So, prevent colons in org role names via check constraint.
    op.execute(
        """
        ALTER TABLE roles
        ADD CONSTRAINT roles_display_name_check CHECK (access_scope != 'organization' OR display_name NOT LIKE '%:%');
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP TRIGGER IF EXISTS tenant_name_no_colons_trigger ON tenants;
        """
    )
    op.execute(
        """
        DROP FUNCTION IF EXISTS check_tenant_name_no_colons;
        """
    )
    op.execute(
        """
        ALTER TABLE roles
        DROP CONSTRAINT IF EXISTS roles_display_name_check;
        """
    )
    op.execute(
        """
        ALTER TABLE scim_groups
        ALTER COLUMN org_role_id DROP NOT NULL;
        """
    )

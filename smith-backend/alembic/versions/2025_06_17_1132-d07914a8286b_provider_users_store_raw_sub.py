"""provider_users store raw sub

Revision ID: d07914a8286b
Revises: 6d68f26be4f3
Create Date: 2025-06-17 11:32:38.747641

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d07914a8286b"
down_revision = "6d68f26be4f3"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        "ALTER TABLE IF EXISTS provider_users ADD COLUMN IF NOT EXISTS raw_sub_claim TEXT"
    )


def downgrade() -> None:
    op.execute(
        "ALTER TABLE IF EXISTS provider_users DROP COLUMN IF EXISTS raw_sub_claim"
    )

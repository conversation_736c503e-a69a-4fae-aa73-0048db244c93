"""scim

Revision ID: a168b2657a9b
Revises: b4fd1e9124a7
Create Date: 2025-06-13 11:04:27.822487

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "a168b2657a9b"
down_revision = "b4fd1e9124a7"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # create a table for SCIM bearer tokens
    op.execute("""
        CREATE TABLE IF NOT EXISTS scim_bearer_tokens (
            id uuid NOT NULL PRIMARY KEY,
            token VARCHAR NOT NULL UNIQUE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            short_token VARCHAR(255) NOT NULL,
            description VARCHAR(255) NOT NULL,
            organization_id uuid REFERENCES organizations(id) ON DELETE CASCADE,
            last_used_at TIMESTAMP WITH TIME ZONE
        );
    """)
    op.execute("""
        CREATE INDEX IF NOT EXISTS ix_scim_bearer_tokens_organization_id ON scim_bearer_tokens(organization_id);
        CREATE INDEX IF NOT EXISTS ix_scim_bearer_tokens_brin_created_at ON scim_bearer_tokens USING brin(created_at);
    """)

    # add a column to the organizations table to indicate if JIT provisioning is enabled,
    #   because this should be disabled when using SCIM. only applicable to SAML SSO in cloud at time of addition.
    op.execute("""
        ALTER TABLE organizations ADD COLUMN IF NOT EXISTS jit_provisioning_enabled BOOLEAN DEFAULT TRUE;
    """)

    # Add provisioning_method=[scim|saml:jit|null] to provider_users for tracking
    op.execute("""
        ALTER TABLE provider_users ADD COLUMN IF NOT EXISTS provisioning_method VARCHAR(255);
    """)

    # allow disabling a login method via SCIM
    op.execute("""
        ALTER TABLE provider_users ADD COLUMN IF NOT EXISTS is_disabled BOOLEAN DEFAULT FALSE;
    """)

    # add updated_at to tenants table + trigger
    op.execute("""
        ALTER TABLE tenants ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE;
    """)
    # This function was added in a previous migration, but only for cloud.
    op.execute(
        """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = now();
            RETURN NEW;
        END;
        $$ LANGUAGE 'plpgsql';
        """
    )
    op.execute(
        """
        CREATE TRIGGER update_tenants_updated_at
        BEFORE UPDATE ON tenants
        FOR EACH ROW
        EXECUTE PROCEDURE update_updated_at_column();
        """
    )

    # todo: set updated_at=max(created_at, updated_at) and provisioning_method in release after code changes


def downgrade() -> None:
    # delete the trigger
    op.execute("""
        DROP TRIGGER IF EXISTS update_tenants_updated_at ON tenants;
    """)
    # drop the columns
    op.execute("""
        ALTER TABLE tenants DROP COLUMN IF EXISTS updated_at;
    """)
    op.execute("""
        ALTER TABLE provider_users DROP COLUMN IF EXISTS is_disabled;
    """)
    op.execute("""
        ALTER TABLE provider_users DROP COLUMN IF EXISTS provisioning_method;
    """)
    op.execute("""
        ALTER TABLE organizations DROP COLUMN IF EXISTS jit_provisioning_enabled;
    """)

    # drop the indexes
    op.execute("""
        DROP INDEX IF EXISTS ix_scim_bearer_tokens_brin_created_at;
        DROP INDEX IF EXISTS ix_scim_bearer_tokens_organization_id;
    """)

    # drop the table
    op.execute("""
        DROP TABLE IF EXISTS scim_bearer_tokens;
    """)

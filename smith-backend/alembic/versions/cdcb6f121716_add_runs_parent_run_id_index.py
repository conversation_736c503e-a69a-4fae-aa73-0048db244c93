"""Add runs.parent_run_id index

Revision ID: cdcb6f121716
Revises: b3c0c27e453b
Create Date: 2023-06-09 07:45:22.266679

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "cdcb6f121716"
down_revision = "b3c0c27e453b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(
        op.f("ix_runs_parent_run_id"),
        "runs",
        ["parent_run_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_runs_parent_run_id"), table_name="runs")
    # ### end Alembic commands ###

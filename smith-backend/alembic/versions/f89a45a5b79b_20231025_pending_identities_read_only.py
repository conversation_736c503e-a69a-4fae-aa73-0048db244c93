"""20231025 pending_identities read-only

Revision ID: f89a45a5b79b
Revises: 3ff5b3d0df73
Create Date: 2023-10-25 13:34:59.806283

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f89a45a5b79b"
down_revision = "3ff5b3d0df73"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE pending_identities
        ADD COLUMN read_only BOOLEAN NOT NULL DEFAULT FALSE
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE pending_identities
        DROP COLUMN read_only
        """
    )

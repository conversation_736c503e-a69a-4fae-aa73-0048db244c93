"""gemini pricing

Revision ID: deb207f94e0e
Revises: b5ffc77cd664
Create Date: 2025-04-14 10:41:39.049830

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "deb207f94e0e"
down_revision = "b5ffc77cd664"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
        VALUES
            (36, 'gemini-2.5-pro-preview-03-25', '^gemini-2\.5-pro-preview-03-25$', 0.00000125, 0.00001, NULL),
            (37, 'gemini-2.0-flash', '^gemini-2\.0-flash(-\d*)?$', 0.0000001, 0.0000004, NULL),
            (38, 'gemini-2.0-flash-lite', '^gemini-2\.0-flash-lite(-\d*)?$', 0.000000075, 0.0000003, NULL),
            (39, 'gemini-1.5-flash', '^gemini-1\.5-flash(-\d*)?$', 0.000000075, 0.0000003, NULL),
            (40, 'gemini-1.5-flash-latest', '^gemini-1\.5-flash-latest$', 0.000000075, 0.0000003, NULL),
            (41, 'gemini-1.5-flash-8b', '^gemini-1\.5-flash-8b(-\d*)?$', 0.0000000375, 0.00000015, NULL),
            (42, 'gemini-1.5-flash-8b-latest', '^gemini-1\.5-flash-8b-latest$', 0.0000000375, 0.00000015, NULL),
            (43, 'gemini-1.5-pro', '^gemini-1\.5-pro(-\d*)?$', 0.00000125, 0.000005, NULL),
            (44, 'gemini-1.5-pro-latest', '^gemini-1\.5-pro-latest$', 0.00000125, 0.000005, NULL)
        """
    )


def downgrade() -> None:
    pass

"""update type

Revision ID: 9ee38ef710b4
Revises: 7a4b910dac1d
Create Date: 2023-06-16 16:59:47.040127

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "9ee38ef710b4"
down_revision = "7a4b910dac1d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column(
        "tracer_session_stats",
        "avg_latency",
    )
    op.add_column(
        "tracer_session_stats",
        sa.Column("avg_latency", sa.Interval(), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column(
        "tracer_session_stats",
        "avg_latency",
    )
    op.add_column(
        "tracer_session_stats",
        sa.Column("avg_latency", sa.FLOAT(), nullable=True),
    )
    # ### end Alembic commands ###

"""model_price_map_unique

Revision ID: cb67b9b68976
Revises: cdc7a8763a06
Create Date: 2024-02-24 01:45:36.080039

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "cb67b9b68976"
down_revision = "cdc7a8763a06"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        "create or replace function array_sort (arr anyarray) returns anyarray immutable as $$ select array_agg(x order by x) from unnest(arr) x; $$ language sql;"
    )

    op.execute(
        "create unique index model_price_map_uniq_idx on model_price_map(tenant_id, match_pattern, array_sort(match_path), start_time) where start_time is not null"
    )

    op.execute(
        "create unique index model_price_map_uniq_idx_null on model_price_map(tenant_id, match_pattern, array_sort(match_path)) where start_time is null"
    )


def downgrade() -> None:
    op.execute("drop index model_price_map_uniq_idx")
    op.execute("drop index model_price_map_uniq_idx_null")
    op.execute("drop function array_sort")

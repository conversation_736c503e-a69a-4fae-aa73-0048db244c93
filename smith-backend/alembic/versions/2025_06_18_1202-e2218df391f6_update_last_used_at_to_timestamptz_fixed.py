"""update last_used_at to timestamptz fixed

Revision ID: e2218df391f6
Revises: 93cf60be169a
Create Date: 2025-06-18 12:02:23.819173

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "e2218df391f6"
down_revision = "93cf60be169a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Alter column last_used_at to TIMESTAMPTZ
    op.execute(
        """
        ALTER TABLE IF EXISTS api_keys
        ALTER COLUMN last_used_at TYPE TIMESTAMPTZ USING last_used_at AT TIME ZONE 'UTC';
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS api_keys
        ALTER COLUMN last_used_at TYPE TIMESTAMP WITHOUT TIME ZONE;
        """
    )

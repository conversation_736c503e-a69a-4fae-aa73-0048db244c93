"""Migrate container_spec column to host_projects table.

Revision ID: 42826a08d997
Revises: d247f4777af5
Create Date: 2025-04-09 14:05:05.332667

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "42826a08d997"
down_revision = "d247f4777af5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # add container_spec column to host_projects
    op.execute(
        """
        ALTER TABLE host_projects
        ADD COLUMN IF NOT EXISTS container_spec JSONB NOT NULL DEFAULT '{}';
        """
    )

    # migrate container_spec column from host_revisions to host_projects
    op.execute(
        """
        UPDATE host_projects hp
        SET container_spec = hr.container_spec
        FROM (
            SELECT DISTINCT ON (project_id) project_id, container_spec
            FROM host_revisions
            ORDER BY project_id, created_at DESC
        ) AS hr
        WHERE hp.id = hr.project_id;
        """
    )


def downgrade() -> None:
    # migrate container_spec column from host_projects to host_revisions
    op.execute(
        """
        UPDATE host_revisions hr
        SET container_spec = hp.container_spec
        FROM host_projects hp
        WHERE hr.project_id = hp.id;
        """
    )

    # drop container_spec column from host_projects
    op.execute(
        """
        ALTER TABLE host_projects
        DROP COLUMN IF EXISTS container_spec;
        """
    )

"""add new openai model pricing

Revision ID: 03a2eb8f5b3c
Revises: 024c727e6a19
Create Date: 2025-04-18 16:21:11.992311

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "03a2eb8f5b3c"
down_revision = "024c727e6a19"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
        VALUES
            (51, 'o4-mini', '^o4-mini$', 0.0000011, 0.0000044, NULL)
        """
    )


def downgrade() -> None:
    pass

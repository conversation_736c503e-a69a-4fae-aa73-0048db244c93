"""hub events startswith

Revision ID: c31481055945
Revises: 0c41571cdbdb
Create Date: 2023-09-12 16:09:32.363811

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "c31481055945"
down_revision = "0c41571cdbdb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE hub_commits
            ALTER COLUMN commit_hash TYPE TEXT COLLATE "C"
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE hub_commits
            ALTER COLUMN commit_hash TYPE TEXT
        """
    )

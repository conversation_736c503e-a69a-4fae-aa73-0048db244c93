"""merge branches

Revision ID: 6d4d0489d191
Revises: b96170cb8e9b, 22763bf190fa
Create Date: 2025-06-23 12:12:40.726649

"""

# revision identifiers, used by Alembic.
revision = "6d4d0489d191"
down_revision = ("b96170cb8e9b", "22763bf190fa")
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    pass


def downgrade() -> None:
    pass

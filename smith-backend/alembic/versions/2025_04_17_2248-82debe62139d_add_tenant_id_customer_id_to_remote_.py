"""add tenant_id, customer_id to remote_metrics

Revision ID: 82debe62139d
Revises: 5f3c19df5607
Create Date: 2025-04-17 22:48:39.651673

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "82debe62139d"
down_revision = "5f3c19df5607"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE remote_metrics
        ADD COLUMN tenant_id VARCHAR(255) DEFAULT NULL,
        ADD COLUMN self_hosted_customer_id VARCHAR(255) DEFAULT NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE remote_metrics
        DROP COLUMN tenant_id,
        DROP COLUMN self_hosted_customer_id;
        """
    )

"""add offline_mode to licenses table

Revision ID: 024c727e6a19
Revises: 9fe9ca4460ae
Create Date: 2025-04-18 07:37:48.922863

"""

from alembic import op
from app.config import settings

# revision identifiers, used by Alembic.
revision = "024c727e6a19"
down_revision = "9fe9ca4460ae"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Only run this migration if we are not self hosted
    if not settings.IS_SELF_HOSTED:
        op.execute("""
            ALTER TABLE self_hosted_licenses
            ADD COLUMN offline_mode BOOLEAN DEFAULT FALSE,
            ALTER COLUMN license_type DROP NOT NULL
        """)


def downgrade() -> None:
    if not settings.IS_SELF_HOSTED:
        op.execute("""
            ALTER TABLE self_hosted_licenses
            DROP COLUMN offline_mode
        """)

"""create feedbacks table

Revision ID: ec1ef07f00b1
Revises: 083a9fb29dfc
Create Date: 2025-04-25 17:25:50.100036
"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ec1ef07f00b1"
down_revision = "083a9fb29dfc"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS feedbacks (
            id                       UUID PRIMARY KEY,
            run_id                   UUID,
            session_id               UUID REFERENCES tracer_session ON DELETE CASCADE,
            user_id                  UUID,
            tenant_id                UUID NOT NULL REFERENCES tenants ON DELETE CASCADE,
            is_root                  BOOLEAN NOT NULL DEFAULT FALSE,
            start_time               TIMESTAMP WITH TIME ZONE,
            created_at               TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            modified_at              TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            "key"                    TEXT NOT NULL,
            score                    NUMERIC(9,4),
            value                    TEXT,
            comment                  TEXT,
            correction               TEXT,
            trace_id                 UUID,
            feedback_source          JSONB,
            comparative_experiment_id UUID,
            feedback_group_id        UUID,
            extra                    JSONB
        );
        """
    )

    # equality look-ups on "key"
    op.execute(
        """CREATE INDEX IF NOT EXISTS idx_feedbacks_key
           ON feedbacks ("key");"""
    )

    # equality look-ups on "tenant_id", "session_id", "run_id", and "user_id"
    op.execute(
        """CREATE INDEX IF NOT EXISTS idx_feedbacks_tenant_session_run_uid
           ON feedbacks (
               tenant_id,
               session_id,
               run_id,
               user_id
           );"""
    )

    op.execute(
        """CREATE INDEX IF NOT EXISTS idx_feedbacks_created_at_desc
           ON feedbacks (created_at DESC);"""
    )


def downgrade() -> None:
    op.execute("DROP TABLE IF EXISTS feedbacks CASCADE;")

"""Add runs delete permission

Revision ID: 46275283308a
Revises: 34c56fa9a5a0
Create Date: 2025-04-22 11:16:02.736282

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "46275283308a"
down_revision = "34c56fa9a5a0"
branch_labels: None = None
depends_on: None = None


def upgrade() -> None:
    # Add the runs:delete permission
    op.execute(
        """
        INSERT INTO permissions (name, description) VALUES
            ('runs:delete', 'Permission to delete runs.')
        ON CONFLICT (name) DO UPDATE
            SET description = EXCLUDED.description;
        """
    )

    # Grant runs:delete to the primary workspace role
    op.execute(
        """
        WITH role_id AS (
            SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'
        )
        INSERT INTO role_permissions (role_id, permission) VALUES
            ((SELECT id from role_id), 'runs:delete')
        ON CONFLICT (role_id, permission) DO NOTHING;
        """
    )


def downgrade() -> None:
    # Revoke the grants first
    op.execute(
        """
        WITH role_id AS (
            SELECT id FROM roles WHERE name = 'WORKSPACE_ADMIN'
        )
        DELETE FROM role_permissions
        WHERE permission = 'runs:delete'
        AND role_id = (SELECT id from role_id)
        """
    )
    # Then drop the permission itself
    op.execute(
        """
        DELETE FROM permissions
        WHERE name = 'runs:delete'
        """
    )

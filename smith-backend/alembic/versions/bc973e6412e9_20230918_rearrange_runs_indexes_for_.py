"""20230918 Rearrange runs indexes for dotted order

Revision ID: bc973e6412e9
Revises: 3f0d5a7db6a5
Create Date: 2023-09-18 09:32:16.484649

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "bc973e6412e9"
down_revision = "3f0d5a7db6a5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """create unique index concurrently if not exists uq_runs_trace_dotted_order
on runs (trace_id, md5(dotted_order));"""
        )
        op.execute(
            """create index concurrently if not exists runs_trace_inc_dotted_order_id
on runs (trace_id) include (dotted_order, id);"""
        )
        op.execute(
            """drop index concurrently if exists runs_trace_dotted_order_inc_id;"""
        )
        op.execute(
            """create index concurrently if not exists runs_id_inc_session_trace_dotted_order_idx on runs (id) include (session_id, trace_id, dotted_order);"""
        )
        op.execute("""drop index concurrently if exists runs_id_inc_session_idx;""")


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """create index concurrently runs_id_inc_session_idx on runs (id) include (session_id);"""
        )
        op.execute(
            """drop index concurrently if exists runs_id_inc_session_trace_dotted_order_idx;"""
        )
        op.execute(
            """create unique index concurrently if not exists runs_trace_dotted_order_inc_id
            on runs (trace_id, dotted_order) include (id);"""
        )
        op.execute("drop index concurrently if exists runs_trace_inc_dotted_order_id;")
        op.execute("drop index concurrently if exists uq_runs_trace_dotted_order;")

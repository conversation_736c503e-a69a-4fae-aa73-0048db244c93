"""use hash for unique constraint

Revision ID: ef21dc3fedf9
Revises: 53a28758b136
Create Date: 2023-05-09 22:56:51.189532

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "ef21dc3fedf9"
down_revision = "53a28758b136"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("CREATE EXTENSION IF NOT EXISTS pgcrypto;")
    op.add_column(
        "examples",
        sa.Column(
            "inputs_hash",
            postgresql.BYTEA(),
            sa.Computed(
                "digest(inputs::text, 'md5')",
            ),
            nullable=True,
        ),
    )
    op.add_column(
        "examples",
        sa.Column(
            "outputs_hash",
            postgresql.BYTEA(),
            sa.Computed(
                "digest(outputs::text, 'md5')",
            ),
            nullable=True,
        ),
    )
    op.drop_constraint("unique_example_per_dataset", "examples", type_="unique")
    op.create_unique_constraint(
        "unique_example_per_dataset",
        "examples",
        ["inputs_hash", "outputs_hash", "dataset_id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("unique_example_per_dataset", "examples", type_="unique")
    op.create_unique_constraint(
        "unique_example_per_dataset", "examples", ["inputs", "outputs", "dataset_id"]
    )
    op.drop_column("examples", "outputs_hash")
    op.drop_column("examples", "inputs_hash")
    # ### end Alembic commands ###

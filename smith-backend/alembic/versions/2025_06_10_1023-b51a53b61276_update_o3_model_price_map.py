"""Update o3 model price map

Revision ID: b51a53b61276
Revises: 26a5a97e6fad
Create Date: 2025-06-10 10:23:16.801852

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b51a53b61276"
down_revision = "26a5a97e6fad"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET prompt_cost = 0.000002, completion_cost = 0.000008, prompt_cost_details = '{"cache_read": 0.0000005}'
        WHERE name = 'o3' AND tenant_id IS NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET prompt_cost = 0.00001, completion_cost = 0.00004, prompt_cost_details = '{"cache_read": 0.0000025}'
        WHERE name = 'o3' AND tenant_id IS NULL;
        """
    )

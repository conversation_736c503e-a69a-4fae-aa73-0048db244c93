"""add alignment annotation queue

Revision ID: 631ce4533f51
Revises: 22763bf190fa
Create Date: 2025-06-23 10:07:32.182638

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "631ce4533f51"
down_revision = "22763bf190fa"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        alter table llm_evaluators
        add column if not exists annotation_queue_id uuid unique references annotation_queues(id)
        """
    )

    op.execute(
        """
        create index if not exists idx_llm_evaluators_annotation_queue_id 
        on llm_evaluators(annotation_queue_id)
        """
    )

    op.execute(
        """
        create index if not exists idx_run_rules_evaluator_id 
        on run_rules(evaluator_id)
        """
    )

    op.execute("""
    create or replace function delete_evaluator_on_rule_delete()
    returns trigger as $$
    begin
        delete from evaluators where id = old.evaluator_id;
        return old;
    end;
    $$ language plpgsql;
    """)

    op.execute("""
    create or replace function delete_annotation_queue_on_evaluator_delete()
    returns trigger as $$
    begin
        delete from annotation_queues where id = old.annotation_queue_id;
        return old;
    end;
    $$ language plpgsql;
    """)

    op.execute("""
    create trigger delete_evaluator_cascade_from_rule
    after delete on run_rules
    for each row
    when (old.evaluator_id is not null)
    execute function delete_evaluator_on_rule_delete();
    """)

    op.execute("""
    create trigger delete_annotation_queue_cascade_from_evaluator
    after delete on llm_evaluators
    for each row
    when (old.annotation_queue_id is not null)
    execute function delete_annotation_queue_on_evaluator_delete();
    """)


def downgrade() -> None:
    op.execute("""
    drop trigger if exists delete_annotation_queue_cascade_from_evaluator on llm_evaluators;
    """)

    op.execute("""
    drop trigger if exists delete_evaluator_cascade_from_rule on run_rules;
    """)

    op.execute("""
    drop function if exists delete_annotation_queue_on_evaluator_delete;
    """)

    op.execute("""
    drop function if exists delete_evaluator_on_rule_delete;
    """)

    op.execute(
        """
        drop index if exists idx_run_rules_evaluator_id
        """
    )

    op.execute(
        """
        drop index if exists idx_llm_evaluators_annotation_queue_id
        """
    )

    op.execute(
        """
        alter table llm_evaluators 
        drop column if exists annotation_queue_id
        """
    )

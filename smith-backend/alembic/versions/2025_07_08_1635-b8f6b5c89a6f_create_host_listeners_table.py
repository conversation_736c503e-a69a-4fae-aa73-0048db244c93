"""Create host_listeners table.

Revision ID: b8f6b5c89a6f
Revises: d4a8c071ec15
Create Date: 2025-07-08 16:35:13.348066

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b8f6b5c89a6f"
down_revision = "d4a8c071ec15"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("CREATE TYPE host_listeners_compute_type AS ENUM ('k8s');")
    op.execute(
        """
        CREATE TABLE IF NOT EXISTS host_listeners (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
            tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
            created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
            version TEXT,
            compute_type host_listeners_compute_type NOT NULL,
            compute_id TEXT NOT NULL,
            compute_config JSONB NOT NULL DEFAULT '{}',
            UNIQUE (organization_id, compute_id)
        );
        """
    )


def downgrade() -> None:
    op.execute("DROP TABLE IF EXISTS host_listeners;")
    op.execute("DROP TYPE IF EXISTS host_listeners_compute_type;")

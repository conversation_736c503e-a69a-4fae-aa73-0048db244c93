"""20230914 Change tracer_session_stats tokens columns to bigint

Revision ID: f2a9e13cc6d4
Revises: 953cf08300a5
Create Date: 2023-09-14 20:43:29.102177

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f2a9e13cc6d4"
down_revision = "953cf08300a5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """alter table tracer_session_stats
        alter column total_tokens type bigint,
        alter column prompt_tokens type bigint,
        alter column completion_tokens type bigint;"""
    )
    op.execute("""analyze tracer_session_stats;""")


def downgrade() -> None:
    pass

"""20231218_host_github_install_table

Revision ID: d4ef6995d138
Revises: b17b76c091db
Create Date: 2023-12-18 18:03:55.704438

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d4ef6995d138"
down_revision = "b17b76c091db"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
    CREATE TABLE IF NOT EXISTS host_integration_namespace_ids (
        id UUID NOT NULL PRIMARY KEY default gen_random_uuid(),
        namespace_id varchar NOT NULL,
        provider varchar NOT NULL,
        tenant_id UUID NOT NULL REFERENCES tenants(id)
    );
    """
    )

    op.execute(
        """
        ALTER TABLE host_projects
        ADD COLUMN host_integration_id UUID NULL;
        """
    )

    op.execute(
        """
        ALTER TABLE host_projects
        ADD CONSTRAINT host_projects_host_integration_id_fkey
        FOREIGN KEY (host_integration_id)
        REFERENCES host_integration_namespace_ids (id)
        ON DELETE SET NULL;
        """
    )

    with op.get_context().autocommit_block():
        op.execute(
            "CREATE INDEX CONCURRENTLY IF NOT EXISTS host_integration_tenant_id_idx on host_integration_namespace_ids(tenant_id)"
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("DROP INDEX CONCURRENTLY IF EXISTS host_integration_tenant_id_idx")

    op.execute(
        "ALTER TABLE host_projects DROP CONSTRAINT host_projects_host_integration_id_fkey;"
    )
    op.execute("ALTER TABLE host_projects DROP COLUMN host_integration_id;")
    op.execute("DROP TABLE IF EXISTS host_integration_namespace_ids;")

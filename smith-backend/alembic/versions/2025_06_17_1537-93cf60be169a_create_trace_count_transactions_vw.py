"""Create trace_count_transactions_vw.

Revision ID: 93cf60be169a
Revises: 5e2ff23ed13f
Create Date: 2025-06-17 15:37:34.264304

"""

from alembic import op
from app.config import settings

# revision identifiers, used by Alembic.
revision = "93cf60be169a"
down_revision = "5e2ff23ed13f"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    if not settings.IS_SELF_HOSTED:
        op.execute(
            """
            CREATE OR REPLACE VIEW trace_count_transactions_vw AS
            SELECT
                id,
                created_at,
                tenant_id,
                session_id,
                trace_count,
                status,
                interval,
                insertion_time_range,
                num_failed_send_attempts,
                transaction_type,
                organization_id,
                source,
                self_hosted_license_id
            FROM trace_count_transactions
            ;
            """
        )


def downgrade() -> None:
    if not settings.IS_SELF_HOSTED:
        op.execute("DROP VIEW IF EXISTS trace_count_transactions_vw")

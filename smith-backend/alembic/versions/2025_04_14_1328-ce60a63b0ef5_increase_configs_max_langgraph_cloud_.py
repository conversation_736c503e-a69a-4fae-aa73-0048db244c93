"""Increase configs max_langgraph_cloud_deployments.

Revision ID: ce60a63b0ef5
Revises: b5ffc77cd664
Create Date: 2025-04-14 13:28:36.722024

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ce60a63b0ef5"
down_revision = "b5ffc77cd664"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE organizations
        SET config = jsonb_set(config, '{max_langgraph_cloud_deployments}', '3'::jsonb)
        WHERE COALESCE((config->>'max_langgraph_cloud_deployments')::int, 0) < 3;
        """
    )


def downgrade() -> None:
    # Note: This isn't a perfect downgrade/rollback query. This may inadvertently
    # set some orgs to have 1 deployment when we previously set them to have 3.
    # Not the end of the world. Easy to manually update.
    op.execute(
        """
        UPDATE organizations
        SET config = jsonb_set(config, '{max_langgraph_cloud_deployments}', '1'::jsonb)
        WHERE (config->>'max_langgraph_cloud_deployments')::int = 3;
        """
    )

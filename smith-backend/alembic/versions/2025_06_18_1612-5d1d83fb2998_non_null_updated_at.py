"""non null updated at

Revision ID: 5d1d83fb2998
Revises: 7603a1eb448a
Create Date: 2025-06-18 16:12:40.499115

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "5d1d83fb2998"
down_revision = "7603a1eb448a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Set tenants.updated_at to not null and default to now()
    op.execute(
        "ALTER TABLE IF EXISTS tenants ALTER COLUMN updated_at SET DEFAULT now()"
    )
    op.execute("UPDATE tenants SET updated_at = GREATEST(created_at, updated_at)")
    op.execute("ALTER TABLE IF EXISTS tenants ALTER COLUMN updated_at SET NOT NULL")


def downgrade() -> None:
    op.execute("ALTER TABLE IF EXISTS tenants ALTER COLUMN updated_at DROP NOT NULL")
    op.execute("ALTER TABLE IF EXISTS tenants ALTER COLUMN updated_at DROP DEFAULT")

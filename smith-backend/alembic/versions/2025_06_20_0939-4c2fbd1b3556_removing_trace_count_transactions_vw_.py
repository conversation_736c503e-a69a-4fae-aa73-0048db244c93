"""Removing trace_count_transactions_vw since we don't need it.

Revision ID: 4c2fbd1b3556
Revises: 2ace5840f740
Create Date: 2025-06-20 09:39:48.250876

"""

from alembic import op
from app.config import settings

# revision identifiers, used by Alembic.
revision = "4c2fbd1b3556"
down_revision = "2ace5840f740"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    if not settings.IS_SELF_HOSTED:
        op.execute("DROP VIEW IF EXISTS trace_count_transactions_vw")


def downgrade() -> None:
    if not settings.IS_SELF_HOSTED:
        op.execute(
            """
            CREATE OR REPLACE VIEW trace_count_transactions_vw AS
            SELECT
                id,
                created_at,
                tenant_id,
                session_id,
                trace_count,
                status,
                interval,
                insertion_time_range,
                num_failed_send_attempts,
                transaction_type,
                organization_id,
                source,
                self_hosted_license_id
            FROM trace_count_transactions
            ;
            """
        )

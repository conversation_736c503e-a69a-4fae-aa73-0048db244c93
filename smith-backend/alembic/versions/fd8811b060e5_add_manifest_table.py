"""Add manifest table

Revision ID: fd8811b060e5
Revises: 64224b66242a
Create Date: 2023-06-21 12:06:39.977834

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "fd8811b060e5"
down_revision = "64224b66242a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "run_manifests",
        sa.Column(
            "id", sa.UUID(), server_default=sa.text("gen_random_uuid()"), nullable=False
        ),
        sa.Column("tenant_id", sa.UUID(), nullable=False),
        sa.Column("manifest", postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column(
            "manifest_hash",
            postgresql.BYTEA(),
            sa.Computed(
                "digest(manifest::text, 'md5')",
            ),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(["tenant_id"], ["tenants.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint(
            "tenant_id", "manifest_hash", name="tenant_id_manifest_hash_uc"
        ),
    )
    op.add_column("runs", sa.Column("manifest_id", sa.UUID(), nullable=True))
    op.create_index(
        "idx_runs_mainfest_id_execution_order_start_time",
        "runs",
        ["manifest_id", "execution_order", sa.text("start_time DESC")],
        unique=False,
    )
    op.create_foreign_key(
        None, "runs", "run_manifests", ["manifest_id"], ["id"], ondelete="SET NULL"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "runs", type_="foreignkey")  # type: ignore
    op.drop_index("idx_runs_mainfest_id_execution_order_start_time", table_name="runs")
    op.drop_column("runs", "manifest_id")
    op.drop_table("run_manifests")
    # ### end Alembic commands ###

"""Make tasks timestamps timezone aware

Revision ID: f3ecf0246a81
Revises: ff90df680a7d
Create Date: 2023-07-28 14:02:48.682026

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f3ecf0246a81"
down_revision = "ff90df680a7d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """alter table tasks
        alter column created_at type timestamp with time zone,
        alter column updated_at type timestamp with time zone,
        alter column schedule_time type timestamp with time zone,
        alter column start_time type timestamp with time zone,
        alter column finish_time type timestamp with time zone
        """
    )
    pass


def downgrade() -> None:
    pass

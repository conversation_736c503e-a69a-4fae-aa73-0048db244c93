"""create organizations

Revision ID: 9e69fdb8c621
Revises: a5e96ab13892
Create Date: 2024-02-07 14:12:43.265323

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9e69fdb8c621"
down_revision = "a5e96ab13892"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """create table organizations (
            id uuid primary key default gen_random_uuid(),
            display_name text not null,
            created_at timestamp with time zone default now(),
            modified_at timestamp with time zone default now(),
            owner_identity_id uuid references identities(id) on delete restrict
        )
        """
    )
    op.execute(
        """alter table tenants add column organization_id uuid references organizations(id) on delete cascade"""
    )
    op.execute(
        """create unique index tenant_organization_uc on tenants(organization_id, display_name)"""
    )
    op.execute(
        """alter table identities add column organization_id uuid references organizations(id) on delete cascade"""
    )
    op.execute(
        """create index identities_organization_ix on identities(organization_id)"""
    )
    op.execute(
        "alter table pending_identities add column organization_id uuid references organizations(id) on delete cascade"
    )
    op.execute(
        "create index pending_identities_organization_ix on pending_identities(organization_id)"
    )


def downgrade() -> None:
    op.execute(
        """
        drop index pending_identities_organization_ix;
        alter table pending_identities drop column organization_id;
        drop index identities_organization_ix;
        alter table identities drop column organization_id;
        drop index tenant_organization_uc;
        alter table tenants drop column organization_id;
        drop table organizations;
        """
    )

"""usage limits max int

Revision ID: 0554f9154d03
Revises: 44205692e503
Create Date: 2025-06-30 14:14:02.097318

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "0554f9154d03"
down_revision = "44205692e503"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Cap any usage limit values that exceed MAX_INT64 to MAX_INT64
    # This ensures compatibility with both Python int and Go int64 types.
    # Maximum 64-bit integer value (2^63 - 1) = 9223372036854775807
    # We use numeric(50, 1) (max 10^50) to capture any values that even exceed bigint max value.
    op.execute("""
        UPDATE tenants
        SET config = config || '{"max_events_ingested_per_minute": 9223372036854775807}'
        WHERE (config->>'max_events_ingested_per_minute')::numeric(50, 1) > 9223372036854775807
    """)

    op.execute("""
        UPDATE tenants
        SET config = config || '{"max_hourly_tracing_requests": 9223372036854775807}'
        WHERE (config->>'max_hourly_tracing_requests')::numeric(50, 1) > 9223372036854775807
    """)

    op.execute("""
        UPDATE tenants
        SET config = config || '{"max_hourly_tracing_bytes": 9223372036854775807}'
        WHERE (config->>'max_hourly_tracing_bytes')::numeric(50, 1) > 9223372036854775807
    """)

    op.execute("""
        UPDATE tenants
        SET config = config || '{"max_monthly_total_unique_traces": 9223372036854775807}'
        WHERE (config->>'max_monthly_total_unique_traces')::numeric(50, 1) > 9223372036854775807
    """)


def downgrade() -> None:
    pass

-- User for Datastream

-- CREATE USER datastream_user WITH PASSWORD '<replace>';

GRANT USAGE ON SCHEMA public TO datastream_user;
GRANT USAGE ON SCHEMA faker TO datastream_user;
GRANT SELECT on ALL TABLES in SCHEMA public to datastream_user;
GRANT SELECT on ALL TABLES in SCHEMA faker to datastream_user;
ALTER DEFAULT privileges IN SCHEMA public GRANT SELECT on TABLES to datastream_user;
ALTER DEFAULT privileges IN SCHEMA faker GRANT SELECT on TABLES to datastream_user;
GRANT SELECT on ALL SEQUENCES in SCHEMA public to datastream_user;
GRANT SELECT on ALL SEQUENCES in SCHEMA faker to datastream_user;
ALTER DEFAULT privileges IN SCHEMA public GRANT SELECT on SEQUENCES to datastream_user;
ALTER DEFAULT privileges IN SCHEMA faker GRANT SELECT on SEQUENCES to datastream_user;

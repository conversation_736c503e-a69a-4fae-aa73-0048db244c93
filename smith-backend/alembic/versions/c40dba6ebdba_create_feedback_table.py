"""create feedback table

Revision ID: c40dba6ebdba
Revises: d8c51bfc18fa
Create Date: 2023-05-24 09:08:34.450810

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "c40dba6ebdba"
down_revision = "d8c51bfc18fa"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "feedback",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("modified_at", sa.DateTime(), nullable=True),
        sa.Column("run_id", sa.UUID(), nullable=False),
        sa.Column("key", sa.String(), nullable=False),
        sa.Column("score", sa.Numeric(), nullable=True),
        sa.Column("value", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("comment", sa.String(), nullable=True),
        sa.Column("correction", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column(
            "feedback_source", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
        sa.ForeignKeyConstraint(
            ["run_id"],
            ["runs.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_feedback_run_id"), "feedback", ["run_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_feedback_run_id"), table_name="feedback")
    op.drop_table("feedback")
    # ### end Alembic commands ###

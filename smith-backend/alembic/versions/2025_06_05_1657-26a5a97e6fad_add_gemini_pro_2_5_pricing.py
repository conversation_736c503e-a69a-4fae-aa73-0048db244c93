"""add gemini pro 2.5 pricing

Revision ID: 26a5a97e6fad
Revises: 4aab817bd6fc
Create Date: 2025-06-05 16:57:35.556553

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "26a5a97e6fad"
down_revision = "4aab817bd6fc"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Remove the old specific gemini-2.5-pro-preview-03-25 entry
    op.execute(
        """
        DELETE FROM model_price_map
        WHERE name = 'gemini-2.5-pro-preview-03-25' AND priority_order = 36;
        """
    )

    # Add the new flexible gemini-2.5-pro-preview pattern
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time, prompt_cost_details, completion_cost_details)
        VALUES
            (58, 'gemini-2.5-pro-preview', '^(models\/)?gemini-2\.5-pro-preview-\d{2}-\d{2}$', 0.00000125, 0.00001, NULL, '{"cache_read": 0.00000031}', NULL)
        """
    )


def downgrade() -> None:
    # Remove the new flexible pattern entry
    op.execute(
        """
        DELETE FROM model_price_map
        WHERE (priority_order, name) IN (
            (58, 'gemini-2.5-pro-preview')
        );
        """
    )

    # Reinstate the original gemini-2.5-pro-preview-03-25 entry
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
        VALUES
            (36, 'gemini-2.5-pro-preview-03-25', '^gemini-2\.5-pro-preview-03-25$', 0.00000125, 0.00001, NULL)
        """
    )

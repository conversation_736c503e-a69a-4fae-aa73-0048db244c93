"""20231027 run blobs

Revision ID: ad0bffbbe3c3
Revises: bc629c403e36
Create Date: 2023-10-27 07:22:59.221534

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ad0bffbbe3c3"
down_revision = "bc629c403e36"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        create table run_blobs(
            run_id uuid not null references runs(id) on delete cascade,
            key varchar(16) not null,
            inline jsonb,
            s3_url text,
            primary key (run_id, key)
        )
        """
    )

    with op.get_context().autocommit_block():
        op.execute(
            """
            create index concurrently if not exists ix_run_blobs_fts on run_blobs using gin (
                jsonb_to_tsvector('english', inline, '["string"]')
            );
            """
        )


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("drop index concurrently if exists ix_run_blobs_fts;")

    op.execute("drop table run_blobs;")

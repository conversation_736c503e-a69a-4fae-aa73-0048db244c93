"""20231017 identities read_only

Revision ID: f2cdc7802e18
Revises: 44efe2d14c32
Create Date: 2023-10-17 20:55:33.324794

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f2cdc7802e18"
down_revision = "44efe2d14c32"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE identities
        ADD COLUMN read_only BOOLEAN NOT NULL DEFAULT FALSE
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE identities
        DROP COLUMN read_only
        """
    )

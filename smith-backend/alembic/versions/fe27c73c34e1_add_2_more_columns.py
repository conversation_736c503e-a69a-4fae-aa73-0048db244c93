"""add 2 more columns

Revision ID: fe27c73c34e1
Revises: 9ee38ef710b4
Create Date: 2023-06-16 18:41:03.678633

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "fe27c73c34e1"
down_revision = "9ee38ef710b4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tracer_session_stats",
        sa.Column("reference_dataset_ids", sa.ARRAY(sa.UUID()), nullable=True),
    )
    op.add_column(
        "tracer_session_stats",
        sa.Column(
            "feedback_stats", postgresql.JSONB(astext_type=sa.Text()), nullable=True
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tracer_session_stats", "feedback_stats")
    op.drop_column("tracer_session_stats", "reference_dataset_ids")
    # ### end Alembic commands ###

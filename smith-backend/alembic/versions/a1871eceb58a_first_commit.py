"""first commit

Revision ID: a1871eceb58a
Revises:
Create Date: 2023-05-03 18:12:52.751486

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "a1871eceb58a"
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "tenants",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("display_name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "api_keys",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=True),
        sa.Column("api_key", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["tenant_id"],
            ["tenants.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("api_key"),
    )
    op.create_table(
        "dataset",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("modified_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_id"],
            ["tenants.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "tenant_id", name="_datasets_name_tenant_uc"),
    )
    op.create_table(
        "identities",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=True),
        sa.Column("user_id", sa.UUID(), nullable=True),
        sa.Column("privileges", sa.ARRAY(sa.String()), nullable=False),
        sa.ForeignKeyConstraint(
            ["tenant_id"],
            ["tenants.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "tracer_session",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("start_time", sa.DateTime(), nullable=True),
        sa.Column("extra", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("tenant_id", sa.UUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["tenant_id"],
            ["tenants.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("name", "tenant_id", name="_name_tenant_uc"),
    )
    op.create_table(
        "examples",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("modified_at", sa.DateTime(), nullable=True),
        sa.Column("inputs", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("outputs", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("dataset_id", sa.UUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["dataset_id"],
            ["dataset.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_table(
        "runs",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("start_time", sa.DateTime(), nullable=True),
        sa.Column("end_time", sa.DateTime(), nullable=True),
        sa.Column("extra", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("error", sa.String(), nullable=True),
        sa.Column("execution_order", sa.Integer(), nullable=True),
        sa.Column("run_type", sa.String(), nullable=False),
        sa.Column("inputs", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("serialized", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("outputs", postgresql.JSON(astext_type=sa.Text()), nullable=True),
        sa.Column("session_id", sa.UUID(), nullable=True),
        sa.Column("parent_run_id", sa.UUID(), nullable=True),
        sa.Column("reference_example_id", sa.UUID(), nullable=True),
        sa.ForeignKeyConstraint(
            ["parent_run_id"],
            ["runs.id"],
        ),
        sa.ForeignKeyConstraint(
            ["reference_example_id"],
            ["examples.id"],
        ),
        sa.ForeignKeyConstraint(
            ["session_id"],
            ["tracer_session.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("runs")
    op.drop_table("examples")
    op.drop_table("tracer_session")
    op.drop_table("identities")
    op.drop_table("dataset")
    op.drop_table("api_keys")
    op.drop_table("tenants")
    # ### end Alembic commands ###

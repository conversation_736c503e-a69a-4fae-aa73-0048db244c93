"""Add dataset_share_keys

Revision ID: a899ad99af82
Revises: 44efe2d14c32
Create Date: 2023-10-15 15:59:06.371743

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a899ad99af82"
down_revision = "44efe2d14c32"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "datasets_share_keys",
        sa.Column(
            "id",
            sa.UUID(),
            nullable=False,
            server_default=sa.text("(gen_random_uuid())"),
        ),
        sa.Column(
            "created_at",
            sa.types.DateTime(),
            nullable=True,
            server_default=sa.text("(now())"),
        ),
        sa.Column("dataset_id", sa.UUID(), nullable=False),
        sa.Column(
            "share_token",
            sa.UUID(),
            nullable=False,
            server_default=sa.text("(gen_random_uuid())"),
        ),
        sa.ForeignKeyConstraint(["dataset_id"], ["dataset.id"], ondelete="CASCADE"),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(
        op.f("ix_datasets_share_keys_dataset_id"),
        "datasets_share_keys",
        ["dataset_id"],
        unique=True,
    )
    op.create_index(
        op.f("ix_datasets_share_keys_share_token"),
        "datasets_share_keys",
        ["share_token"],
        unique=True,
    )


def downgrade() -> None:
    op.drop_index(
        op.f("ix_datasets_share_keys_share_token"), table_name="datasets_share_keys"
    )
    op.drop_index(
        op.f("ix_datasets_share_keys_dataset_id"), table_name="datasets_share_keys"
    )
    op.drop_table("datasets_share_keys")

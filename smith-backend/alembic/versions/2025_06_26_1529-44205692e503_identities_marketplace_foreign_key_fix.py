"""identities marketplace foreign key fix

Revision ID: 44205692e503
Revises: 1cb24a57fc1b
Create Date: 2025-06-26 15:29:08.409313

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "44205692e503"
down_revision = "1cb24a57fc1b"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # TODO: store ls_user_id in marketplace_request_logs instead of identity_id if we end up using the table
    op.execute("""
        ALTER TABLE IF EXISTS marketplace_request_logs
        DROP CONSTRAINT IF EXISTS marketplace_request_logs_identity_id_fkey;
    """)

    op.execute("""
        ALTER TABLE IF EXISTS marketplace_request_logs
        ADD CONSTRAINT marketplace_request_logs_identity_id_fkey
        FOREIGN KEY (identity_id)
        REFERENCES identities(id)
        ON DELETE CASCADE;
    """)


def downgrade() -> None:
    op.execute("""
        ALTER TABLE marketplace_request_logs
        DROP CONSTRAINT IF EXISTS marketplace_request_logs_identity_id_fkey;
    """)
    op.execute("""
        ALTER TABLE marketplace_request_logs
        ADD CONSTRAINT marketplace_request_logs_identity_id_fkey
        FOREIGN KEY (identity_id)
        REFERENCES identities(id);
    """)

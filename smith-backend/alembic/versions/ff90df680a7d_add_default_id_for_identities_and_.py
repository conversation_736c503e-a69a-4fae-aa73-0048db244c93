"""Add default id for identities and tenants

Revision ID: ff90df680a7d
Revises: 76f43ea63eff
Create Date: 2023-07-27 14:43:22.509733

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "ff90df680a7d"
down_revision = "76f43ea63eff"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE identities
        ALTER COLUMN id SET DEFAULT gen_random_uuid();
        """
    )
    op.execute(
        """
        ALTER TABLE tenants
        ALTER COLUMN id SET DEFAULT gen_random_uuid();
        """
    )


def downgrade() -> None:
    pass

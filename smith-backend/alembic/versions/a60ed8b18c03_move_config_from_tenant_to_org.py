"""move config from tenant to org

Revision ID: a60ed8b18c03
Revises: bbad2d3ec385
Create Date: 2024-02-22 11:17:01.815936

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "a60ed8b18c03"
down_revision = "bbad2d3ec385"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS is_personal BOOLEAN NOT NULL DEFAULT FALSE;
"""
    )


def downgrade() -> None:
    op.execute(
        """
ALTER TABLE organizations DROP COLUMN IF EXISTS is_personal;
"""
    )

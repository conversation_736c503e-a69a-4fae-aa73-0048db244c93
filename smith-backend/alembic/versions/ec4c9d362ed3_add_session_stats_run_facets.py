"""add session_stats.run_facets

Revision ID: ec4c9d362ed3
Revises: 12561ad51135
Create Date: 2023-06-19 11:06:04.976398

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "ec4c9d362ed3"
down_revision = "12561ad51135"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tracer_session_stats",
        sa.Column("run_facets", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tracer_session_stats", "run_facets")
    # ### end Alembic commands ###

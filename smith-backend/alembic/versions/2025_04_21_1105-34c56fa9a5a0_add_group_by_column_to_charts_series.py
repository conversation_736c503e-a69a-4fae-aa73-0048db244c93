"""add_group_by_column_to_charts_series

Revision ID: 34c56fa9a5a0
Revises: 03a2eb8f5b3c
Create Date: 2025-04-21 11:05:31.185548

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "34c56fa9a5a0"
down_revision = "03a2eb8f5b3c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """alter table custom_chart_series add column if not exists group_by jsonb;
        """
    )


def downgrade() -> None:
    op.execute(
        """alter table custom_chart_series drop column if exists group_by;
        """
    )

"""empty message

Revision ID: b17b76c091db
Revises: 2edcc5e6693b
Create Date: 2023-12-13 13:47:52.267619

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b17b76c091db"
down_revision = "2edcc5e6693b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute("drop index concurrently if exists ix_runs_session_fts_root")
        op.execute("drop index concurrently if exists ix_run_blobs_fts")
        op.execute(
            "drop index concurrently if exists "
            "runs_session_id_start_time_id_status_end_time_tokens_llm_idx"
        )
        op.execute(
            "drop index concurrently if exists "
            "runs_session_id_start_time_id_status_end_time_tokens_root_idx"
        )
        op.execute("drop index concurrently if exists runs_id_inc_session_trace_idx")
        op.execute(
            "drop index concurrently if exists runs_id_inc_parent_start_time_idx"
        )
        op.execute("drop index concurrently if exists runs_session_name_root")
        op.execute("drop index concurrently if exists runs_session_type_root")
        op.execute("drop index concurrently if exists runs_session_status_root")
        op.execute("drop index concurrently if exists runs_session_metadata_root")
        op.execute("drop index concurrently if exists runs_session_tags_root")
        op.execute(
            "drop index concurrently if exists "
            "runs_reference_example_id_start_time_id_root_idx"
        )


def downgrade() -> None:
    pass

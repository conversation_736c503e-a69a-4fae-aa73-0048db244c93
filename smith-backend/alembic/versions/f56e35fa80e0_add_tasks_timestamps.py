"""Add tasks timestamps

Revision ID: f56e35fa80e0
Revises: 833b134b9a40
Create Date: 2023-07-21 12:42:55.116957

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "f56e35fa80e0"
down_revision = "833b134b9a40"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "tasks",
        sa.Column(
            "schedule_time",
            sa.DateTime(),
            nullable=True,
            comment="The desired start time",
        ),
    )
    op.add_column(
        "tasks",
        sa.Column(
            "start_time", sa.DateTime(), nullable=True, comment="The actual start time"
        ),
    )
    op.add_column(
        "tasks",
        sa.Column(
            "finish_time",
            sa.DateTime(),
            nullable=True,
            comment="The actual finish time",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("tasks", "finish_time")
    op.drop_column("tasks", "start_time")
    op.drop_column("tasks", "schedule_time")
    # ### end Alembic commands ###

"""create evaluators table

Revision ID: 2ace5840f740
Revises: 5d1d83fb2998
Create Date: 2025-06-18 14:52:40.987196

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "2ace5840f740"
down_revision = "5d1d83fb2998"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Create enum type first
    op.execute(
        """
        create type evaluator_type as enum ('llm');
        """
    )

    op.execute(
        """
        create table if not exists evaluators (
            id uuid primary key default gen_random_uuid(),
            name varchar(255) not null,
            type evaluator_type not null default 'llm'
        );
        """
    )

    op.execute(
        """
        create table if not exists llm_evaluators (
            evaluator_id uuid primary key references evaluators(id) on delete cascade,
            prompt_id uuid not null references hub_repos(id) on delete cascade,
            commit_hash_or_tag text,
            variable_mapping jsonb
        );

        create index if not exists idx_llm_evaluators_prompt_id on llm_evaluators(prompt_id);
        """
    )

    op.execute(
        """
        alter table if exists run_rules add column if not exists evaluator_id uuid unique references evaluators(id) on delete cascade;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        alter table if exists run_rules drop column if exists evaluator_id;
        """
    )

    op.execute(
        """
        drop table if exists llm_evaluators;

        drop index if exists idx_llm_evaluators_prompt_id;
        """
    )

    op.execute(
        """
        drop table if exists evaluators;
        """
    )

    op.execute(
        """
        drop type if exists evaluator_type;
        """
    )

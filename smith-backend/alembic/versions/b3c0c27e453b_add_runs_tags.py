"""Add runs.tags

Revision ID: b3c0c27e453b
Revises: 0a63ae4158f4
Create Date: 2023-06-08 16:12:11.751232

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b3c0c27e453b"
down_revision = "0a63ae4158f4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("runs", sa.Column("tags", sa.ARRAY(sa.String())))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("runs", "tags")
    # ### end Alembic commands ###

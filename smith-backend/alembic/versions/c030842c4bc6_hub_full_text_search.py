"""hub full text search

Revision ID: c030842c4bc6
Revises: 1ffe115e634a
Create Date: 2023-09-05 16:58:04.656365

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "c030842c4bc6"
down_revision = "1ffe115e634a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """
            CREATE INDEX concurrently hub_repos_fts_idx ON hub_repos USING gin (
                (
                    to_tsvector('english', coalesce(repo_handle, '')) ||
                    to_tsvector('english', coalesce(description, '')) ||
                    to_tsvector('english', coalesce(readme, '[]')) ||
                    jsonb_to_tsvector('english', coalesce(tags, '[]'), '["string"]')
                )
            );
            """
        )


def downgrade() -> None:
    op.execute("DROP INDEX if exists hub_repos_fts_idx;")

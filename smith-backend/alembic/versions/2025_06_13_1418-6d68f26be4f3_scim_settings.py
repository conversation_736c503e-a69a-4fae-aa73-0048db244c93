"""scim settings

Revision ID: 6d68f26be4f3
Revises: a168b2657a9b
Create Date: 2025-06-13 14:18:22.724041

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "6d68f26be4f3"
down_revision = "a168b2657a9b"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS scim_bearer_tokens ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT now();
    """)
    # forgot this in original table definition
    op.execute("""
        ALTER TABLE IF EXISTS scim_bearer_tokens ALTER COLUMN id SET DEFAULT gen_random_uuid();
    """)
    op.execute(
        """
        CREATE TRIGGER update_scim_bearer_tokens_updated_at
        BEFORE UPDATE ON scim_bearer_tokens
        FOR EACH ROW
        EXECUTE PROCEDURE update_updated_at_column();
        """
    )
    op.execute("""
        UPDATE provider_users SET provisioning_method = 'saml:jit' WHERE provider = 'supabase:sso';
    """)
    # SAML SSO in cloud (supabase:sso): JIT or SCIM
    # OIDC in self-hosted (custom-oidc): SCIM or null
    # Other providers (Supabase non-SSO in cloud & basic auth a.k.a. email in self-hosted): null
    op.execute("""
        ALTER TABLE IF EXISTS provider_users ADD CONSTRAINT ck_provider_users_provisioning_method CHECK (
            (provider = 'supabase:sso' AND (provisioning_method IN ('saml:jit', 'scim'))) OR
            (provider = 'custom-oidc' AND (provisioning_method IS NULL OR provisioning_method = 'scim')) OR
            (provider NOT IN ('supabase:sso', 'custom-oidc') AND provisioning_method IS NULL)
        );
    """)
    op.execute("""
        UPDATE tenants SET updated_at = GREATEST(created_at, updated_at);
    """)


def downgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS provider_users DROP CONSTRAINT IF EXISTS ck_provider_users_provisioning_method;
    """)
    op.execute("""
        UPDATE provider_users SET provisioning_method = NULL WHERE provider = 'supabase:sso';
    """)
    op.execute("""
        DROP TRIGGER IF EXISTS update_scim_bearer_tokens_updated_at ON scim_bearer_tokens;
    """)
    op.execute("""
        ALTER TABLE IF EXISTS scim_bearer_tokens ALTER COLUMN id DROP DEFAULT;
    """)
    op.execute("""
        ALTER TABLE IF EXISTS scim_bearer_tokens DROP COLUMN IF EXISTS updated_at;
    """)

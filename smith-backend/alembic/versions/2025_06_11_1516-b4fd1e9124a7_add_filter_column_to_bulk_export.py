"""add_filter_column_to_bulk_export

Revision ID: b4fd1e9124a7
Revises: b51a53b61276
Create Date: 2025-06-11 15:16:11.295676

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b4fd1e9124a7"
down_revision = "b51a53b61276"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS bulk_exports 
        ADD COLUMN IF NOT EXISTS filter TEXT DEFAULT NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS bulk_exports 
        DROP COLUMN IF EXISTS filter;
        """
    )

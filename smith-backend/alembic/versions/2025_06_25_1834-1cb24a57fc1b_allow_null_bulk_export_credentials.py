"""allow null bulk export credentials

Revision ID: 1cb24a57fc1b
Revises: 1b7d505d440b
Create Date: 2025-06-25 18:34:49.253042

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "1cb24a57fc1b"
down_revision = "1b7d505d440b"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS bulk_export_destinations
        ALTER COLUMN encrypted_credentials DROP NOT NULL;
    """)


def downgrade() -> None:
    op.execute("""
        ALTER TABLE IF EXISTS bulk_export_destinations
        ALTER COLUMN encrypted_credentials SET NOT NULL;
    """)

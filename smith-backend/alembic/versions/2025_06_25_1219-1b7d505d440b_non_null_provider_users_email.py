"""non-null provider_users.email

Revision ID: 1b7d505d440b
Revises: 6928e1153e87
Create Date: 2025-06-25 12:19:39.253602

"""

from lc_config.settings import shared_settings

from alembic import op

# revision identifiers, used by Alembic.
revision = "1b7d505d440b"
down_revision = "6928e1153e87"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # set email to the corresponding users.email if null
    if shared_settings.IS_SELF_HOSTED:
        op.execute("""
            UPDATE provider_users
            SET email = users.email
            FROM users
            WHERE provider_users.ls_user_id = users.ls_user_id
            AND provider_users.email = '';
        """)

    # There is a single entry in prod that needs updating. This will be run manually.
    # The root email attribute is NULL in Supabase, but raw_user_meta_data.email is not empty.
    # if shared_settings.LANGCHAIN_ENV == "production":
    #     op.execute("""
    #         UPDATE users set email = '<redacted>' WHERE ls_user_id = 'bf985ebf-c3bf-462c-9dff-db80417d73ea' AND email = '';
    #     """)
    #     op.execute("""
    #         UPDATE provider_users set email = '<redacted>' WHERE ls_user_id = 'bf985ebf-c3bf-462c-9dff-db80417d73ea' AND email = '';
    #     """)

    # create triggers to validate email is not empty for new rows
    op.execute("""
        CREATE OR REPLACE FUNCTION validate_user_email()
        RETURNS TRIGGER AS $$
        BEGIN
            IF NEW.email = '' THEN
                RAISE EXCEPTION 'email cannot be empty';
            END IF;
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)

    op.execute("""
        CREATE TRIGGER validate_user_email_trigger
        BEFORE INSERT OR UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION validate_user_email();
    """)

    op.execute("""
        CREATE TRIGGER validate_provider_user_email_trigger
        BEFORE INSERT OR UPDATE ON provider_users
        FOR EACH ROW
        EXECUTE FUNCTION validate_user_email();
    """)


def downgrade() -> None:
    op.execute("""
        DROP TRIGGER IF EXISTS validate_provider_user_email_trigger ON provider_users;
    """)
    op.execute("""
        DROP TRIGGER IF EXISTS validate_user_email_trigger ON users;
    """)
    op.execute("""
        DROP FUNCTION IF EXISTS validate_user_email();
    """)

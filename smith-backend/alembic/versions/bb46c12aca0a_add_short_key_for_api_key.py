"""add short_key for api key

Revision ID: bb46c12aca0a
Revises: 2b3747de4dc4
Create Date: 2023-06-27 18:39:54.581206

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "bb46c12aca0a"
down_revision = "2b3747de4dc4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("api_keys", sa.Column("short_key", sa.String()))
    op.execute(
        """
        UPDATE api_keys
        SET short_key = '***'
        WHERE short_key IS NULL
        """
    )
    op.alter_column("api_keys", "short_key", nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("api_keys", "short_key")
    # ### end Alembic commands ###

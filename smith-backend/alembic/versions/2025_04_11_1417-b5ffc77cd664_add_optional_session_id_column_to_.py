"""add_optional_session_id_column_to_dashboards

Revision ID: b5ffc77cd664
Revises: 3b6868da6a25
Create Date: 2025-04-11 14:17:09.864517

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b5ffc77cd664"
down_revision = "3b6868da6a25"
branch_labels = None
depends_on = None


def upgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            """ALTER TABLE custom_chart_section 
            ADD COLUMN IF NOT EXISTS session_id UUID,
            ADD CONSTRAINT fk_session FOREIGN KEY (session_id) REFERENCES tracer_session(id) ON DELETE CASCADE NOT VALID,
            ADD CONSTRAINT unique_session_id UNIQUE (session_id);"""
        )

    with op.get_context().autocommit_block():
        op.execute(
            """ALTER TABLE custom_chart_section VALIDATE CONSTRAINT fk_session;
            """
        )


def downgrade() -> None:
    op.execute(
        """ALTER TABLE custom_chart_section
        DROP CONSTRAINT IF EXISTS unique_session_id,
        DROP CONSTRAINT IF EXISTS fk_session,
        DROP COLUMN IF EXISTS session_id;"""
    )

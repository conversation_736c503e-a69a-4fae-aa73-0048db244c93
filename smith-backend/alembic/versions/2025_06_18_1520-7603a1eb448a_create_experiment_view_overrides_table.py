"""Create experiment view overrides table

Revision ID: 7603a1eb448a
Revises: e2218df391f6
Create Date: 2025-06-18 15:20:10.354221

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "7603a1eb448a"
down_revision = "e2218df391f6"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute("""
    CREATE TABLE IF NOT EXISTS experiment_view_overrides (
        id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
        dataset_id UUID NOT NULL
            REFERENCES dataset(id) ON DELETE CASCADE,
        column_overrides jsonb NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
        modified_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
    );
    
    CREATE INDEX IF NOT EXISTS idx_experiment_view_overrides_dataset_id 
    ON experiment_view_overrides (dataset_id);
    
    ALTER TABLE experiment_view_overrides 
    ADD CONSTRAINT experiment_view_overrides_dataset_id_key UNIQUE (dataset_id);
    """)


def downgrade() -> None:
    op.execute("""
    ALTER TABLE IF EXISTS experiment_view_overrides 
    DROP CONSTRAINT IF EXISTS experiment_view_overrides_dataset_id_key;
    
    DROP INDEX IF EXISTS idx_experiment_view_overrides_dataset_id;
    DROP TABLE IF EXISTS experiment_view_overrides;
    """)

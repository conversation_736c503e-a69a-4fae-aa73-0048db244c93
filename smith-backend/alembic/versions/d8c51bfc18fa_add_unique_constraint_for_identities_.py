"""add unique constraint for identities and make runs extra jsonb

Revision ID: d8c51bfc18fa
Revises: 40b56c31b6a1
Create Date: 2023-05-17 14:26:04.856503

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d8c51bfc18fa"
down_revision = "40b56c31b6a1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "runs",
        "extra",
        existing_type=postgresql.JSON(astext_type=sa.Text()),
        type_=postgresql.JSONB(astext_type=sa.Text()),
        postgresql_using="extra::jsonb",
    )

    op.create_unique_constraint(
        "uq_tenant_user", "identities", ["tenant_id", "user_id"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("uq_tenant_user", "identities", type_="unique")

    op.alter_column(
        "runs",
        "extra",
        existing_type=postgresql.JSONB(astext_type=sa.Text()),
        type_=postgresql.JSON(astext_type=sa.Text()),
        postgresql_using="extra::json",
    )
    # ### end Alembic commands ###

"""add expires_at column to api keys

Revision ID: b96170cb8e9b
Revises: 7603a1eb448a
Create Date: 2025-06-18 15:32:21.276763

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "b96170cb8e9b"
down_revision = "7603a1eb448a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS api_keys
        ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ NULL DEFAULT NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE IF EXISTS api_keys
        DROP COLUMN IF EXISTS expires_at;
        """
    )

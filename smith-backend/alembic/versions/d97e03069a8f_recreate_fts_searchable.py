"""Recreate fts_searchable

Revision ID: d97e03069a8f
Revises: 452e28495206
Create Date: 2023-06-21 13:10:20.332388

"""

import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

from alembic import op

# revision identifiers, used by Alembic.
revision = "d97e03069a8f"
down_revision = "452e28495206"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "runs",
        sa.Column(
            "fts_searchable",
            postgresql.TSVECTOR(),
            sa.Computed(
                "\nto_tsvector('english', name)\n|| array_to_tsvector(coalesce(tags, '{}'))\n|| to_tsvector('english', coalesce(error, ''))\n|| to_tsvector('english', run_type)\n|| jsonb_to_tsvector('english', coalesce(inputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(outputs, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(events, '{}'), '[\"string\", \"numeric\", \"key\"]')\n|| jsonb_to_tsvector('english', coalesce(extra, '{}'), '[\"string\", \"numeric\", \"key\"]')\n",
            ),
            nullable=True,
        ),
    )
    op.create_index(
        "idx_fts", "runs", ["fts_searchable"], unique=False, postgresql_using="gin"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("idx_fts", table_name="runs", postgresql_using="gin")
    op.drop_column("runs", "fts_searchable")
    # ### end Alembic commands ###

"""populate tracer_session_stats for existing sessions

Revision ID: b8f2dec24d22
Revises: fe27c73c34e1
Create Date: 2023-06-16 18:47:07.295960

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "b8f2dec24d22"
down_revision = "fe27c73c34e1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        sa.text(
            """
with

run_stats as (
    select
        session_id as tracer_session_id,
        count(*) as run_count,
        max(start_time) as last_run_start_time,
        sum(cast(extra->>'total_tokens' as int)) as total_tokens,
        sum(cast(extra->>'prompt_tokens' as int)) as prompt_tokens,
        sum(cast(extra->>'completion_tokens' as int)) as completion_tokens,
        avg(end_time - start_time) as avg_latency,
        array_agg(distinct dataset_id) filter (where dataset_id is not null)
            as reference_dataset_ids
    from runs
    left join examples on runs.reference_example_id = examples.id
    where session_id = any(select distinct id from tracer_session) and execution_order = 1
    group by 1
),

feedback_stats as (
    with stats as (
        select
            session_id as tracer_session_id,
            key,
            jsonb_build_object(
                'n', count(*),
                'mode', mode() within group (order by score),
                'avg', avg(score)
            ) as aggs
        from runs
        inner join feedback on runs.id = feedback.run_id
        where session_id = any(select distinct id from tracer_session)
        group by 1, 2
    )

    select
        tracer_session_id,
        jsonb_object_agg(key, aggs) as feedback_stats
    from stats
    group by 1
),

all_stats as (
    select run_stats.*, feedback_stats
    from run_stats
    full outer join feedback_stats
        on run_stats.tracer_session_id = feedback_stats.tracer_session_id
)

insert into tracer_session_stats (
    tracer_session_id,
    run_count,
    last_run_start_time,
    total_tokens,
    prompt_tokens,
    completion_tokens,
    avg_latency,
    reference_dataset_ids,
    feedback_stats
)
select * from all_stats
on conflict (tracer_session_id) do update
set run_count = excluded.run_count,
    last_run_start_time = excluded.last_run_start_time,
    total_tokens = excluded.total_tokens,
    prompt_tokens = excluded.prompt_tokens,
    completion_tokens = excluded.completion_tokens,
    avg_latency = excluded.avg_latency,
    reference_dataset_ids = excluded.reference_dataset_ids,
    feedback_stats = excluded.feedback_stats
where tracer_session_stats.tracer_session_id = any(select distinct id from tracer_session)
returning tracer_session_id
"""
        )
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###

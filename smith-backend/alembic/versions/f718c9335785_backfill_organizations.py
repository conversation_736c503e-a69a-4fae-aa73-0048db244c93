"""backfill organizations

Revision ID: f718c9335785
Revises: 9e69fdb8c621
Create Date: 2024-02-07 15:16:29.793826

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f718c9335785"
down_revision = "9e69fdb8c621"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
with

first_identities as (
    select id, tenant_id
    from (
        select
            id,
            tenant_id,
            row_number() over (partition by tenant_id order by created_at) as identity_order
        from identities
    ) identities_with_order
    where identity_order = 1
),

tenants_w_first_identity as (
    select
        display_name,
        gen_random_uuid() as organization_id,
        tenants.id as tenant_id,
        first_identities.id as identity_id
    from tenants
    left join first_identities on tenants.id = first_identities.tenant_id
    where tenants.organization_id is null
),

create_organizations as (
    insert into organizations (display_name, id, owner_identity_id)
    select display_name, organization_id, identity_id
    from tenants_w_first_identity
),

update_tenants as (
    update tenants
    set organization_id = tenants_w_first_identity.organization_id
    from tenants_w_first_identity
    where tenants.id = tenants_w_first_identity.tenant_id
),

update_identities as (
    update identities
    set organization_id = tenants_w_first_identity.organization_id
    from tenants_w_first_identity
    where identities.tenant_id = tenants_w_first_identity.tenant_id
        and identities.organization_id is null
),

update_pending_identities as (
    update pending_identities
    set organization_id = tenants_w_first_identity.organization_id
    from tenants_w_first_identity
    where pending_identities.tenant_id = tenants_w_first_identity.tenant_id
        and pending_identities.organization_id is null
)

select 1
"""
    )


def downgrade() -> None:
    pass

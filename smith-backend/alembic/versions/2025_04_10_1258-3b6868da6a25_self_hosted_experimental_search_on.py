"""self_hosted_experimental_search_on

Revision ID: 3b6868da6a25
Revises: 42826a08d997
Create Date: 2025-04-10 12:58:25.084913

"""

from alembic import op
from app import config

# revision identifiers, used by Alembic.
revision = "3b6868da6a25"
down_revision = "42826a08d997"
branch_labels = None
depends_on = None


def upgrade() -> None:
    if config.settings.IS_SELF_HOSTED:
        op.execute(
            """UPDATE organizations SET config = jsonb_set(config, '{langsmith_experimental_search_enabled}', 'true'::jsonb);"""
        )


def downgrade() -> None:
    if config.settings.IS_SELF_HOSTED:
        op.execute(
            """UPDATE organizations SET config = jsonb_set(config, '{langsmith_experimental_search_enabled}', 'false'::jsonb);"""
        )

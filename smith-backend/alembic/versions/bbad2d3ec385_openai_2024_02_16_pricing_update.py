"""openai 2024-02-16 pricing update

Revision ID: bbad2d3ec385
Revises: cb67b9b68976
Create Date: 2024-02-25 03:01:00.749562

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "bbad2d3ec385"
down_revision = "cb67b9b68976"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        sa.text(
            """
            INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
            VALUES
                (9, 'gpt-3.5-turbo-1106', '^gpt-(3\.5|35)-turbo-1106$', 0.000001, 0.000002, NULL),
                (10, 'gpt-3.5-turbo', '^gpt-(3\.5|35)-turbo(-\d{4})?$', 0.0000005, 0.0000015, '2024-02-16'),
                (11, 'gpt-3.5-turbo-16k', '^gpt-(3\.5|35)-turbo-16k(-\d{4})?$', 0.0000005, 0.0000015, '2024-02-16');
    """
        )
    )


def downgrade() -> None:
    pass

"""add_created_by_updated_by_columns_to_host_project_templates

Revision ID: d247f4777af5
Revises: 38b099c8d334
Create Date: 2025-04-04 12:52:34.576129

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d247f4777af5"
down_revision = "38b099c8d334"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add created_by and updated_by columns as optional text fields
    op.execute(
        """
        ALTER TABLE host_project_templates
        ADD COLUMN created_by UUID,
        ADD COLUMN updated_by UUID;
        """
    )


def downgrade() -> None:
    # Remove the columns if migration is rolled back
    op.execute(
        """
        ALTER TABLE host_project_templates
        DROP COLUMN created_by,
        DROP COLUMN updated_by;
        """
    )

"""use uuid for migrations

Revision ID: 9fe9ca4460ae
Revises: 82debe62139d
Create Date: 2025-04-18 06:10:41.306573

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "9fe9ca4460ae"
down_revision = "82debe62139d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        ALTER TABLE remote_metrics
        DROP COLUMN IF EXISTS tenant_id,
        DROP COLUMN IF EXISTS self_hosted_customer_id;
        """
    )
    op.execute(
        """
        ALTER TABLE remote_metrics
        ADD COLUMN tenant_id UUID DEFAULT NULL,
        ADD COLUMN self_hosted_customer_id UUID DEFAULT NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE remote_metrics
        DROP COLUMN IF EXISTS tenant_id,
        DROP COLUMN IF EXISTS self_hosted_customer_id;
        """
    )
    op.execute(
        """
        ALTER TABLE remote_metrics
        ADD COLUMN tenant_id VARCHAR(255) DEFAULT NULL,
        ADD COLUMN self_hosted_customer_id VARCHAR(255) DEFAULT NULL;
        """
    )

"""Permit models prefix in front of default Gemini price maps

Revision ID: 083a9fb29dfc
Revises: 46275283308a
Create Date: 2025-04-29 15:16:14.067688

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "083a9fb29dfc"
down_revision = "46275283308a"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-2\.5-pro-preview-03-25$'
        WHERE name = 'gemini-2.5-pro-preview-03-25' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-2\.0-flash(-\d*)?$'
        WHERE name = 'gemini-2.0-flash' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-2\.0-flash-lite(-\d*)?$'
        WHERE name = 'gemini-2.0-flash-lite' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-flash(-\d*)?$'
        WHERE name = 'gemini-1.5-flash' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-flash-latest$'
        WHERE name = 'gemini-1.5-flash-latest' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-flash-8b(-\d*)?$'
        WHERE name = 'gemini-1.5-flash-8b' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-flash-8b-latest$'
        WHERE name = 'gemini-1.5-flash-8b-latest' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-pro(-\d*)?$'
        WHERE name = 'gemini-1.5-pro' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^(models\/)?gemini-1\.5-pro-latest$'
        WHERE name = 'gemini-1.5-pro-latest' AND tenant_id IS NULL;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE model_price_map
        SET match_pattern = '^gemini-2\.5-pro-preview-03-25$'
        WHERE name = 'gemini-2.5-pro-preview-03-25' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-2\.0-flash(-\d*)?$'
        WHERE name = 'gemini-2.0-flash' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-2\.0-flash-lite(-\d*)?$'
        WHERE name = 'gemini-2.0-flash-lite' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-flash(-\d*)?$'
        WHERE name = 'gemini-1.5-flash' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-flash-latest$'
        WHERE name = 'gemini-1.5-flash-latest' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-flash-8b(-\d*)?$'
        WHERE name = 'gemini-1.5-flash-8b' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-flash-8b-latest$'
        WHERE name = 'gemini-1.5-flash-8b-latest' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-pro(-\d*)?$'
        WHERE name = 'gemini-1.5-pro' AND tenant_id IS NULL;

        UPDATE model_price_map
        SET match_pattern = '^gemini-1\.5-pro-latest$'
        WHERE name = 'gemini-1.5-pro-latest' AND tenant_id IS NULL;
        """
    )

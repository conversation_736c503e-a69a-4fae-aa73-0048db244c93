"""update last_used_at to timestamptz

Revision ID: 5e2ff23ed13f
Revises: d07914a8286b
Create Date: 2025-06-17 14:33:57.732227

"""

# revision identifiers, used by Alembic.
revision = "5e2ff23ed13f"
down_revision = "d07914a8286b"
branch_labels = None
depends_on = None


# Use IF EXISTS for ALTER TABLE commands because some tables don't exist in self-hosted.
def upgrade() -> None:
    # Revert with pass
    pass
    # op.execute(
    #     """
    #     ALTER TABLE IF EXISTS api_keys
    #     ALTER COLUMN last_used_at TYPE TIMESTAMPTZ USING last_used_at AT TIME ZONE 'UTC';
    #     """
    # )


def downgrade() -> None:
    # Revert with pass
    pass
    # op.execute(
    #     """
    #     ALTER TABLE IF EXISTS api_keys
    #     ALTER COLUMN last_used_at TYPE TIMESTAMP WITHOUT TIME ZONE;
    #     """
    # )

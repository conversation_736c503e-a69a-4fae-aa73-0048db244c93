"""add new openai 4.1 model pricing

Revision ID: 5f3c19df5607
Revises: baacccd278d8
Create Date: 2025-04-15 17:46:49.761867

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "5f3c19df5607"
down_revision = "baacccd278d8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost, start_time)
        VALUES
            (45, 'gpt-4.1', '^gpt-4\.1$', 0.000002, 0.000008, NULL),
            (46, 'gpt-4.1-2025-04-14', '^gpt-4\.1-2025-04-14$', 0.000002, 0.000008, NULL),
            (47, 'gpt-4.1-mini', '^gpt-4\.1-mini$', 0.0000004, 0.0000016, NULL),
            (48, 'gpt-4.1-mini-2025-04-14', '^gpt-4\.1-mini-2025-04-14$', 0.0000004, 0.0000016, NULL),
            (49, 'gpt-4.1-nano', '^gpt-4\.1-nano$', 0.0000001, 0.0000004, NULL),
            (50, 'gpt-4.1-nano-2025-04-14', '^gpt-4\.1-nano-2025-04-14$', 0.0000001, 0.0000004, NULL)
        """
    )


def downgrade() -> None:
    pass

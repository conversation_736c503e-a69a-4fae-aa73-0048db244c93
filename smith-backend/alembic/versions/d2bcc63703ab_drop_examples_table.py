"""drop examples table

Revision ID: d2bcc63703ab
Revises: 225bc23b4fba
Create Date: 2024-01-11 18:20:17.417342

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "d2bcc63703ab"
down_revision = "225bc23b4fba"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # We already duplicated the examples table as examples_log in a previous migration.
    # But there may have been some examples inserted during the migration.
    # We need to migrate those examples to the examples_log table before we drop the examples table.
    op.execute(
        """
        INSERT INTO examples_log (id, dataset_id, created_at, modified_at, inputs, outputs, source_run_id)
        SELECT e.id, e.dataset_id, timezone('UTC', e.created_at),coalesce(timezone('UTC', coalesce(modified_at, created_at)), now()), e.inputs, e.outputs, e.source_run_id
        FROM examples e
        ON CONFLICT DO NOTHING;
        """
    )

    op.execute(
        """
        ALTER TABLE dataset
        ALTER COLUMN created_at SET DATA TYPE TIMESTAMP WITH TIME ZONE,
        ALTER COLUMN modified_at SET DATA TYPE TIMESTAMP WITH TIME ZONE;
        """
    )


def downgrade() -> None:
    pass

"""model_price_map

Revision ID: a5e96ab13892
Revises: d7426becda4f
Create Date: 2024-02-02 01:08:49.275312

"""

import sqlalchemy as sa

from alembic import op

# revision identifiers, used by Alembic.
revision = "a5e96ab13892"
down_revision = "d7426becda4f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        sa.text(
            """
            CREATE TABLE model_price_map (
                id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                tenant_id uuid NULL REFERENCES tenants(id),
                name text NOT NULL,
                created_at timestamptz NOT NULL DEFAULT now(),
                start_time timestamptz NULL,
                match_path text[] DEFAULT array['model', 'model_name', 'model_id', 'model_path', 'endpoint_name'],
                match_pattern TEXT NOT NULL,
                priority_order INT NOT NULL DEFAULT 0,
                prompt_cost DECIMAL(18, 12) NOT NULL,
                completion_cost DECIMAL(18, 12) NOT NULL
            );
            """
        )
    )

    with op.get_context().autocommit_block():
        op.execute(
            sa.text(
                """
                CREATE INDEX idx_model_price_map_tenant_id ON model_price_map (tenant_id);
                """
            )
        )

    op.execute(
        sa.text(
            """
            INSERT INTO model_price_map (priority_order, name, match_pattern, prompt_cost, completion_cost) 
            VALUES 
                (0, 'gpt-3.5-turbo-16k', '^gpt-(3\.5|35)-turbo-16k(-\d{4})?$', 0.000003, 0.000004),
                (1, 'gpt-3.5-turbo', '^gpt-(3\.5|35)-turbo(-\d{4})?$', 0.0000015, 0.000002),
                (2, 'gpt-4-vision-preview', '^gpt-4(-\d{4})?-vision-preview$', 0.00001, 0.00003),
                (3, 'gpt-4-turbo-preview', '^gpt-4-(\d{4}|turbo)-preview$', 0.00001, 0.00003),
                (4, 'gpt-4-32k', '^gpt-4-32k(-\d{4})?$', 0.00006, 0.00012),
                (5, 'gpt-4', '^gpt-4(-\d{4})?$', 0.00003, 0.00006),
                (6, 'ft:gpt-3.5-turbo', '^ft:gpt-(3\.5|35)-turbo$', 0.000012, 0.000016),
                (7, 'gpt-3.5-turbo-instruct', '^gpt-(3\.5|35)-turbo-instruct$', 0.0000015, 0.0000020),
                (8, 'gpt-3.5-turbo-0125', '^gpt-(3\.5|35)-turbo-0125$', 0.0000005, 0.0000015);     
        """
        )
    )

    pass


def downgrade() -> None:
    with op.get_context().autocommit_block():
        op.execute(
            sa.text(
                """
                DROP INDEX idx_model_price_map_tenant_id
                """
            )
        )
    op.execute(
        sa.text(
            """
            DROP TABLE model_price_map
            """
        )
    )
    pass

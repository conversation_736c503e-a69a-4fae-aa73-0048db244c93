import argparse
import asyncio
import datetime
import math

import structlog
from lc_database.clickhouse import ClickhouseClient, clickhouse_client

# ────────────────────────────── CONFIG ────────────────────────────── #

CLIENT_TYPE = ClickhouseClient.INTERNAL_ANALYTICS_SLOW
WINDOW_HOURS = 12
LOG_EVERY = 1

logger = structlog.get_logger(__name__)

INSERT_TEMPLATE = """
INSERT INTO runs_token_counts
(
    id, tenant_id, session_id, is_root, source_id, start_time,
    prompt_tokens, completion_tokens, total_tokens, first_token_time,
    prompt_cost, completion_cost, total_cost,
    prompt_token_details, completion_token_details, prompt_cost_details, completion_cost_details,
    modified_at, is_deleted,
    trace_tier, trace_ttl_seconds, trace_first_received_at
)
SETTINGS
  parts_to_throw_insert = 10000,
  parts_to_delay_insert = 5000,
  max_insert_block_size = 1000000000,
  min_insert_block_size_rows = 2000000,
  min_insert_block_size_bytes = 1000000000,
  max_threads = 16,
  max_insert_threads = {insert_threads},
  send_progress_in_http_headers = 1
SELECT
    r.id,
    r.tenant_id,
    r.session_id,
    r.is_root,
    r.id as source_id,
    toStartOfMinute(r.start_time) as start_time,
    r.prompt_tokens,
    r.completion_tokens,
    r.total_tokens,
    r.first_token_time,
    r.prompt_cost,
    r.completion_cost,
    r.total_cost,
    r.prompt_token_details,
    r.completion_token_details,
    r.prompt_cost_details,
    r.completion_cost_details,
    r.modified_at,
    r.is_deleted,
    r.trace_tier,
    r.trace_ttl_seconds,
    r.trace_first_received_at
FROM runs r
WHERE
    r.modified_at >= {start}
    AND r.modified_at < {end}
    AND r.is_deleted = 0
    AND r.run_type = 'llm'
"""


async def _min_max_modified_at() -> tuple[datetime.datetime, datetime.datetime]:
    async with clickhouse_client(client_type=CLIENT_TYPE) as ch:
        result = await ch.fetchrow(
            "get_min_max_modified_at",
            "SELECT min(modified_at) as min_modified_at, max(modified_at) as max_modified_at "
            "FROM runs WHERE is_deleted = 0 AND run_type = 'llm'",
        )
        # Make datetimes timezone-aware by assuming UTC
        min_dt = result["min_modified_at"]
        max_dt = result["max_modified_at"]
        if min_dt.tzinfo is None:
            min_dt = min_dt.replace(tzinfo=datetime.timezone.utc)
        if max_dt.tzinfo is None:
            max_dt = max_dt.replace(tzinfo=datetime.timezone.utc)
        return min_dt, max_dt


async def _insert_window(
    start: datetime.datetime, end: datetime.datetime, insert_threads: int
) -> None:
    params = {
        "start": start.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "end": end.strftime("%Y-%m-%d %H:%M:%S.%f"),
        "insert_threads": insert_threads,
    }
    async with clickhouse_client(client_type=CLIENT_TYPE) as ch:
        await ch.execute("insert_runs_token_counts", INSERT_TEMPLATE, params=params)


async def _process_batch(
    batch_info: tuple[int, datetime.datetime, datetime.datetime], insert_threads: int
) -> None:
    """Process a single batch of data."""
    batch_num, window_start, window_end = batch_info
    try:
        await _insert_window(window_start, window_end, insert_threads)
    except Exception as e:
        logger.error("batch failed, continuing", batch=batch_num, error=str(e))
    else:
        if batch_num % LOG_EVERY == 0:
            logger.info(
                "backfill progress",
                batch=batch_num + 1,
                window_start=window_start.isoformat(),
                window_end=window_end.isoformat(),
            )


async def main() -> None:
    parser = argparse.ArgumentParser(
        description="Backfill runs_token_counts table with data from runs table"
    )
    parser.add_argument(
        "--insert-threads",
        type=int,
        default=2,
        help="Number of insert threads for ClickHouse (default: 2)",
    )
    parser.add_argument(
        "--start-time",
        type=str,
        help="Start time for backfill (ISO format, e.g., 2024-01-01T00:00:00Z). If not provided, will use min modified_at from runs table.",
    )
    parser.add_argument(
        "--end-time",
        type=str,
        help="End time for backfill (ISO format, e.g., 2024-12-31T23:59:59Z). If not provided, will use max modified_at from runs table.",
    )
    args = parser.parse_args()

    # Get the time range
    if args.start_time and args.end_time:
        start_dt = datetime.datetime.fromisoformat(
            args.start_time.replace("Z", "+00:00")
        )
        end_dt = datetime.datetime.fromisoformat(args.end_time.replace("Z", "+00:00"))
    else:
        start_dt, end_dt = await _min_max_modified_at()

    total_hours = math.ceil((end_dt - start_dt).total_seconds() / 3600)
    num_batches = math.ceil(total_hours / WINDOW_HOURS)

    logger.info(
        "backfill start",
        start=start_dt,
        end=end_dt,
        window_hours=WINDOW_HOURS,
        batches=num_batches,
        insert_threads=args.insert_threads,
    )

    # Calculate all batch windows
    batch_tasks = []
    window_start = start_dt

    for i in range(num_batches):
        window_end = min(window_start + datetime.timedelta(hours=WINDOW_HOURS), end_dt)
        batch_tasks.append((i, window_start, window_end))
        window_start = window_end

    semaphore = asyncio.Semaphore(4)

    async def process_batch_with_semaphore(batch_info):
        async with semaphore:
            return await _process_batch(batch_info, args.insert_threads)

    await asyncio.gather(
        *[process_batch_with_semaphore(batch) for batch in batch_tasks]
    )

    logger.info("backfill complete")


if __name__ == "__main__":
    asyncio.run(main())

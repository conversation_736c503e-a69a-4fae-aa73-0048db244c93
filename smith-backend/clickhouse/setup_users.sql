-- These scripts are run manually against our ClickHouse Cloud database instances.

-- This user is used by Preset.
CREATE USER IF NOT EXISTS ext_superset IDENTIFIED BY '<replace_with_password>';

-- Run the outputs of this query to grant SELECT permissions on databases.
SELECT
    'GRANT SELECT ON ' || name || '.* TO ext_superset;'
FROM system.databases
WHERE name NOT IN ('system');

GRANT CREATE TEMPORARY TABLE, POSTGRES ON *.* TO ext_superset;

ALTER USER ext_superset SETTINGS
    max_threads=16,
    async_insert=1,
    async_insert_max_data_size=2000000,
    deduplicate_blocks_in_dependent_materialized_views=1,
    wait_for_async_insert=0,
    parallel_view_processing=1,
    max_table_size_to_drop=1000000000;

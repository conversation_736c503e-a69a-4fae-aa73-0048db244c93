ALTER TABLE runs ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_history ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_tags ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_inputs_kv ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_outputs_kv ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_metadata_kv ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_trace_id ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_run_id_v2 ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE runs_token_counts ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;
ALTER TABLE feedbacks_rmt ADD INDEX IF NOT EXISTS sk_mm_start_time(start_time) TYPE minmax GRANULARITY 1;

ALTER TABLE runs_lite ADD INDEX IF NOT EXISTS metadata_key_bloom mapKeys(metadata_kv) TYPE bloom_filter(0.01) GRANULARITY 1;
ALTER TABLE runs_lite ADD INDEX IF NOT EXISTS tag_bloom tags TYPE bloom_filter(0.01) GRANULARITY 1;
ALTER TABLE runs_lite ADD INDEX IF NOT EXISTS input_key_bloom mapKeys(inputs_kv) TYPE bloom_filter(0.01) GRANULARITY 1;
ALTER TABLE runs_lite ADD INDEX IF NOT EXISTS output_key_bloom mapKeys(outputs_kv) TYPE bloom_filter(0.01) GRANULARITY 1;

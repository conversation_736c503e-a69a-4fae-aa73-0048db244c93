CREATE TABLE IF NOT EXISTS root_runs_lite {{ON_CLUSTER}}
(
  id UUID,
  tenant_id UUID,
  name String,
  start_time DateTime64(6, 'UTC') COMMENT 'Start time of the run with microsecond precision',
  end_time Nullable(DateTime64(6, 'UTC')) COMMENT 'End time of the run with microsecond precision',
  is_root Bool,
  run_type LowCardinality(String),
  session_id UUID,
  parent_run_id Nullable(UUID),
  reference_example_id Nullable(UUID),
  reference_dataset_id Nullable(UUID),
  manifest_id Nullable(UUID),
  status LowCardinality(String),
  trace_id UUID,
  dotted_order String,
  prompt_tokens UInt32 DEFAULT 0,
  completion_tokens UInt32 DEFAULT 0,
  total_tokens UInt32 DEFAULT 0,
  first_token_time Nullable(DateTime64(6, 'UTC')) COMMENT 'Time of first token with microsecond precision',
  modified_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC') COMMENT 'Time of last modification with microsecond precision',
  is_deleted UInt8 COMMENT 'Whether the run is deleted',
  received_at Nullable(DateTime64(6, 'UTC')) COMMENT 'Time of receiving the run with microsecond precision',
  tenant_tier UInt8 DEFAULT 0 COMMENT 'Tier of the tenant',
  inserted_at DateTime64(6, 'UTC') DEFAULT now64(6, 'UTC'),
  prompt_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of prompt tokens',
  completion_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of completion tokens',
  total_cost Nullable(Decimal(18, 12)) DEFAULT NULL COMMENT 'USD cost of all tokens',
  trace_tier Nullable(String) DEFAULT NULL COMMENT 'Tier of the trace',
  trace_ttl_seconds Nullable(UInt64) DEFAULT NULL COMMENT 'Time to live in seconds',
  trace_first_received_at Nullable(DateTime64(6, 'UTC')) DEFAULT NULL COMMENT 'Time when the parent run was first received',
  trace_upgrade Bool DEFAULT false COMMENT 'If trace was upgraded',
  thread_id Nullable(String) DEFAULT NULL COMMENT 'thread_id for the run',
  tags Array(String) DEFAULT [] COMMENT 'Tags for the run',
  metadata_kv Map(String, String) DEFAULT map() COMMENT 'Metadata key-value pairs',
  inputs_kv Map(String, String) DEFAULT map() COMMENT 'Inputs key-value pairs',
  outputs_kv Map(String, String) DEFAULT map() COMMENT 'Outputs key-value pairs',
  expiration_time DateTime COMMENT 'Time when the run expires',
  INDEX idx_cursor_minmax concat(toString(start_time), toString(id)) TYPE minmax GRANULARITY 4,
  INDEX sk_mm_start_time start_time TYPE minmax GRANULARITY 1,
  INDEX sk_mm_end_time end_time TYPE minmax GRANULARITY 1,
  INDEX sk_mm_inserted_at inserted_at TYPE minmax GRANULARITY 1,
  INDEX sk_mm_expiration_time expiration_time TYPE minmax GRANULARITY 1,
  INDEX tenant_id_bloom tenant_id TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX session_id_bloom session_id TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX run_id_bloom id TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX thread_id_bloom thread_id TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX metadata_key_bloom mapKeys(metadata_kv) TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX tag_bloom tags TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX input_key_bloom mapKeys(inputs_kv) TYPE bloom_filter(0.01) GRANULARITY 1,
  INDEX output_key_bloom mapKeys(outputs_kv) TYPE bloom_filter(0.01) GRANULARITY 1
)
ENGINE = {{REPLACING_ENGINE(modified_at, is_deleted)}}
ORDER BY (tenant_id, session_id, start_time, id)
TTL expiration_time
SETTINGS
  allow_nullable_key = 1,
  index_granularity = 8192,
  ttl_only_drop_parts=1;


CREATE MATERIALIZED VIEW IF NOT EXISTS root_runs_lite_mv {{ON_CLUSTER}} TO root_runs_lite AS
SELECT
  id,
  tenant_id,
  name,
  start_time,
  end_time,
  is_root,
  run_type,
  session_id,
  parent_run_id,
  reference_example_id,
  reference_dataset_id,
  manifest_id,
  status,
  trace_id,
  dotted_order,
  prompt_tokens,
  completion_tokens,
  total_tokens,
  first_token_time,
  modified_at,
  is_deleted,
  received_at,
  tenant_tier,
  inserted_at,
  prompt_cost,
  completion_cost,
  total_cost,
  trace_tier,
  ttl_seconds as trace_ttl_seconds,
  trace_first_received_at,
  trace_upgrade,
  thread_id,
  tags,
  CAST(
        (
            arrayMap(x -> x.1, JSONExtractKeysAndValuesRaw(CAST(JSONExtractRaw(extra, 'metadata') AS String))),
            arrayMap(x -> x.2, JSONExtractKeysAndValuesRaw(CAST(JSONExtractRaw(extra, 'metadata') AS String)))
        ),
        'Map(String, String)'
    ) AS metadata_kv,
  inputs_kv,
  outputs_kv,
  IF(
    trace_first_received_at IS NOT NULL AND ttl_seconds IS NOT NULL,
    toDateTime(trace_first_received_at + toIntervalSecond(ttl_seconds)),
    toDateTime('2106-02-07 06:28:15')
  ) AS expiration_time
FROM runs WHERE is_root = 1;

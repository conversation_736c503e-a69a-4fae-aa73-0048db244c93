ALTER TABLE runs DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_history DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_tags DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_inputs_kv DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_outputs_kv DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_metadata_kv DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_trace_id DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_run_id_v2 DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE runs_token_counts DROP INDEX IF EXISTS sk_mm_start_time;
ALTER TABLE feedbacks_rmt DROP INDEX IF EXISTS sk_mm_start_time;

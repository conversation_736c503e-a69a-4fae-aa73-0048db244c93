import csv
import json
import os
import sys
import uuid
from collections import defaultdict
from datetime import datetime, timedelta
from typing import Any, Dict

from dateutil import parser


def parse_timestamp(timestamp_str: str) -> datetime:
    """Parse timestamp string to datetime object."""
    # Handle various timestamp formats:
    # - "2025-05-19 19:00:04.944218" (no timezone)
    # - "2025-04-14 21:28:02.602481+00" (PostgreSQL style with +00)
    try:
        # Clean up the string
        timestamp_str = timestamp_str.strip()

        # If it has +00 timezone notation, convert to +00:00
        if "+00" in timestamp_str and not timestamp_str.endswith("+00:00"):
            timestamp_str = timestamp_str.replace("+00", "+00:00")

        # Parse the timestamp
        return parser.parse(timestamp_str)
    except Exception as e:
        print(f"Failed to parse timestamp: '{timestamp_str}'")
        print(f"Error: {str(e)}")
        raise e


def extract_lower_bound(time_range: str) -> str:
    """Extract the lower bound from insertion_time_range.
    Handles format like '["2025-04-14 21:28:02.602481+00","2025-04-14 21:31:01.863572+00"]'"""
    try:
        # Remove outer brackets and quotes
        cleaned = time_range.strip("[](){}")

        # Handle JSON-style array format
        if '"' in cleaned:
            # Parse as JSON array
            import json

            try:
                # Add brackets back for valid JSON
                time_array = json.loads("[" + cleaned + "]")
                if len(time_array) >= 1:
                    return time_array[0]
            except Exception as e:
                print(f"Failed to parse JSON: {str(e)}")
                # Fallback to string parsing
                parts = cleaned.split('","')
                if len(parts) >= 1:
                    return parts[0].strip('"')
        else:
            # Handle simple comma-separated format
            parts = cleaned.split(",")
            if len(parts) >= 1:
                return parts[0].strip()

        return time_range
    except Exception as e:
        print(f"Warning: Could not parse time range: {time_range}")
        print(f"Error: {str(e)}")
        return time_range


def create_transaction(
    row: Dict[str, Any], timestamp: str, count: int
) -> Dict[str, Any]:
    """Create a transaction object from a CSV row."""
    return {
        "transaction_id": str(uuid.uuid4()),
        "customer_id": row["sc.id"],
        "timestamp": timestamp,
        "event_type": "langsmith_traces",
        "properties": {
            "count": str(count),
            "project_id": row["tc.session_id"],
            "tenant_id": row["tc.tenant_id"],
            "organization_id": row["tc.organization_id"],
        },
    }


def sanitize_filename(name: str) -> str:
    """Sanitize customer name for use as filename."""
    # Replace problematic characters
    replacements = {
        "/": "_",
        "\\": "_",
        ":": "_",
        " ": "_",
        ".": "_",
        "<": "_",
        ">": "_",
        "|": "_",
        "?": "_",
        "*": "_",
        '"': "_",
        "'": "_",
        "\n": "_",
        "\r": "_",
        "\t": "_",
    }

    for char, replacement in replacements.items():
        name = name.replace(char, replacement)

    # Remove any multiple underscores
    while "__" in name:
        name = name.replace("__", "_")

    # Trim underscores from start/end
    name = name.strip("_")

    # If name is empty after sanitization, use a default
    if not name:
        name = "unknown_customer"

    return name


def process_csv_to_jsonl(input_file: str, output_dir: str):
    """Process CSV file and convert to JSONL format, one file per customer."""

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Fixed date for negative transactions
    MARCH_25_2025 = datetime(2025, 3, 25)

    # Group transactions by customer name
    customer_transactions = defaultdict(list)

    # Read CSV file
    with open(input_file, "r", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)

        for row in reader:
            # Extract timestamps
            created_at = parse_timestamp(row["tc.created_at"])
            insertion_time_lower = extract_lower_bound(row["tc.insertion_time_range"])
            insertion_timestamp = parse_timestamp(insertion_time_lower)

            # Calculate negative transaction timestamp
            # max(created_date - 30 days, march 25, 2025)
            created_minus_30 = created_at - timedelta(days=30)
            negative_timestamp = max(created_minus_30, MARCH_25_2025)

            # Get trace count
            trace_count = int(row["tc.trace_count"])

            # Get customer name
            customer_name = row["sc.customer_name"]

            # Create negative transaction
            negative_txn = create_transaction(
                row, negative_timestamp.isoformat(), -trace_count
            )
            customer_transactions[customer_name].append(negative_txn)

            # Create positive transaction
            positive_txn = create_transaction(
                row, insertion_timestamp.isoformat(), trace_count
            )
            customer_transactions[customer_name].append(positive_txn)

    # Write separate JSONL file for each customer
    total_transactions = 0
    file_count = 0

    for customer_name, transactions in customer_transactions.items():
        # Create safe filename
        safe_customer_name = sanitize_filename(customer_name)
        output_file = os.path.join(output_dir, f"{safe_customer_name}.jsonl")

        # Handle duplicate filenames by appending number
        if os.path.exists(output_file):
            counter = 1
            while os.path.exists(
                os.path.join(output_dir, f"{safe_customer_name}_{counter}.jsonl")
            ):
                counter += 1
            output_file = os.path.join(
                output_dir, f"{safe_customer_name}_{counter}.jsonl"
            )

        # Write transactions to file
        with open(output_file, "w", encoding="utf-8") as jsonlfile:
            for transaction in transactions:
                json.dump(transaction, jsonlfile)
                jsonlfile.write("\n")

        total_transactions += len(transactions)
        file_count += 1
        print(
            f"Created {output_file} with {len(transactions)} transactions for '{customer_name}'"
        )

    print(f"\nSuccessfully converted {total_transactions} total transactions")
    print(f"Created {file_count} customer files in {output_dir}")
    print(f"Average transactions per customer: {total_transactions / file_count:.1f}")


def main():
    if len(sys.argv) != 3:
        print("Usage: python csv_to_jsonl.py <input_csv> <output_directory>")
        print("Example: python csv_to_jsonl.py data.csv customer_transactions/")
        sys.exit(1)

    input_file = sys.argv[1]
    output_dir = sys.argv[2]

    try:
        process_csv_to_jsonl(input_file, output_dir)
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error processing file: {str(e)}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()

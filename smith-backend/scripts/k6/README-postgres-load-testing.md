# PostgreSQL Load Testing Integration

This document describes the integrated PostgreSQL load testing capabilities for LangSmith, designed to test database performance at scale (targeting 100B traces, 20x current volume).

## Overview

The load testing framework now includes three types of database performance tests:

1. **Large Batch Size Test** - Tests database write performance via `/runs/batch` endpoint
2. **Bulk Export Test** - Tests database read performance for large result sets  
3. **Direct PostgreSQL Test** - Tests database performance directly (bypassing API layer)

## Critical: asyncpg + PGBouncer Issue

### The Problem
When using PGBouncer with asyncpg (Python PostgreSQL library), there's a known issue with prepared statements that causes random failures under high load:

**Error**: `prepared statement "__asyncpg_stmt_1__" already exists`

**Root Cause**: [GitHub Issue #1058](https://github.com/MagicStack/asyncpg/issues/1058)
- asyncpg creates prepared statements on Connection A
- PGBouncer switches you to Connection B  
- asyncpg tries to reuse prepared statement, but it only exists on Connection A
- Results in random connection errors under high concurrent load

### The Solution
Configure asyncpg with unique prepared statement names:

```python
# For SQLAlchemy 2.0+
connect_args = {
    "prepared_statement_name_func": lambda: f"__asyncpg_{uuid.uuid4()}__",
    "statement_cache_size": 0,
    "prepared_statement_cache_size": 0,
}

# For SQLAlchemy 1.4
from asyncpg import Connection
import uuid

class UniquePreparedStatementConnection(Connection):
    def _get_unique_id(self, prefix: str) -> str:
        return f"__asyncpg_{prefix}_{uuid.uuid4()}__"

connect_args = {
    "connection_class": UniquePreparedStatementConnection,
    "statement_cache_size": 0,
    "prepared_statement_cache_size": 0,
}
```

## Test Types

### 1. Large Batch Size Test (`large-batch-size`)

**Purpose**: Tests PostgreSQL write performance under high insert/update load.

**What it does**:
- Creates parent runs with large payloads (configurable size)
- Creates multiple child runs per parent (configurable batch size)
- Sends via `/runs/batch` endpoint
- Stresses database write operations and connection pooling

**Usage**:
```bash
# Via GitHub Actions workflow
Test Type: large-batch-size
Batch Size: 99 (child runs per parent)
Payload Size: 25000 (bytes)
Scale Level: baseline/4x/20x
```

**Database Impact**:
- High INSERT operations
- Tests connection pool limits
- Stresses asyncpg prepared statement handling
- Tests PGBouncer transaction mode performance

### 2. Bulk Export Test (`bulk-export`)

**Purpose**: Tests PostgreSQL read performance for large result sets.

**What it does**:
- Queries large datasets via `/runs/query`, `/datasets`, `/feedback` endpoints
- Configurable batch sizes (1000+ records)
- Tests database read scalability
- Simulates analytics/export workloads

**Usage**:
```bash
# Via GitHub Actions workflow  
Test Type: bulk-export
Batch Size: 1000 (records to fetch)
Scale Level: baseline/4x/20x
```

**Database Impact**:
- Large SELECT operations
- Tests query performance under load
- Stresses memory usage for large result sets
- Tests connection pool efficiency for read operations

### 3. Direct PostgreSQL Test (`postgres-direct`)

**Purpose**: Tests PostgreSQL performance directly, bypassing the API layer.

**What it does**:
- Direct SQL queries against PostgreSQL
- Multiple test patterns: read_heavy, write_heavy, mixed, connection_pool
- Tests database limits without application overhead
- Uses K6 SQL extension

**Usage**:
```bash
# Via GitHub Actions workflow
Test Type: postgres-direct
PostgreSQL Host: your-db-host
PostgreSQL DB: langsmith_test
PostgreSQL Test Type: mixed/read_heavy/write_heavy/connection_pool
Scale Level: baseline/4x/20x
```

**Database Impact**:
- Direct connection pool testing
- Raw database performance measurement
- Connection establishment latency
- Query execution performance

## Scale Configurations

| Scale Level | Description | Target Load |
|------------|-------------|-------------|
| `baseline` | Current production baseline | 5B traces |
| `4x` | 4x scale testing | 20B traces |
| `20x` | Target scale testing | 100B traces |

### VU (Virtual Users) & RPS Targets

| Test Type | Baseline | 4x Scale | 20x Scale |
|-----------|----------|----------|-----------|
| large-batch-size | 50 VUs | 200 VUs | 1000 VUs |
| bulk-export | 5 VUs, 10 RPS | 20 VUs, 40 RPS | 100 VUs, 200 RPS |
| postgres-direct | 10 VUs, 100 QPS | 40 VUs, 400 QPS | 200 VUs, 2000 QPS |

## Running Tests

### Via GitHub Actions (Recommended)

1. Go to Actions tab in the repository
2. Select "K6 Load Test" workflow
3. Click "Run workflow"
4. Configure test parameters:
   - **Test Type**: Select database test type
   - **Scale Level**: Choose scale level  
   - **Database Config**: Set PostgreSQL connection details
   - **Duration**: Configure test duration

### Example Configurations

#### Testing Database Write Performance
```yaml
Test Type: large-batch-size
Scale Level: 4x
Batch Size: 99
Payload Size: 50000
Duration Config: 5m,10m,2m
```

#### Testing Database Read Performance  
```yaml
Test Type: bulk-export
Scale Level: 20x
Batch Size: 5000
Duration Config: 2m,15m,1m
```

#### Testing Direct Database Performance
```yaml
Test Type: postgres-direct
PostgreSQL Host: your-clone-db-host
PostgreSQL Test Type: mixed
Scale Level: 20x
Duration Config: 3m,10m,2m
```

## Database Clone for Testing

**Important**: Always use a database clone for load testing to avoid impacting production.

Google Cloud SQL Clone: `langchainplus-db-clone`
- Console: https://console.cloud.google.com/sql/instances/langchainplus-db-clone/overview?project=langchain-staging
- Use this for safe, isolated load testing

## Monitoring & Results

### Key Metrics

**Large Batch Size Test**:
- Request latency (p50, p95, p99)
- Error rates
- Database connection errors
- Insert throughput

**Bulk Export Test**:
- Export latency (slower thresholds for bulk ops)
- Response sizes (MB transferred)
- Large response count (>1MB responses)
- Database read throughput

**Direct PostgreSQL Test**:
- Query latency (p50, p95, p99)
- Connection latency
- Slow queries (>1s)
- Connection pool errors

### Performance Thresholds

| Metric | Baseline Target | 20x Scale Target |
|--------|----------------|------------------|
| API Response Time (p95) | <2s | <5s |
| Database Query Time (p95) | <500ms | <1s |
| Connection Time (p95) | <200ms | <500ms |
| Error Rate | <0.5% | <1% |

## Troubleshooting

### Common Issues

1. **Prepared Statement Errors**
   - Symptom: `prepared statement already exists`
   - Solution: Configure unique prepared statement names (see above)

2. **Connection Pool Exhaustion**
   - Symptom: Connection timeout errors
   - Solution: Increase max_connections, tune PGBouncer pool size

3. **High Memory Usage**
   - Symptom: OOM errors during bulk exports
   - Solution: Reduce batch sizes, add pagination

4. **Slow Bulk Operations**
   - Symptom: High p99 latencies for exports
   - Solution: Add database indexes, optimize queries

### Performance Investigation

1. **Check Database Metrics**:
   - Connection count
   - Active queries
   - Lock waits
   - Memory usage

2. **Monitor PGBouncer**:
   - Pool utilization
   - Wait times
   - Connection churning

3. **Application Metrics**:
   - asyncpg connection pool stats
   - SQLAlchemy query performance
   - Memory usage patterns

## Future Enhancements

1. **Add More Endpoints**: Test other high-traffic APIs
2. **Database Profiling**: Integrate with PostgreSQL query profiling
3. **Connection Pool Testing**: Dedicated PGBouncer stress tests
4. **Memory Profiling**: Track memory usage during bulk operations
5. **Replication Testing**: Test read replica performance

## References

- [asyncpg Issue #1058](https://github.com/MagicStack/asyncpg/issues/1058) - Prepared statement conflicts
- [GCP Managed Connection Pooling](https://cloud.google.com/sql/docs/postgres/managed-connection-pooling) - Server-side pooling
- [Load Testing Notion](https://www.notion.so/Load-Testing-f2c36b983224481aa51c52636c862664) - Past performance tests 
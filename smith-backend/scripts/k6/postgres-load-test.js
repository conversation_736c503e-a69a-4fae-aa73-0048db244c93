import sql from 'k6/x/sql';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';

// Custom metrics
const queryLatency = new Trend('query_latency');
const connectionLatency = new Trend('connection_latency');
const successfulQueries = new Counter('successful_queries');
const failedQueries = new Counter('failed_queries');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const connectionErrors = new Counter('connection_errors');
const slowQueries = new Counter('slow_queries');

// Test configuration from environment variables
const DB_HOST = __ENV.POSTGRES_HOST || 'localhost';
const DB_PORT = __ENV.POSTGRES_PORT || '5432';
const DB_NAME = __ENV.POSTGRES_DB || 'langsmith_test';
const DB_USER = __ENV.POSTGRES_USER || 'postgres';
const DB_PASSWORD = __ENV.POSTGRES_PASSWORD || 'postgres';
const DB_SCHEMA = __ENV.POSTGRES_SCHEMA || 'public';
const TEST_TYPE = __ENV.POSTGRES_TEST_TYPE || 'mixed'; // mixed, read_heavy, write_heavy, connection_pool
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// Parse scale configuration from environment (format: "vus,rps")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // If SCALE_LEVEL contains comma-separated values (new format)
  if (scaleLevel && scaleLevel.includes(',')) {
    const [vus, rps] = scaleLevel.split(',').map(val => parseInt(val.trim()));
    return { vus, queries_per_second: rps }; // PostgreSQL uses queries_per_second
  }

  // Fallback to legacy scale configurations
  const SCALE_CONFIGS = {
    baseline: {
      vus: 10,     // Baseline concurrent connections
      queries_per_second: 100
    },
    '4x': {
      vus: 40,     // 4x scale for 20B traces
      queries_per_second: 400
    },
    '20x': {
      vus: 200,    // 20x scale for 100B traces
      queries_per_second: 2000
    }
  };

  const scaleLevelKey = scaleLevel || 'baseline';
  const config = SCALE_CONFIGS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(SCALE_CONFIGS).join(', ')} or "vus,qps" format`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

// Generate unique table name to avoid conflicts
const TEST_RUN_ID = Math.random().toString(36).substring(7);
const TEMP_TABLE_NAME = `temp_load_test_${TEST_RUN_ID}`;

// Test queries based on LangSmith schema patterns
const TEST_QUERIES = {
  // Simulated read queries similar to LangSmith patterns
  read_queries: [
    `SELECT id, name, created_at FROM ${DB_SCHEMA}.projects LIMIT 10`,
    `SELECT id, name, description FROM ${DB_SCHEMA}.datasets LIMIT 20`,
    `SELECT count(*) FROM ${DB_SCHEMA}.runs WHERE created_at > NOW() - INTERVAL '1 hour'`,
    `SELECT id, start_time, end_time FROM ${DB_SCHEMA}.runs ORDER BY start_time DESC LIMIT 50`,
    `SELECT DISTINCT project_id FROM ${DB_SCHEMA}.runs WHERE created_at > NOW() - INTERVAL '1 day'`
  ],

  // Simulated write queries (safe operations)
  write_queries: [
    `INSERT INTO ${DB_SCHEMA}.${TEMP_TABLE_NAME} (test_data, created_at) VALUES ($1, NOW())`,
    `UPDATE ${DB_SCHEMA}.${TEMP_TABLE_NAME} SET updated_at = NOW() WHERE created_at < NOW() - INTERVAL '1 minute'`,
    `DELETE FROM ${DB_SCHEMA}.${TEMP_TABLE_NAME} WHERE created_at < NOW() - INTERVAL '5 minutes'`
  ],

  // Connection pool stress queries
  connection_queries: [
    'SELECT 1',
    'SELECT pg_backend_pid()',
    'SELECT current_database()',
    'SELECT version()'
  ]
};

export const options = {
  scenarios: {
    postgres_load_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.queries_per_second },
        { duration: STEADY_DURATION, target: CONFIG.queries_per_second },
        { duration: RAMP_DOWN_DURATION, target: 0 }
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * 2, // Allow K6 to scale up if needed
    },
  },
  thresholds: {
    'query_latency': [
      { threshold: 'p(50)<100', abortOnFail: false },   // 50th percentile < 100ms
      { threshold: 'p(95)<500', abortOnFail: false },   // 95th percentile < 500ms
      { threshold: 'p(99)<1000', abortOnFail: false }   // 99th percentile < 1000ms
    ],
    'connection_latency': [
      { threshold: 'p(95)<200', abortOnFail: false }    // Connection time < 200ms
    ],
    'error_rate': [
      { threshold: 'rate<0.01', abortOnFail: false }    // 1% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ],
    'slow_queries': [
      { threshold: 'count<100', abortOnFail: false }    // Queries > 1s
    ]
  },
};

// Database connection string
const CONNECTION_STRING = `postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=disable`;

// Setup function to create temp table if needed
export function setup() {
  const db = sql.open('postgres', CONNECTION_STRING);
  
  try {
    // Create temp table for write operations
    db.exec(`
      CREATE TABLE IF NOT EXISTS ${DB_SCHEMA}.${TEMP_TABLE_NAME} (
        id SERIAL PRIMARY KEY,
        test_data TEXT,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP
      )
    `);
    console.log('Setup complete: temp table created');
  } catch (error) {
    console.error('Setup error:', error);
  } finally {
    db.close();
  }
}

// Cleanup function with retry logic
export function teardown() {
  const maxRetries = 3;
  const retryDelay = 1000; // 1 second
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const db = sql.open('postgres', CONNECTION_STRING);

    try {
      // Try to drop the temp table
      db.exec(`DROP TABLE IF EXISTS ${DB_SCHEMA}.${TEMP_TABLE_NAME}`);
      console.log(`Teardown successful on attempt ${attempt}: temp table dropped`);
      return; // Success - exit the function
    } catch (error) {
      console.warn(`Teardown attempt ${attempt}/${maxRetries} failed:`, error.message);

      if (attempt === maxRetries) {
        // Final attempt failed - log detailed error but don't crash
        console.error('🚨 TEARDOWN FAILED - Manual cleanup may be required!');
        console.error(`💡 Run manually: DROP TABLE IF EXISTS ${DB_SCHEMA}.${TEMP_TABLE_NAME};`);
        console.error('🔍 Check for orphaned temp tables:', error);

        // Try to list any remaining temp tables for debugging
        try {
          const result = db.exec(`
            SELECT tablename 
            FROM pg_tables 
            WHERE schemaname = $1 AND tablename LIKE '${TEMP_TABLE_NAME}%'
          `, DB_SCHEMA);
          if (result && result.length > 0) {
            console.error('🗂️  Found orphaned temp tables:', result);
          }
        } catch (listError) {
          console.error('Could not list temp tables:', listError.message);
        }
      } else {
        // Wait before retry
        sleep(retryDelay / 1000);
      }
    } finally {
      db.close();
    }
  }
}

export default function () {
  const connectionStart = new Date().getTime();
  
  let db;
  try {
    db = sql.open('postgres', CONNECTION_STRING);
    const connectionTime = new Date().getTime() - connectionStart;
    connectionLatency.add(connectionTime);
  } catch (error) {
    connectionErrors.add(1);
    errorRate.add(1);
    console.error('Connection error:', error);
    return;
  }

  try {
    const queryStart = new Date().getTime();
    let query, params = [];
    
    // Select query type based on test configuration
    switch (TEST_TYPE) {
      case 'read_heavy':
        query = TEST_QUERIES.read_queries[Math.floor(Math.random() * TEST_QUERIES.read_queries.length)];
        break;
      case 'write_heavy':
        const writeQuery = TEST_QUERIES.write_queries[Math.floor(Math.random() * TEST_QUERIES.write_queries.length)];
        if (writeQuery.includes('$1')) {
          query = writeQuery;
          params = [`test_data_${Math.random().toString(36).substring(7)}`];
        } else {
          query = writeQuery;
        }
        break;
      case 'connection_pool':
        query = TEST_QUERIES.connection_queries[Math.floor(Math.random() * TEST_QUERIES.connection_queries.length)];
        break;
      default: // mixed
        const allQueries = [...TEST_QUERIES.read_queries, ...TEST_QUERIES.connection_queries];
        query = allQueries[Math.floor(Math.random() * allQueries.length)];
    }

    // Execute query
    let result;
    if (params.length > 0) {
      result = db.exec(query, ...params);
    } else {
      result = db.exec(query);
    }
    
    const queryTime = new Date().getTime() - queryStart;
    queryLatency.add(queryTime);
    
    // Track slow queries
    if (queryTime > 1000) {
      slowQueries.add(1);
    }
    
    const success = check(result, {
      'query executed successfully': (r) => r !== null,
    });
    
    if (success) {
      successfulQueries.add(1);
    } else {
      failedQueries.add(1);
      errorRate.add(1);
    }
    
  } catch (error) {
    if (error.message && error.message.includes('timeout')) {
      timeoutErrors.add(1);
    }
    failedQueries.add(1);
    errorRate.add(1);
    console.error('Query error:', error);
  } finally {
    db.close();
  }
}

// Log test configuration on startup
console.log('Postgres Load Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetQPS: CONFIG.queries_per_second,
  testType: TEST_TYPE,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  database: `${DB_HOST}:${DB_PORT}/${DB_NAME}`,
  schema: DB_SCHEMA
}); 
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

/*
 * Commits Endpoint Load Test for LangSmith
 *
 * This script tests database write performance via the /commits endpoint.
 *
 * Configuration Options (via environment variables):
 * - RPS_PER_VU_RATIO: How many RPS per VU (default: 20). Lower = more concurrent connections.
 * - BASELINE_RPS/VUS: Override baseline scale values (defaults: 10 RPS, 2 VUs)
 * - SCALE_4X_RPS/VUS: Override 4x scale values (defaults: 40 RPS, 8 VUs)
 * - SCALE_20X_RPS/VUS: Override 20x scale values (defaults: 200 RPS, 40 VUs)
 * - VU_SCALE_MULTIPLIER: Max VU scaling factor (default: 2)
 *
 * Examples:
 *   RPS_PER_VU_RATIO=10 k6 run commits-endpoint-test.js   # More aggressive VU scaling
 *   BASELINE_RPS=20 k6 run commits-endpoint-test.js       # Higher baseline load
 */

// Custom metrics
const commitLatency = new Trend('commit_latency');
const successfulCommits = new Counter('successful_commits');
const failedCommits = new Counter('failed_commits');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const COMMIT_OWNER = __ENV.COMMIT_OWNER || 'langchain-ai';
const COMMIT_SHA = __ENV.COMMIT_SHA || 'test-commit';
const DATA_SIZE = parseInt(__ENV.DATA_SIZE || '25000');
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// ===== CONFIGURATION SECTION =====
// These values can be tweaked to adjust load testing behavior
const LOAD_TEST_CONFIG = {
  // VU calculation: How many RPS per VU for commit operations
  // Higher ratio = fewer VUs for given RPS (commit operations are write-heavy)
  RPS_PER_VU_RATIO: parseInt(__ENV.RPS_PER_VU_RATIO || '20'),

  // Default scale levels - can be overridden via environment
  SCALE_DEFAULTS: {
    baseline: {
      vus: parseInt(__ENV.BASELINE_VUS || '2'),      // Few VUs for write operations
      rps: parseInt(__ENV.BASELINE_RPS || '10')      // Conservative baseline for commits
    },
    '4x': {
      vus: parseInt(__ENV.SCALE_4X_VUS || '8'),      // 4x scale
      rps: parseInt(__ENV.SCALE_4X_RPS || '40')      // 4x RPS
    },
    '20x': {
      vus: parseInt(__ENV.SCALE_20X_VUS || '40'),    // 20x scale
      rps: parseInt(__ENV.SCALE_20X_RPS || '200')    // 20x RPS
    }
  },

  // VU scaling multiplier - how much to allow K6 to scale up if needed
  VU_SCALE_MULTIPLIER: parseInt(__ENV.VU_SCALE_MULTIPLIER || '2')
};
// ===== END CONFIGURATION SECTION =====

// Parse scale configuration from environment (format: "rps" or "vus,rps" or "baseline,rps_ratio=20,vu_mult=3")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // Parse extended format with key=value parameters
  if (scaleLevel && scaleLevel.includes(',')) {
    const parts = scaleLevel.split(',');
    const basePart = parts[0];

    // Initialize with default config values
    let currentConfig = { ...LOAD_TEST_CONFIG };

    // Parse key=value parameters
    parts.slice(1).forEach(part => {
      if (part.includes('=')) {
        const [key, value] = part.split('=');
        const numValue = parseInt(value);

        switch (key.trim()) {
          case 'rps_ratio':
            currentConfig.RPS_PER_VU_RATIO = numValue;
            break;
          case 'vu_mult':
            currentConfig.VU_SCALE_MULTIPLIER = numValue;
            break;
          default:
            console.warn(`Unknown parameter: ${key}=${value}`);
        }
      }
    });

    // Check if basePart is VUs,RPS format (legacy)
    if (!isNaN(parseInt(basePart))) {
      // Could be "50,200,rps_ratio=3" or just "50,200"
      const secondPart = parts[1];
      if (secondPart && !secondPart.includes('=') && !isNaN(parseInt(secondPart))) {
        // Legacy VUs,RPS format: "50,200,rps_ratio=3"
        const vus = parseInt(basePart);
        const rps = parseInt(secondPart);
        return { vus, rps };
      } else {
        // RPS with parameters: "100,rps_ratio=3"
        const rps = parseInt(basePart);
        const vus = Math.max(1, Math.ceil(rps / currentConfig.RPS_PER_VU_RATIO));
        return { vus, rps };
      }
    }

    // Named scale level with parameters: "baseline,rps_ratio=20,vu_mult=3"
    const config = currentConfig.SCALE_DEFAULTS[basePart];
    if (config) {
      return {
        vus: config.vus,
        rps: config.rps,
        vuMultiplier: currentConfig.VU_SCALE_MULTIPLIER
      };
    }
  }

  // If SCALE_LEVEL is just a number (RPS only)
  if (scaleLevel && !isNaN(parseInt(scaleLevel))) {
    const rps = parseInt(scaleLevel);
    const vus = Math.max(1, Math.ceil(rps / LOAD_TEST_CONFIG.RPS_PER_VU_RATIO));
    return { vus, rps };
  }

  // Fallback to configurable scale configurations
  const scaleLevelKey = scaleLevel || 'baseline';
  const config = LOAD_TEST_CONFIG.SCALE_DEFAULTS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(LOAD_TEST_CONFIG.SCALE_DEFAULTS).join(', ')}, "rps" format (e.g. "50"), "vus,rps" format, or extended format (e.g. "baseline,rps_ratio=20,vu_mult=3")`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    commits_endpoint_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.rps },
        { duration: STEADY_DURATION, target: CONFIG.rps },
        { duration: RAMP_DOWN_DURATION, target: 0 }
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * (CONFIG.vuMultiplier || LOAD_TEST_CONFIG.VU_SCALE_MULTIPLIER), // Use dynamic or default multiplier
    },
  },
  thresholds: {
    'commit_latency': [
      { threshold: 'p(50)<1000', abortOnFail: false },  // 50th percentile < 1000ms
      { threshold: 'p(95)<2000', abortOnFail: false },  // 95th percentile < 2000ms
      { threshold: 'p(99)<5000', abortOnFail: false }   // 99th percentile < 5000ms
    ],
    'error_rate': [
      { threshold: 'rate<0.01', abortOnFail: false }    // 1% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<25', abortOnFail: false }
    ]
  },
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for commits endpoint: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for commits endpoint: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for commits endpoint: ${response.request.url}`);
  }
}

// Generate commit data
function generateCommitData() {
  const commitId = uuidv4();
  const timestamp = new Date().toISOString();
  
  // Generate commit message with configurable payload size
  const baseMessage = `Load test commit ${commitId}`;
  const paddingSize = Math.max(0, DATA_SIZE - baseMessage.length - 100); // Leave room for other fields
  const padding = 'x'.repeat(paddingSize);
  
  return {
    sha: `${COMMIT_SHA}-${commitId}`,
    message: `${baseMessage}\n\n${padding}`,
    author: {
      name: 'K6 Load Test',
      email: '<EMAIL>',
      date: timestamp
    },
    committer: {
      name: 'K6 Load Test',
      email: '<EMAIL>', 
      date: timestamp
    },
    tree: `tree-${commitId}`,
    parents: [`parent-${uuidv4()}`],
    url: `https://github.com/${COMMIT_OWNER}/test-repo/commit/${commitId}`,
    html_url: `https://github.com/${COMMIT_OWNER}/test-repo/commit/${commitId}`,
    // Additional metadata that might be stored in PostgreSQL
    stats: {
      additions: Math.floor(Math.random() * 100),
      deletions: Math.floor(Math.random() * 50),
      total: Math.floor(Math.random() * 150)
    },
    files: [
      {
        filename: `test_file_${Math.floor(Math.random() * 1000)}.py`,
        status: 'modified',
        additions: Math.floor(Math.random() * 50),
        deletions: Math.floor(Math.random() * 25),
        changes: Math.floor(Math.random() * 75)
      }
    ]
  };
}

export default function () {
  const startTime = new Date().getTime();
  
  // Generate commit data
  const commitData = generateCommitData();
  
  const response = http.post(
    `${BASE_URL}/commits/${COMMIT_OWNER}/${commitData.sha}`,
    JSON.stringify(commitData),
    {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
      },
      timeout: '10s',
    }
  );

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  commitLatency.add(duration);
  
  const success = check(response, {
    'status is 200 or 201': (r) => r.status === 200 || r.status === 201,
    'response time < 10s': (r) => r.timings.duration < 10000,
    'has commit response': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.sha || body.id || body.message;
      } catch (e) {
        return false;
      }
    }
  });

  if (success) {
    successfulCommits.add(1);
    console.log(`Commit successful: ${commitData.sha}, ${duration}ms`);
  } else {
    failedCommits.add(1);
    errorRate.add(1);
    categorizeError(response);
    console.error(`Commit failed: ${response.status}, ${duration}ms, SHA: ${commitData.sha}`);
  }
}

// Log test configuration on startup
console.log('Commits Endpoint Load Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  commitOwner: COMMIT_OWNER,
  commitSha: COMMIT_SHA,
  dataSize: DATA_SIZE,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: `${BASE_URL}/commits/${COMMIT_OWNER}/*`
}); 
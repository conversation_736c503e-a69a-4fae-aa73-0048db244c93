import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';

// Custom metrics
const readLatency = new Trend('read_latency');
const successfulReads = new Counter('successful_reads');
const failedReads = new Counter('failed_reads');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');
const cacheHits = new Counter('cache_hits');
const runIdsFetched = new Counter('run_ids_fetched');

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;

// Parse TEST_CONFIG: format is "runsLimit,projectName,sessionId" (comma-separated)
const TEST_CONFIG = __ENV.TEST_CONFIG || '';
const configParts = TEST_CONFIG.split(',');

const RULES_LIMIT = parseInt(configParts[0] || __ENV.RULES_LIMIT || '1000'); // Number of runs to fetch from query endpoint
const PROJECT_ID = configParts[1] || __ENV.PROJECT_ID; // Optional project filter
const SESSION_ID = configParts[2] || __ENV.SESSION_ID_FILTER; // Optional session filter  
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// Parse scale configuration from environment (format: "rps" or "vus,rps")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // If SCALE_LEVEL contains comma-separated values (legacy format)
  if (scaleLevel && scaleLevel.includes(',')) {
    const [vus, rps] = scaleLevel.split(',').map(val => parseInt(val.trim()));
    return { vus, rps };
  }

  // If SCALE_LEVEL is just a number (new simplified format - just RPS)
  if (scaleLevel && !isNaN(parseInt(scaleLevel))) {
    const rps = parseInt(scaleLevel);
    const vus = Math.max(1, Math.ceil(rps / 20)); // Auto-calculate VUs (1 VU per 20 RPS for dynamic tests)
    return { vus, rps };
  }

  // Fallback to legacy scale configurations
  const SCALE_CONFIGS = {
    baseline: {
      vus: 10,     // Current baseline virtual users
      rps: 600     // Current baseline requests per second
    },
    '4x': {
      vus: 40,     // 4x the baseline VUs
      rps: 2800    // Target 2.8k RPS
    },
    '20x': {
      vus: 200,    // 20x the baseline VUs
      rps: 15000   // Target 15k RPS
    }
  };

  const scaleLevelKey = scaleLevel || 'baseline';
  const config = SCALE_CONFIGS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(SCALE_CONFIGS).join(', ')}, "rps" format (e.g. "100"), or "vus,rps" format`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    dynamic_run_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.rps },    // Ramp up to target RPS
        { duration: STEADY_DURATION, target: CONFIG.rps },     // Maintain target RPS
        { duration: RAMP_DOWN_DURATION, target: 0 }           // Ramp down to 0
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * 2, // Allow K6 to scale up if needed
    },
  },
  thresholds: {
    'read_latency': [
      { threshold: 'p(50)<1000', abortOnFail: false },  // 50th percentile < 1000ms
      { threshold: 'p(95)<2000', abortOnFail: false },  // 95th percentile < 2000ms
      { threshold: 'p(99)<5000', abortOnFail: false }   // 99th percentile < 5000ms
    ],
    'error_rate': [
      { threshold: 'rate<0.005', abortOnFail: false }  // 0.5% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<100', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ]
  },
};

// Global variable to store run IDs (shared across all VUs)
let availableRunIds = [];

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for request: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for request: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for request: ${response.request.url}`);
  }
}

// Setup function to fetch available run IDs
export function setup() {
  console.log('🔍 Fetching available run IDs from /runs/query endpoint...');
  
  // Build query payload using official LangSmith API structure
  const queryPayload = {
    limit: RULES_LIMIT,
    order_by: ['-start_time'], // Get most recent runs first
    select: ['id', 'trace_id', 'name', 'run_type'] // Only fetch needed fields for performance
  };

  // Add optional filters (following official docs patterns)
  if (PROJECT_ID) {
    queryPayload.project_name = PROJECT_ID; // Official parameter name
  }
  
  if (SESSION_ID) {
    queryPayload.session = [SESSION_ID]; // Must be array per docs
  }

  const response = http.post(
    `${BASE_URL}/runs/query`,
    JSON.stringify(queryPayload),
    {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
      },
      timeout: '30s', // Longer timeout for initial setup
    }
  );

  if (response.status !== 200) {
    console.error(`❌ Failed to fetch runs: ${response.status} - ${response.body}`);
    return { runIds: [] };
  }

  try {
    const queryData = JSON.parse(response.body);
    // Official API response structure: { "runs": [...], "cursors": {...} }
    const runIds = queryData.runs ? queryData.runs.map(run => run.id).filter(id => id) : [];
    
    console.log(`✅ Successfully fetched ${runIds.length} run IDs for dynamic testing`);
    console.log(`📊 Scale level: ${__ENV.SCALE_LEVEL} (${CONFIG.vus} VUs, ${CONFIG.rps} target RPS)`);
    
    if (runIds.length === 0) {
      console.warn('⚠️  No run IDs found! Test will fail.');
      console.warn('💡 Try providing a PROJECT_ID (project_name) or SESSION_ID filter to find runs');
      console.warn('📖 Refer to: https://docs.smith.langchain.com/observability/how_to_guides/export_traces');
    } else {
      console.log(`🎯 Sample run IDs: ${runIds.slice(0, 3).join(', ')}...`);
      if (queryData.cursors && queryData.cursors.next) {
        console.log('📄 More results available via pagination');
      }
    }
    
    return { runIds: runIds };
  } catch (error) {
    console.error(`❌ Error parsing runs query response: ${error}`);
    return { runIds: [] };
  }
}

export default function (data) {
  // Initialize run IDs from setup data
  if (data && data.runIds && data.runIds.length > 0) {
    availableRunIds = data.runIds;
  }

  if (availableRunIds.length === 0) {
    console.error('❌ No run IDs available for testing');
    failedReads.add(1);
    errorRate.add(1);
    return;
  }

  // Randomly select a run ID from the available list
  const randomIndex = Math.floor(Math.random() * availableRunIds.length);
  const selectedRunId = availableRunIds[randomIndex];

  const startTime = new Date().getTime();

  const response = http.get(
    `${BASE_URL}/runs/${selectedRunId}`,
    {
      headers: {
        'x-api-key': API_KEY,
      },
      timeout: '10s',
    }
  );

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  readLatency.add(duration);
  
  // Check for cache indicators in response headers
  if (response.headers['x-cache'] || response.headers['cf-cache-status']) {
    cacheHits.add(1);
  }

  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 5s': (r) => r.timings.duration < 5000,
    'has run data': (r) => {
      try {
        const body = JSON.parse(r.body);
        return body.id === selectedRunId; // Verify we got the correct run
      } catch (e) {
        return false;
      }
    }
  });

  if (success) {
    successfulReads.add(1);
    console.log(`✅ Read successful: ${selectedRunId}, ${duration}ms`);
  } else {
    failedReads.add(1);
    errorRate.add(1);
    categorizeError(response);
    console.error(`❌ Read failed: ${selectedRunId}, ${response.status}, ${duration}ms`);
  }
}

// Log test configuration on startup
console.log('Dynamic Run-by-ID Load Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  runsLimit: RULES_LIMIT,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: BASE_URL,
  projectId: PROJECT_ID || 'all projects',
  sessionId: SESSION_ID || 'all sessions',
  testConfig: TEST_CONFIG || 'none provided (using defaults)',
  configFormat: 'runsLimit,projectName,sessionId (comma-separated)'
}); 
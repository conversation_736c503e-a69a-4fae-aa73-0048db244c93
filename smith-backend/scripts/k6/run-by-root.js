import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';

// Custom metrics
const queryLatency = new Trend('query_latency');
const successfulQueries = new Counter('successful_queries');
const failedQueries = new Counter('failed_queries');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const START_TIME = __ENV.START_TIME || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(); // Default to 24 hours ago
const PROJECT_ID = __ENV.PROJECT_ID;  // Optional project filter
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// Parse scale configuration from environment (format: "vus,rps")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // If SCALE_LEVEL contains comma-separated values (new format)
  if (scaleLevel && scaleLevel.includes(',')) {
    const [vus, rps] = scaleLevel.split(',').map(val => parseInt(val.trim()));
    return { vus, rps };
  }

  // Fallback to legacy scale configurations
  const SCALE_CONFIGS = {
    baseline: {
      vus: 10,     // Current baseline virtual users
      rps: 600     // Current baseline requests per second
    },
    '4x': {
      vus: 40,     // 4x the baseline VUs
      rps: 2800    // Target 2.8k RPS
    },
    '20x': {
      vus: 200,    // 20x the baseline VUs
      rps: 15000   // Target 15k RPS
    }
  };

  const scaleLevelKey = scaleLevel || 'baseline';
  const config = SCALE_CONFIGS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(SCALE_CONFIGS).join(', ')} or "vus,rps" format`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    query_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.rps },    // Ramp up to target RPS
        { duration: STEADY_DURATION, target: CONFIG.rps },     // Maintain target RPS
        { duration: RAMP_DOWN_DURATION, target: 0 }           // Ramp down to 0
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * 2, // Allow K6 to scale up if needed
    },
  },
  thresholds: {
    'query_latency': [
      { threshold: 'p(50)<1000', abortOnFail: false },  // 50th percentile < 1000ms
      { threshold: 'p(95)<2000', abortOnFail: false },  // 95th percentile < 2000ms
      { threshold: 'p(99)<5000', abortOnFail: false }   // 99th percentile < 5000ms
    ],
    'error_rate': [
      { threshold: 'rate<0.005', abortOnFail: false }  // 0.5% error rate allowed
    ],
    'timeout_errors': [
      { threshold: 'count<100', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<50', abortOnFail: false }
    ]
  },
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for request: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for request: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for request: ${response.request.url}`);
  }
}

export default function () {
  const startTime = new Date().getTime();

  // Prepare query payload
  const payload = {
    is_root: true,
    start_time: START_TIME,
    use_experimental_search: true,
    order_by: ["-start_time"]  // Order by start_time descending
  };

  // Add project_id if provided
  if (PROJECT_ID) {
    payload.project_id = PROJECT_ID;
  }

  const response = http.post(
    `${BASE_URL}/runs/query`,
    JSON.stringify(payload),
    {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
      },
      timeout: '10s', // Timeout after 10 seconds
    }
  );

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  queryLatency.add(duration);
  
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 5s': (r) => r.timings.duration < 5000, // Align with 5s target
    'has runs array': (r) => {
      try {
        const body = JSON.parse(r.body);
        return Array.isArray(body.runs);
      } catch (e) {
        return false;
      }
    }
  });

  if (success) {
    successfulQueries.add(1);
  } else {
    failedQueries.add(1);
    errorRate.add(1);
    categorizeError(response);
  }
}

// Log test configuration on startup
console.log('Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: BASE_URL,
  startTime: START_TIME,
  projectId: PROJECT_ID || 'not provided'
}); 
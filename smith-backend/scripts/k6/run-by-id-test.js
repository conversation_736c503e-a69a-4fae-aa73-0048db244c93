// langchainplus/smith-backend/scripts/k6/run-by-id-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';

/*
 * Run-by-ID Load Test for LangSmith
 *
 * This script tests read performance via the GET /runs/<id> endpoint.
 *
 * Configuration Options (via environment variables):
 * - RPS_PER_VU_RATIO: How many RPS per VU (default: 10). Lower = more concurrent connections.
 * - BASELINE_RPS/VUS: Override baseline scale values (defaults: 10 RPS, 5 VUs)
 * - SCALE_4X_RPS/VUS: Override 4x scale values (defaults: 40 RPS, 15 VUs)
 * - SCALE_20X_RPS/VUS: Override 20x scale values (defaults: 100 RPS, 50 VUs)
 * - VU_SCALE_MULTIPLIER: Max VU scaling factor (default: 2)
 *
 * Examples:
 *   RPS_PER_VU_RATIO=5 k6 run run-by-id-test.js   # More aggressive VU scaling
 *   BASELINE_RPS=20 k6 run run-by-id-test.js      # Higher baseline load
 */

// Custom metrics
const readLatency = new Trend('read_latency');
const successfulReads = new Counter('successful_reads');
const failedReads = new Counter('failed_reads');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const RUN_ID = __ENV.TEST_CONFIG || __ENV.TEST_RUN_ID || '89323346-3373-4822-a016-55115160f4b3'; // Support both new and old format, with default fallback
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// ===== CONFIGURATION SECTION =====
// These values can be tweaked to adjust load testing behavior
const LOAD_TEST_CONFIG = {
  // VU calculation: How many RPS per VU for read operations
  // Higher ratio = fewer VUs for given RPS (read operations are lighter)
  RPS_PER_VU_RATIO: parseInt(__ENV.RPS_PER_VU_RATIO || '10'),

  // Default scale levels - can be overridden via environment
  SCALE_DEFAULTS: {
    baseline: {
      vus: parseInt(__ENV.BASELINE_VUS || '5'),      // Lower VUs for read operations
      rps: parseInt(__ENV.BASELINE_RPS || '10')      // Conservative baseline for reads
    },
    '4x': {
      vus: parseInt(__ENV.SCALE_4X_VUS || '15'),     // 4x scale
      rps: parseInt(__ENV.SCALE_4X_RPS || '40')      // 4x RPS
    },
    '20x': {
      vus: parseInt(__ENV.SCALE_20X_VUS || '50'),    // 20x scale
      rps: parseInt(__ENV.SCALE_20X_RPS || '100')    // 20x RPS
    }
  },

  // VU scaling multiplier - how much to allow K6 to scale up if needed
  VU_SCALE_MULTIPLIER: parseInt(__ENV.VU_SCALE_MULTIPLIER || '2')
};
// ===== END CONFIGURATION SECTION =====

// Parse scale configuration from environment (format: "rps" or "vus,rps" or "baseline,rps_ratio=10,vu_mult=3")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // Parse extended format with key=value parameters
  if (scaleLevel && scaleLevel.includes(',')) {
    const parts = scaleLevel.split(',');
    const basePart = parts[0];

    // Initialize with default config values
    let currentConfig = { ...LOAD_TEST_CONFIG };

    // Parse key=value parameters
    parts.slice(1).forEach(part => {
      if (part.includes('=')) {
        const [key, value] = part.split('=');
        const numValue = parseInt(value);

        switch (key.trim()) {
          case 'rps_ratio':
            currentConfig.RPS_PER_VU_RATIO = numValue;
            break;
          case 'vu_mult':
            currentConfig.VU_SCALE_MULTIPLIER = numValue;
            break;
          default:
            console.warn(`Unknown parameter: ${key}=${value}`);
        }
      }
    });

    // Check if basePart is VUs,RPS format (legacy)
    if (!isNaN(parseInt(basePart))) {
      // Could be "50,200,rps_ratio=3" or just "50,200"
      const secondPart = parts[1];
      if (secondPart && !secondPart.includes('=') && !isNaN(parseInt(secondPart))) {
        // Legacy VUs,RPS format: "50,200,rps_ratio=3"
        const vus = parseInt(basePart);
        const rps = parseInt(secondPart);
        return { vus, rps };
      } else {
        // RPS with parameters: "100,rps_ratio=3"
        const rps = parseInt(basePart);
        const vus = Math.max(1, Math.ceil(rps / currentConfig.RPS_PER_VU_RATIO));
        return { vus, rps };
      }
    }

    // Named scale level with parameters: "baseline,rps_ratio=10,vu_mult=3"
    const config = currentConfig.SCALE_DEFAULTS[basePart];
    if (config) {
      return {
        vus: config.vus,
        rps: config.rps,
        vuMultiplier: currentConfig.VU_SCALE_MULTIPLIER
      };
    }
  }

  // If SCALE_LEVEL is just a number (RPS only)
  if (scaleLevel && !isNaN(parseInt(scaleLevel))) {
    const rps = parseInt(scaleLevel);
    const vus = Math.max(1, Math.ceil(rps / LOAD_TEST_CONFIG.RPS_PER_VU_RATIO));
    return { vus, rps };
  }

  // Fallback to configurable scale configurations
  const scaleLevelKey = scaleLevel || 'baseline';
  const config = LOAD_TEST_CONFIG.SCALE_DEFAULTS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(LOAD_TEST_CONFIG.SCALE_DEFAULTS).join(', ')}, "rps" format (e.g. "50"), "vus,rps" format, or extended format (e.g. "baseline,rps_ratio=10,vu_mult=3")`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    read_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.rps },    // Ramp up to target RPS
        { duration: STEADY_DURATION, target: CONFIG.rps },     // Maintain target RPS
        { duration: RAMP_DOWN_DURATION, target: 0 }           // Ramp down to 0
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * LOAD_TEST_CONFIG.VU_SCALE_MULTIPLIER, // Allow K6 to scale up if needed
    },
  },
  thresholds: {
    'read_latency': [
        { threshold: 'p(50)<800', abortOnFail: false },   // Current p50 is 664ms
        { threshold: 'p(90)<1500', abortOnFail: false },  // Current p90 is 1.08s
        { threshold: 'p(95)<2000', abortOnFail: false }   // Current p95 is 1.28s
    ]
  },
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408) {
    timeoutErrors.add(1);
    console.error(`Timeout error for request: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for request: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for request: ${response.request.url}`);
  }
}

export default function () {
  const startTime = new Date().getTime();
  const response = http.get(`${BASE_URL}/runs/${RUN_ID}`, {
    headers: {
      'x-api-key': API_KEY, 
      'Content-Type': 'application/json',
    },
    timeout: '10s', // Timeout after 10 seconds
  });

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  readLatency.add(duration);
  
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 5s': (r) => r.timings.duration < 5000, // Align with 5s target
  });

  if (success) {
    successfulReads.add(1);
  } else {
    failedReads.add(1);
    errorRate.add(1);
    categorizeError(response);
  }
}

// Log test configuration on startup
console.log('Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: BASE_URL
});
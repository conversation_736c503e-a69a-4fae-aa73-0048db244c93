import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend, Counter, Rate } from 'k6/metrics';
import { uuidv4 } from 'https://jslib.k6.io/k6-utils/1.4.0/index.js';

/*
 * Data Export Load Test for LangSmith
 *
 * This script tests database read performance for large result sets via data export endpoints.
 *
 * Configuration Options (via environment variables):
 * - RPS_PER_VU_RATIO: How many RPS per VU (default: 10). Lower = more concurrent connections.
 * - BASELINE_RPS/VUS: Override baseline scale values (defaults: 10 RPS, 5 VUs)
 * - SCALE_4X_RPS/VUS: Override 4x scale values (defaults: 40 RPS, 20 VUs)
 * - SCALE_20X_RPS/VUS: Override 20x scale values (defaults: 200 RPS, 100 VUs)
 * - VU_SCALE_MULTIPLIER: Max VU scaling factor (default: 2)
 *
 * Examples:
 *   RPS_PER_VU_RATIO=5 k6 run data-export-test.js   # More aggressive VU scaling
 *   BASELINE_RPS=20 k6 run data-export-test.js      # Higher baseline load
 */

// Custom metrics
const exportLatency = new Trend('export_latency');
const successfulExports = new Counter('successful_exports');
const failedExports = new Counter('failed_exports');
const errorRate = new Rate('error_rate');
const timeoutErrors = new Counter('timeout_errors');
const serverErrors = new Counter('server_errors');
const clientErrors = new Counter('client_errors');
const largeResponseCount = new Counter('large_responses'); // Responses > 1MB

// Test configuration from environment variables
const API_KEY = __ENV.LANGCHAIN_API_KEY;
const BASE_URL = __ENV.LANGCHAIN_ENDPOINT;
const PROJECT_ID = __ENV.PROJECT_ID; // Optional project filter
const EXPORT_TYPE = __ENV.EXPORT_TYPE || 'runs'; // runs, datasets, feedbacks
const BATCH_SIZE = parseInt(__ENV.BATCH_SIZE || '1000'); // Number of records to export
const RAMP_UP_DURATION = __ENV.RAMP_UP_DURATION || '2m';
const STEADY_DURATION = __ENV.STEADY_DURATION || '5m';
const RAMP_DOWN_DURATION = __ENV.RAMP_DOWN_DURATION || '1m';

// ===== CONFIGURATION SECTION =====
// These values can be tweaked to adjust load testing behavior
const LOAD_TEST_CONFIG = {
  // VU calculation: How many RPS per VU for bulk export operations
  // Higher ratio = fewer VUs for given RPS (bulk operations are resource intensive)
  RPS_PER_VU_RATIO: parseInt(__ENV.RPS_PER_VU_RATIO || '10'),

  // Default scale levels - can be overridden via environment
  SCALE_DEFAULTS: {
    baseline: {
      vus: parseInt(__ENV.BASELINE_VUS || '5'),      // Bulk operations need fewer VUs
      rps: parseInt(__ENV.BASELINE_RPS || '10')      // Lower RPS for large data transfers
    },
    '4x': {
      vus: parseInt(__ENV.SCALE_4X_VUS || '20'),     // 4x scale
      rps: parseInt(__ENV.SCALE_4X_RPS || '40')      // 4x RPS
    },
    '20x': {
      vus: parseInt(__ENV.SCALE_20X_VUS || '100'),   // 20x scale
      rps: parseInt(__ENV.SCALE_20X_RPS || '200')    // 20x RPS
    }
  },

  // VU scaling multiplier - how much to allow K6 to scale up if needed
  VU_SCALE_MULTIPLIER: parseInt(__ENV.VU_SCALE_MULTIPLIER || '2')
};
// ===== END CONFIGURATION SECTION =====

// Parse scale configuration from environment (format: "rps" or "vus,rps" or "baseline,rps_ratio=10,vu_mult=3")
function parseScaleConfig() {
  const scaleLevel = __ENV.SCALE_LEVEL;

  // Parse extended format with key=value parameters
  if (scaleLevel && scaleLevel.includes(',')) {
    const parts = scaleLevel.split(',');
    const basePart = parts[0];

    // Initialize with default config values
    let currentConfig = { ...LOAD_TEST_CONFIG };

    // Parse key=value parameters
    parts.slice(1).forEach(part => {
      if (part.includes('=')) {
        const [key, value] = part.split('=');
        const numValue = parseInt(value);

        switch (key.trim()) {
          case 'rps_ratio':
            currentConfig.RPS_PER_VU_RATIO = numValue;
            break;
          case 'vu_mult':
            currentConfig.VU_SCALE_MULTIPLIER = numValue;
            break;
          default:
            console.warn(`Unknown parameter: ${key}=${value}`);
        }
      }
    });

    // Check if basePart is VUs,RPS format (legacy)
    if (!isNaN(parseInt(basePart))) {
      // Could be "50,200,rps_ratio=3" or just "50,200"
      const secondPart = parts[1];
      if (secondPart && !secondPart.includes('=') && !isNaN(parseInt(secondPart))) {
        // Legacy VUs,RPS format: "50,200,rps_ratio=3"
        const vus = parseInt(basePart);
        const rps = parseInt(secondPart);
        return { vus, rps };
      } else {
        // RPS with parameters: "100,rps_ratio=3"
        const rps = parseInt(basePart);
        const vus = Math.max(1, Math.ceil(rps / currentConfig.RPS_PER_VU_RATIO));
        return { vus, rps };
      }
    }

    // Named scale level with parameters: "baseline,rps_ratio=10,vu_mult=3"
    const config = currentConfig.SCALE_DEFAULTS[basePart];
    if (config) {
      return {
        vus: config.vus,
        rps: config.rps,
        vuMultiplier: currentConfig.VU_SCALE_MULTIPLIER
      };
    }
  }

  // If SCALE_LEVEL is just a number (RPS only)
  if (scaleLevel && !isNaN(parseInt(scaleLevel))) {
    const rps = parseInt(scaleLevel);
    const vus = Math.max(1, Math.ceil(rps / LOAD_TEST_CONFIG.RPS_PER_VU_RATIO));
    return { vus, rps };
  }

  // Fallback to configurable scale configurations
  const scaleLevelKey = scaleLevel || 'baseline';
  const config = LOAD_TEST_CONFIG.SCALE_DEFAULTS[scaleLevelKey];

  if (!config) {
    throw new Error(`Invalid scale level: ${scaleLevelKey}. Must be one of: ${Object.keys(LOAD_TEST_CONFIG.SCALE_DEFAULTS).join(', ')}, "rps" format (e.g. "50"), "vus,rps" format, or extended format (e.g. "baseline,rps_ratio=10,vu_mult=3")`);
  }

  return config;
}

// Get scale configuration
const CONFIG = parseScaleConfig();

export const options = {
  scenarios: {
    bulk_export_test: {
      executor: 'ramping-arrival-rate',
      startRate: 0,
      stages: [
        { duration: RAMP_UP_DURATION, target: CONFIG.rps },
        { duration: STEADY_DURATION, target: CONFIG.rps },
        { duration: RAMP_DOWN_DURATION, target: 0 }
      ],
      preAllocatedVUs: CONFIG.vus,
      maxVUs: CONFIG.vus * LOAD_TEST_CONFIG.VU_SCALE_MULTIPLIER, // Allow K6 to scale up if needed
    },
  },
  thresholds: {
    'export_latency': [
      { threshold: 'p(50)<5000', abortOnFail: false },   // 50th percentile < 5s (bulk ops are slower)
      { threshold: 'p(95)<15000', abortOnFail: false },  // 95th percentile < 15s
      { threshold: 'p(99)<30000', abortOnFail: false }   // 99th percentile < 30s
    ],
    'error_rate': [
      { threshold: 'rate<0.02', abortOnFail: false }     // 2% error rate allowed for bulk ops
    ],
    'timeout_errors': [
      { threshold: 'count<20', abortOnFail: false }
    ],
    'server_errors': [
      { threshold: 'count<10', abortOnFail: false }
    ]
  },
};

// Error categorization helper
function categorizeError(response) {
  if (response.status === 0 || response.status === 408 || response.status === 504) {
    timeoutErrors.add(1);
    console.error(`Timeout error for bulk export: ${response.request.url}`);
  } else if (response.status >= 500) {
    serverErrors.add(1);
    console.error(`Server error ${response.status} for bulk export: ${response.request.url}`);
  } else if (response.status >= 400) {
    clientErrors.add(1);
    console.error(`Client error ${response.status} for bulk export: ${response.request.url}`);
  }
}

// Generate test data for bulk export
function generateExportPayload() {
  const now = new Date();
  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
  
  const basePayload = {
    start_time: oneHourAgo.toISOString(),
    end_time: now.toISOString(),
    limit: BATCH_SIZE,
  };

  // Add project filter if provided
  if (PROJECT_ID) {
    basePayload.project_id = PROJECT_ID;
  }

  switch (EXPORT_TYPE) {
    case 'runs':
      return {
        ...basePayload,
        run_type: 'chain', // Filter for specific run types
        order_by: ['-start_time']
      };
    case 'datasets':
      return {
        ...basePayload,
        data_type: 'llm' // Filter for LLM datasets
      };
    case 'feedbacks':
      return {
        ...basePayload,
        feedback_source_type: 'api'
      };
    default:
      return basePayload;
  }
}

export default function () {
  const startTime = new Date().getTime();
  
  // Generate export request
  const payload = generateExportPayload();
  
  // Determine endpoint based on export type
  let endpoint;
  switch (EXPORT_TYPE) {
    case 'runs':
      endpoint = '/runs/query';
      break;
    case 'datasets':
      endpoint = '/datasets';
      break;
    case 'feedbacks':
      endpoint = '/feedback';
      break;
    default:
      endpoint = '/runs/query';
  }

  const response = http.post(
    `${BASE_URL}${endpoint}`,
    JSON.stringify(payload),
    {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      timeout: '60s', // Longer timeout for bulk operations
    }
  );

  // Record detailed metrics
  const duration = new Date().getTime() - startTime;
  exportLatency.add(duration);
  
  // Check response size (database stress indicator)
  const responseSize = response.body ? response.body.length : 0;
  if (responseSize > 1024 * 1024) { // > 1MB
    largeResponseCount.add(1);
  }
  
  const success = check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 60s': (r) => r.timings.duration < 60000,
    'has data array': (r) => {
      try {
        const body = JSON.parse(r.body);
        // Check for different response structures based on endpoint
        return Array.isArray(body.runs) || Array.isArray(body.datasets) || Array.isArray(body.feedback) || Array.isArray(body);
      } catch (e) {
        return false;
      }
    },
    'response size > 1KB': (r) => r.body && r.body.length > 1024, // Ensure we're getting data
  });

  if (success) {
    successfulExports.add(1);
    console.log(`Bulk export successful: ${responseSize} bytes, ${duration}ms, endpoint: ${endpoint}`);
  } else {
    failedExports.add(1);
    errorRate.add(1);
    categorizeError(response);
    console.error(`Bulk export failed: ${response.status}, ${duration}ms, endpoint: ${endpoint}`);
  }
}

// Log test configuration on startup
console.log('Bulk Export Load Test Configuration:', {
  scaleLevel: __ENV.SCALE_LEVEL,
  targetVUs: CONFIG.vus,
  targetRPS: CONFIG.rps,
  exportType: EXPORT_TYPE,
  batchSize: BATCH_SIZE,
  rampUp: RAMP_UP_DURATION,
  steady: STEADY_DURATION,
  rampDown: RAMP_DOWN_DURATION,
  endpoint: BASE_URL,
  projectId: PROJECT_ID || 'all projects'
}); 
import argparse
import json
import os
from datetime import datetime, timedelta

import requests

BASE_URL = "https://api.metronome.com"


def make_request(
    token,
    path,
    payload=None,
    method="POST",
    version="v1",
    params=None,
    full_response=False,
):
    response = requests.request(
        method,
        f"{BASE_URL}/{version}/{path}",
        headers={"Authorization": f"Bearer {token}"},
        json=payload,
        params=params,
    )
    if response.status_code != 200:
        print(response.text)
    response.raise_for_status()
    return response.json()["data"] if not full_response else response.json()


def fetch_products(token):
    return make_request(
        token, "contract-pricing/products/list", {"archive_filter": "NOT_ARCHIVED"}
    )


def fetch_plans(token):
    return make_request(token, "plans", params={"limit": 100}, method="GET")


def fetch_rate_cards(token):
    return make_request(token, "contract-pricing/rate-cards/list", payload={})


def fetch_billable_metrics(token):
    return make_request(token, "billable-metrics", method="GET")


def create_billable_metric(token, metric, dry_run=False):
    payload = {
        "name": metric["name"],
        **(
            {"event_type_filter": metric.get("event_type_filter")}
            if metric.get("event_type_filter")
            else {}
        ),
        **(
            {"group_keys": metric.get("group_keys")} if metric.get("group_keys") else {}
        ),
        **(
            {"property_filters": metric.get("property_filters")}
            if metric.get("property_filters")
            else {}
        ),
        **(
            {"aggregation_type": metric.get("aggregation_type")}
            if metric.get("aggregation_type")
            else {}
        ),
        **(
            {"aggregation_key": metric.get("aggregation_key")}
            if metric.get("aggregation_key")
            else {}
        ),
        **(
            {"custom_fields": metric.get("custom_fields")}
            if metric.get("custom_fields")
            else {}
        ),
        **({"sql": metric.get("sql")} if metric.get("sql") else {}),
    }
    if dry_run:
        print(
            f"[DRY RUN] Would create billable metric: {json.dumps(payload, indent=2)}\nfrom source: {json.dumps(metric, indent=2)}"
        )
        return
    print(f"Creating billable metric: {metric['name']}")
    return make_request(token, "billable-metrics/create", payload)


def create_product(token, product, billable_metric_id=None, dry_run=False):
    name = product["current"]["name"]
    payload = {
        "name": name,
        "type": product["type"],
        "custom_fields": product.get("custom_fields", {}),
    }

    if billable_metric_id:
        payload["billable_metric_id"] = billable_metric_id

    if dry_run:
        print(
            f"[DRY RUN] Would create product: {json.dumps(payload, indent=2)}\nfrom source: {json.dumps(product, indent=2)}"
        )
        return
    print(f"Creating product: {name}")
    return make_request(token, "contract-pricing/products/create", payload)


def update_product(token, product_id, product, dry_run=False):
    name = product["current"]["name"]
    payload = {
        "product_id": product_id,
        "name": name,
        "custom_fields": product.get("custom_fields", {}),
    }
    if dry_run:
        print(
            f"[DRY RUN] Would update product: {json.dumps(payload, indent=2)}\nfrom source: {json.dumps(product, indent=2)}"
        )
        return
    print(f"Updating product: {name}")
    return make_request(token, "contract-pricing/products/update", payload)


def create_rate_card(token, rate_card, dry_run=False):
    name = rate_card["name"]
    payload = {
        "name": name,
        "description": rate_card.get("description", ""),
        "aliases": rate_card.get("aliases", []),
        "custom_fields": rate_card.get("custom_fields", {}),
    }
    if dry_run:
        print(
            f"[DRY RUN] Would create rate card: {json.dumps(payload, indent=2)}\nfrom source: {json.dumps(rate_card, indent=2)}"
        )
        return
    print(f"Creating rate card: {name}")
    return make_request(token, "contract-pricing/rate-cards/create", payload)


def update_rate_card(token, rate_card_id, rate_card, dry_run=False):
    name = rate_card["name"]
    payload = {
        "rate_card_id": rate_card_id,
        "name": name,
        "description": rate_card.get("description", ""),
        "fiat_credit_type_id": rate_card["fiat_credit_type"]["id"],
        "credit_type_conversions": [
            {
                "custom_credit_type_id": rate_card["fiat_credit_type"]["id"],
                "fiat_per_custom_credit": 1,
            }
        ],
        "aliases": rate_card.get("aliases", []),
        "custom_fields": rate_card.get("custom_fields", {}),
    }
    if dry_run:
        print(f"[DRY RUN] Would update rate card: {json.dumps(payload, indent=2)}")
        return
    print(f"Updating rate card: {name}")
    return make_request(token, "contract-pricing/rate-cards/update", payload)


def create_contract(
    token,
    customer_id,
    rate_card_id,
    traces_product_id,
    commit_product_id,
    dry_run=False,
):
    today = datetime.now().date()
    one_year = today + timedelta(days=364)

    starting_at = today.isoformat() + "T00:00:00Z"
    ending_before = one_year.isoformat() + "T00:00:00Z"

    payload = {
        "customer_id": customer_id,
        "rate_card_id": rate_card_id,
        "starting_at": starting_at,
        "usage_statement_schedule": {
            "frequency": "monthly",
            "day": "first_of_month",
        },
        "commits": [
            {
                "product_id": commit_product_id,
                "description": "Prepaid - 2,000,000 traces",
                "priority": 1,
                "rate_type": "list_rate",
                "type": "prepaid",
                "applicable_product_ids": [traces_product_id],
                "access_schedule": {
                    "schedule_items": [
                        {
                            "amount": 2_000_000,
                            "starting_at": starting_at,
                            "ending_before": ending_before,
                        }
                    ]
                },
                "invoice_schedule": {
                    "schedule_items": [
                        {
                            "timestamp": starting_at,
                            "unit_price": 2_000_000,
                            "quantity": 1,
                        }
                    ]
                },
            }
        ],
    }

    if dry_run:
        print(
            f"[DRY RUN] Would create contract for customer {customer_id} with payload:"
        )
        print(json.dumps(payload, indent=2))
        return

    print(f"Creating contract for customer {customer_id} {payload}")
    return make_request(token, "contracts/create", payload)


def fetch_contracts_for_customer(token, customer_id):
    return make_request(
        token,
        "contracts/list",
        payload={"customer_id": customer_id},
        version="v2",
    )


def fetch_customers(token):
    customers = []
    next_page = None
    while True:
        params = {"limit": 100}
        if next_page:
            params["next_page"] = next_page
        response = make_request(
            token,
            "customers",
            params=params,
            method="GET",
            full_response=True,
        )
        customers.extend(response["data"])
        print(f"Fetched {len(customers)} customers")
        if not response.get("next_page"):
            break
        next_page = response["next_page"]
    return customers


def fetch_customers_for_plan(token, plan_id):
    customers = []
    next_page = None
    while True:
        params = {"limit": 100}
        if next_page:
            params["next_page"] = next_page
        response = make_request(
            token,
            f"planDetails/{plan_id}/customers",
            params=params,
            method="GET",
            full_response=True,
        )
        customers.extend(response["data"])
        print(f"Fetched {len(customers)} customers for plan {plan_id}")
        if not response.get("next_page"):
            break
        next_page = response["next_page"]
    return customers


def fetch_plans_for_customer(token, customer_id):
    return make_request(
        token,
        f"customers/{customer_id}/plans",
        params={"limit": 100},
        method="GET",
    )


def sync_resources(
    update_existing: bool,
    dry_run: bool,
    source_token: str,
    dest_token: str,
    sync_metrics: bool,
    sync_products: bool,
    sync_rate_cards: bool,
    sync_contracts: bool,
):
    dest_metrics_by_name = {}

    if sync_metrics or sync_products:
        source_metrics = fetch_billable_metrics(source_token)
        dest_metrics = fetch_billable_metrics(dest_token)
        dest_metrics_by_name = {m["name"]: m for m in dest_metrics}

        if sync_metrics:
            if dry_run:
                print("\n--- Destination Billable Metrics ---")
                for m in dest_metrics:
                    print(f"- {m['name']}")
                print("\n--- Billable Metrics to Create / Update ---")

            for metric in source_metrics:
                if metric["name"] not in dest_metrics_by_name:
                    create_billable_metric(dest_token, metric, dry_run=dry_run)

        if sync_products:
            source_products = fetch_products(source_token)
            dest_products = fetch_products(dest_token)
            dest_products_by_name = {p["current"]["name"]: p for p in dest_products}

            if dry_run:
                print("\n--- Source Products ---")
                for p in source_products:
                    print(f"- {p['current']['name']}")
                print("\n--- Destination Products ---")
                for p in dest_products:
                    print(f"- {p['current']['name']}")
                print("\n--- Products to Create / Update ---")
                for p in source_products:
                    if p["current"]["name"] not in dest_products_by_name:
                        print(f"- {p['current']['name']}")

            for traces_product in source_products:
                name = traces_product["current"]["name"]
                if name in dest_products_by_name:
                    if update_existing:
                        update_product(
                            dest_token,
                            dest_products_by_name[name]["id"],
                            traces_product,
                            dry_run=dry_run,
                        )
                else:
                    if traces_product["current"].get("billable_metric_id"):
                        source_billable_metric = next(
                            (
                                x
                                for x in source_metrics
                                if x["id"]
                                == traces_product["current"]["billable_metric_id"]
                            ),
                            None,
                        )
                        if not source_billable_metric:
                            print(
                                f"Billable metric {traces_product['current']['billable_metric_id']} not found in source"
                            )
                            continue
                        source_billable_metric_name = source_billable_metric["name"]
                        if dest_metric := dest_metrics_by_name.get(
                            source_billable_metric_name
                        ):
                            create_product(
                                dest_token,
                                traces_product,
                                billable_metric_id=dest_metric["id"],
                                dry_run=dry_run,
                            )
                        else:
                            print(
                                f"Billable metric {source_billable_metric_name} not found in destination"
                            )
                    else:
                        create_product(dest_token, traces_product, dry_run=dry_run)

    if sync_rate_cards:
        source_rate_cards = fetch_rate_cards(source_token)
        dest_rate_cards = fetch_rate_cards(dest_token)
        dest_rate_cards_by_name = {rc["name"]: rc for rc in dest_rate_cards}

        if dry_run:
            print("\n--- Destination Rate Cards ---")
            for rc in dest_rate_cards:
                print(f"- {rc['name']}")
            print("\n--- Rate Cards to Create / Update ---")

        for rate_card in source_rate_cards:
            name = rate_card["name"]
            if name in dest_rate_cards_by_name:
                if update_existing:
                    update_rate_card(
                        dest_token,
                        dest_rate_cards_by_name[name]["id"],
                        rate_card,
                        dry_run=dry_run,
                    )
            else:
                create_rate_card(dest_token, rate_card, dry_run=dry_run)

    if sync_contracts:
        valid_tiers = {"enterprise_legacy", "enterprise"}
        rate_card_name = "LangChain Enterprise Customers - Traces Only"
        rate_cards = fetch_rate_cards(dest_token)
        rate_card = next(
            (rc for rc in rate_cards if rc["name"] == rate_card_name), None
        )

        if not rate_card:
            print(f"No rate card available to assign contracts for {rate_card_name}")
            return

        products = fetch_products(dest_token)
        product_name = "LangSmith Traces"
        commit_name = "Pre-Paid Commit"
        traces_product = next(
            (p for p in products if p["current"]["name"] == product_name), None
        )
        commit_product = next(
            (p for p in products if p["current"]["name"] == commit_name),
            None,
        )
        if not traces_product or not commit_product:
            print(f"No product available with name {product_name} or {commit_name}")
            return

        customers = []
        plans = fetch_plans(dest_token)
        for tier in valid_tiers:
            plan_for_tier = next(
                (p for p in plans if p.get("custom_fields", {}).get("__tier") == tier),
                None,
            )
            if not plan_for_tier:
                print(f"No plan available for tier {tier}")
                continue
            customers.extend(fetch_customers_for_plan(dest_token, plan_for_tier["id"]))

        for customer in customers:
            customer_id = customer["customer_details"]["id"]
            customer_name = customer["customer_details"]["name"]
            existing_contracts = fetch_contracts_for_customer(dest_token, customer_id)
            if existing_contracts:
                print(
                    f"Skipping customer {customer_name} ({customer_id}) with existing contract {existing_contracts}"
                )
                continue  # already has a contract
            print(f"Creating contract for customer {customer_name} ({customer_id})")
            create_contract(
                dest_token,
                customer_id,
                rate_card["id"],
                traces_product["id"],
                commit_product["id"],
                dry_run=dry_run,
            )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Sync Metronome resources between environments"
    )
    parser.add_argument(
        "--update-existing",
        action="store_true",
        help="Update existing resources with source content",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Print what would happen without making any changes",
    )
    parser.add_argument(
        "--sync-billable-metrics", action="store_true", help="Sync billable metrics"
    )
    parser.add_argument("--sync-products", action="store_true", help="Sync products")
    parser.add_argument(
        "--sync-rate-cards", action="store_true", help="Sync rate cards"
    )
    parser.add_argument(
        "--sync-contracts",
        action="store_true",
        help="Sync contracts for customers based on tier",
    )

    args = parser.parse_args()

    source_token = os.getenv("METRONOME_SRC_API_KEY")
    dest_token = os.getenv("METRONOME_DEST_API_KEY")

    if not source_token or not dest_token:
        raise Exception(
            "Please set METRONOME_SRC_API_KEY and METRONOME_DEST_API_KEY environment variables."
        )

    sync_resources(
        update_existing=args.update_existing,
        dry_run=args.dry_run,
        source_token=source_token,
        dest_token=dest_token,
        sync_metrics=args.sync_billable_metrics,
        sync_products=args.sync_products,
        sync_rate_cards=args.sync_rate_cards,
        sync_contracts=args.sync_contracts,
    )

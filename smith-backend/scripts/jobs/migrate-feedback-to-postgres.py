#!/usr/bin/env python3
"""
Back-fill feedbacks from ClickHouse → Postgres.

Example:
    poetry run python backfill_feedbacks.py
"""

import asyncio
import functools as _ft
import logging
from datetime import datetime, timezone
from typing import Any, Tuple
from uuid import UUID

import asyncpg
import click
import orjson
from aiochclient import Record
from dateutil.relativedelta import relativedelta
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_conn

from app.models.feedback.ingest import COLUMNS
from app.models.feedback.utils import normalize_feedback_key
from app.retry import retry_asyncpg

# ---------------------------------------------------------------------------

logger = logging.getLogger(__name__)
logging.basicConfig(
    format="%(asctime)s  %(levelname)s  %(message)s", level=logging.INFO
)

PG_BATCH_SIZE = 1000

ZERO_UUID_STR = "00000000-0000-0000-0000-000000000000"
ZERO_UUID = UUID(ZERO_UUID_STR)


def _safe_json_dict(raw):
    if raw is None or raw in ("", "{}", "null"):
        return None
    if isinstance(raw, dict):
        return raw
    if isinstance(raw, str):
        try:
            parsed = orjson.loads(raw)
            return parsed if isinstance(parsed, dict) else None
        except orjson.JSONDecodeError:
            logger.warning("Failed to parse feedback_source: %s", raw)
            return None
    return None


def _to_uuid(val):
    if (
        val is None
        or val in ("", ZERO_UUID_STR)
        or (isinstance(val, UUID) and val.int == 0)
    ):
        return None
    return val if isinstance(val, UUID) else UUID(val)


def _row_tuple(rec: dict) -> tuple[Any, ...]:
    run_id = _to_uuid(rec.get("run_id"))
    comp_experiment_id = _to_uuid(rec.get("comparative_experiment_id"))
    feedback_group_id = _to_uuid(rec.get("feedback_group_id"))
    session_id = _to_uuid(rec.get("session_id"))

    fs_dict = _safe_json_dict(rec.get("feedback_source"))
    user_id = _to_uuid(fs_dict.get("user_id") if fs_dict else None)

    feedback_source_raw = rec.get("feedback_source")

    def _json_field(val):
        if val is None or val in ("", "{}", "null"):
            return None
        return val if isinstance(val, str) else orjson.dumps(val).decode("utf-8")

    value = _json_field(rec.get("value"))
    correction = _json_field(rec.get("correction"))
    extra = rec.get("extra")

    return (
        _to_uuid(rec["id"]),  # id
        run_id,  # run_id
        session_id,  # session_id
        user_id,  # user_id
        rec.get("t_id"),  # tenant_id
        rec.get("is_root", False),  # is_root
        rec.get("start_time") if run_id else None,  # start_time
        normalize_feedback_key(rec.get("key", "")),  # key
        rec.get("score"),  # score
        value,  # value
        rec.get("comment") or None,  # comment
        correction,  # correction
        rec.get("trace_id") if rec.get("trace_id") != ZERO_UUID else None,  # trace_id
        feedback_source_raw,  # feedback_source
        comp_experiment_id,  # comparative_experiment_id
        feedback_group_id,  # feedback_group_id
        extra,  # extra
        rec.get("created_at"),  # created_at
        rec.get("latest_modified_at"),  # modified_at
    )


async def _insert_batch_to_postgres(chunk: list[Tuple]) -> None:
    values_sql = ",\n".join(
        "(" + ",".join(f"${i}" for i in range(idx, idx + len(COLUMNS))) + ")"
        for idx in range(1, len(chunk) * len(COLUMNS) + 1, len(COLUMNS))
    )

    sql = f"""
        INSERT INTO feedbacks ({", ".join(COLUMNS)})
        VALUES {values_sql}
        ON CONFLICT (id) DO UPDATE
          SET
            run_id      = EXCLUDED.run_id,
            session_id  = EXCLUDED.session_id,
            user_id     = EXCLUDED.user_id,
            tenant_id   = EXCLUDED.tenant_id,
            is_root     = EXCLUDED.is_root,
            start_time  = EXCLUDED.start_time,
            key         = EXCLUDED.key,
            score       = EXCLUDED.score,
            value       = EXCLUDED.value,
            comment     = EXCLUDED.comment,
            correction  = EXCLUDED.correction,
            trace_id    = EXCLUDED.trace_id,
            feedback_source           = EXCLUDED.feedback_source,
            comparative_experiment_id = EXCLUDED.comparative_experiment_id,
            feedback_group_id         = EXCLUDED.feedback_group_id,
            extra                     = EXCLUDED.extra,
            modified_at               = EXCLUDED.modified_at;
        """

    flat_params = [v for row in chunk for v in row]
    async with asyncpg_conn() as conn:
        try:
            await conn.execute(sql, *flat_params)
        except asyncpg.exceptions.ForeignKeyViolationError as e:
            # One (or more) rows violate FK constraints.  Retry each row
            # individually so we can skip only the rows that violate the FK.
            logger.warning(
                "Foreign-key violation on batch (%d rows): %s — retrying row-by-row",
                len(chunk),
                e,
            )

            single_sql = (
                f"INSERT INTO feedbacks ({', '.join(COLUMNS)}) VALUES "
                f"({', '.join(f'${i}' for i in range(1, len(COLUMNS) + 1))}) "
                "ON CONFLICT (id) DO UPDATE SET "
                "run_id      = EXCLUDED.run_id, "
                "session_id  = EXCLUDED.session_id, "
                "user_id     = EXCLUDED.user_id, "
                "tenant_id   = EXCLUDED.tenant_id, "
                "is_root     = EXCLUDED.is_root, "
                "start_time  = EXCLUDED.start_time, "
                "key         = EXCLUDED.key, "
                "score       = EXCLUDED.score, "
                "value       = EXCLUDED.value, "
                "comment     = EXCLUDED.comment, "
                "correction  = EXCLUDED.correction, "
                "trace_id    = EXCLUDED.trace_id, "
                "feedback_source           = EXCLUDED.feedback_source, "
                "comparative_experiment_id = EXCLUDED.comparative_experiment_id, "
                "feedback_group_id         = EXCLUDED.feedback_group_id, "
                "extra                     = EXCLUDED.extra, "
                "modified_at               = EXCLUDED.modified_at;"
            )

            for row in chunk:
                try:
                    await conn.execute(single_sql, *row)
                except asyncpg.exceptions.ForeignKeyViolationError:
                    logger.warning("Skipping feedback %s due to FK violation", row[0])
                    continue


async def _insert_to_postgres(inserts: list[Record]):
    rows: list[Tuple] = [_row_tuple(ins) for ins in inserts]

    for start in range(0, len(rows), PG_BATCH_SIZE):
        chunk = rows[start : start + PG_BATCH_SIZE]
        await _insert_batch_to_postgres(chunk)

    logger.info("Upserted %d feedbacks into postgres", len(inserts))


async def _backfill_all_tenants() -> None:
    now = datetime.now(tz=timezone.utc).replace(
        hour=0, minute=0, second=0, microsecond=0
    )
    start_date = datetime(2023, 1, 1, tzinfo=timezone.utc)

    async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
        total_rows = 0
        current_date = start_date

        while current_date <= now:
            next_date = current_date + relativedelta(months=1)

            logger.info(
                "⏳ Processing month %s",
                current_date.strftime("%Y-%m"),
            )
            rows_this_month = 0

            sql_template = f"""
            SELECT
                id,
                argMax(run_id, modified_at)                  AS run_id,
                argMax(session_id, modified_at)              AS session_id,
                argMax(is_root, modified_at)                 AS is_root,
                argMax(start_time, modified_at)              AS start_time,
                min(created_at)                              AS created_at,
                argMax(modified_at, modified_at)             AS latest_modified_at,
                argMax(key, modified_at)                     AS key,
                argMax(score, modified_at)                   AS score,
                argMax(value, modified_at)                   AS value,
                argMax(comment, modified_at)                 AS comment,
                argMax(correction, modified_at)              AS correction,
                argMax(trace_id, modified_at)                AS trace_id,
                argMax(feedback_source, modified_at)         AS feedback_source,
                argMax(comparative_experiment_id, modified_at) AS comparative_experiment_id,
                argMax(feedback_group_id, modified_at)       AS feedback_group_id,
                argMax(extra, modified_at)                   AS extra
            FROM feedbacks_rmt
            WHERE modified_at >= toDateTime({current_date.timestamp():.0f})
              AND modified_at <  toDateTime({next_date.timestamp():.0f})
              AND is_deleted = 0
            GROUP BY id
            ORDER BY latest_modified_at, id
            """

            records = await ch.fetch(
                f"feedback_month_{current_date.strftime('%Y%m')}",
                sql_template,
            )
            if not records:
                logger.info("  • no rows for %s", current_date.strftime("%Y-%m"))
                current_date = next_date
                continue

            await _insert_to_postgres(records)

            rows_fetched = len(records)
            rows_this_month += rows_fetched
            total_rows += rows_fetched

            logger.info(
                "✅ Completed %s (rows: %d, cumulative: %d)",
                current_date.strftime("%Y-%m"),
                rows_this_month,
                total_rows,
            )
            current_date = next_date

    logger.info("🎉 All done – total rows upserted: %d", total_rows)


def _async_cmd(func):
    @_ft.wraps(func)
    def wrapper(*args, **kwargs):
        return asyncio.run(func(*args, **kwargs))

    return wrapper


@click.command(name="backfill-feedbacks")
@_async_cmd
@retry_asyncpg
async def cli() -> None:
    await _backfill_all_tenants()


if __name__ == "__main__":
    cli()

#!/usr/bin/env python3
"""
Back-fill feedback-configs from ClickHouse → Postgres.

Example:
    poetry run python backfill_feedback_configs.py
"""

import asyncio
import functools as _ft
import logging
from datetime import datetime, timezone
from typing import Any, <PERSON><PERSON>
from uuid import UUID

import asyncpg
import click
import or<PERSON><PERSON>
from aiochclient import Record
from dateutil.relativedelta import relativedelta
from lc_database.clickhouse import ClickhouseClient, clickhouse_client
from lc_database.database import asyncpg_conn

# ────────────────────────────────────────────────────────────────────────────
logger = logging.getLogger(__name__)
logging.basicConfig(
    format="%(asctime)s  %(levelname)s  %(message)s",
    level=logging.INFO,
)

PG_BATCH_SIZE = 1_000
COLUMNS = (
    "tenant_id",
    "feedback_key",
    "feedback_config",
    "modified_at",
    "is_lower_score_better",
)


def _safe_json(raw: Any) -> str | None:
    """Return a JSON string suitable for pg JSONB or None."""
    if raw in (None, "", "{}", "null", b""):
        return None
    if isinstance(raw, str):
        try:
            loaded = orjson.loads(raw)  # validate / de-double-encode
            return orjson.dumps(loaded).decode()
        except orjson.JSONDecodeError:
            logger.warning("⚠️  bad JSON – skipping: %s", raw[:120])
            return None
    if isinstance(raw, (bytes, bytearray)):
        return _safe_json(raw.decode())
    return orjson.dumps(raw).decode()  # already dict-like


def _row_tuple(rec: Record) -> Tuple[Any, ...]:
    return (
        UUID(rec["tenant_id"])
        if not isinstance(rec["tenant_id"], UUID)
        else rec["tenant_id"],
        rec["feedback_key"],
        _safe_json(rec["feedback_config"]),
        rec["modified_at"],
        bool(rec.get("is_lower_score_better", False)),
    )


async def _insert_pg(rows: list[Tuple[Any, ...]]) -> None:
    if not rows:
        return

    async with asyncpg_conn() as conn:
        for start in range(0, len(rows), PG_BATCH_SIZE):
            chunk_rows = rows[start : start + PG_BATCH_SIZE]

            values_sql = ",\n".join(
                "(" + ",".join(f"${i}" for i in range(idx, idx + len(COLUMNS))) + ")"
                for idx in range(1, len(chunk_rows) * len(COLUMNS) + 1, len(COLUMNS))
            )

            sql = f"""
            INSERT INTO feedback_configs ({", ".join(COLUMNS)})
            VALUES {values_sql}
            ON CONFLICT (tenant_id, feedback_key) DO UPDATE
              SET feedback_config        = EXCLUDED.feedback_config,
                  modified_at            = EXCLUDED.modified_at,
                  is_lower_score_better  = EXCLUDED.is_lower_score_better;
            """

            flat_params = [v for row in chunk_rows for v in row]

            try:
                await conn.execute(sql, *flat_params)
            except asyncpg.exceptions.ForeignKeyViolationError as e:
                logger.warning("FK violation batch-retry: %s", e)
                single_sql = (
                    f"INSERT INTO feedback_configs ({', '.join(COLUMNS)}) VALUES "
                    f"({', '.join(f'${i}' for i in range(1, len(COLUMNS) + 1))}) "
                    "ON CONFLICT (tenant_id, feedback_key) DO UPDATE "
                    "SET feedback_config        = EXCLUDED.feedback_config, "
                    "    modified_at            = EXCLUDED.modified_at, "
                    "    is_lower_score_better  = EXCLUDED.is_lower_score_better;"
                )
                for row in chunk_rows:
                    try:
                        await conn.execute(single_sql, *row)
                    except asyncpg.exceptions.ForeignKeyViolationError:
                        logger.error("❌  skipping (%s, %s)", row[0], row[1])


async def _backfill_tenant() -> None:
    logger.info("▶️  Back-filling feedback configs")

    start = datetime(2023, 1, 1, tzinfo=timezone.utc)
    now = datetime.now(tz=timezone.utc).replace(
        day=1, hour=0, minute=0, second=0, microsecond=0
    )

    total_rows = 0
    month_idx = 0

    async with clickhouse_client(ClickhouseClient.INTERNAL_ANALYTICS_SLOW) as ch:
        while start <= now:
            month_end = start + relativedelta(months=1)

            records = await ch.fetch(
                f"fetch_cfgs_{month_idx}",
                """
                SELECT DISTINCT ON (tenant_id, feedback_key)
                       tenant_id,
                       feedback_key,
                       feedback_config,
                       modified_at,
                       COALESCE(is_lower_score_better, 0) AS is_lower_score_better
                  FROM feedback_configs FINAL
                 WHERE modified_at >= toDateTime({from_ts})
                   AND modified_at <  toDateTime({to_ts})
                   AND is_deleted  = 0
              ORDER BY feedback_key, modified_at DESC
                """,
                params={
                    "from_ts": int(start.timestamp()),
                    "to_ts": int(month_end.timestamp()),
                },
            )

            if records:
                await _insert_pg([_row_tuple(r) for r in records])
                total_rows += len(records)
                logger.info(
                    "  • %s → upserted %d (cumulative %d)",
                    start.strftime("%Y-%m"),
                    len(records),
                    total_rows,
                )

            start = month_end
            month_idx += 1

    logger.info("✅ Migration complete – %d rows", total_rows)


def _async_cmd(fn):
    @_ft.wraps(fn)
    def wrapper(*args, **kwargs):
        return asyncio.run(fn(*args, **kwargs))

    return wrapper


@click.command(name="backfill-feedback-configs")
@_async_cmd
async def cli() -> None:
    await _backfill_tenant()


if __name__ == "__main__":
    cli()

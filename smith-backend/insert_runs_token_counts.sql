-- Insert query for runs_token_counts table
-- Simple insert from runs table to runs_token_counts table

INSERT INTO runs_token_counts
(
    id, tenant_id, session_id, is_root, source_id, start_time,
    prompt_tokens, completion_tokens, total_tokens, first_token_time,
    prompt_cost, completion_cost, total_cost,
    prompt_token_details, completion_token_details, prompt_cost_details, completion_cost_details,
    modified_at, is_deleted,
    trace_tier, trace_ttl_seconds, trace_first_received_at
)
SELECT
    r.id,
    r.tenant_id,
    r.session_id,
    r.is_root,
    r.id as source_id,
    toStartOfMinute(r.start_time) as start_time,
    r.prompt_tokens,
    r.completion_tokens,
    r.total_tokens,
    r.first_token_time,
    r.prompt_cost,
    r.completion_cost,
    r.total_cost,
    r.prompt_token_details,
    r.completion_token_details,
    r.prompt_cost_details,
    r.completion_cost_details,
    r.modified_at,
    r.is_deleted,
    r.trace_tier,
    r.trace_ttl_seconds,
    r.trace_first_received_at
FROM runs r
WHERE
    r.modified_at >= '2024-01-01 00:00:00.000000'  -- Adjust start time
    AND r.modified_at < '2024-12-31 23:59:59.999999'  -- Adjust end time
    AND r.is_deleted = 0
    AND r.run_type = 'llm'
SETTINGS
  parts_to_throw_insert = 10000,
  parts_to_delay_insert = 5000,
  max_insert_block_size = 1000000000,
  min_insert_block_size_rows = 2000000,
  min_insert_block_size_bytes = 1000000000,
  max_threads = 16,
  max_insert_threads = 2,
  send_progress_in_http_headers = 1;
